# ASP.NET Core
# Build and test ASP.NET Core projects targeting .NET Core.
# Add steps that run tests, create a NuGet package, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/dotnet-core
resources:
  repositories:
    - repository: QualityChecksRepo
      type: git
      name: Back-End/Hubtel.CodeQuality.Pipelines
      ref: master

trigger:
  - master
  - demo
  - staging

schedules:
  - cron: "0 0 * * *"
    displayName: DailyMidnight
    branches:
      include:
      - master
      - staging
      - demo

variables:
  - group: HubtelDockerBuildVariables
  - name: buildConfiguration
    value: 'Release'

extends:
  template: backend-repos/main-staged.yml@QualityChecksRepo
  parameters:
    ProjectsToBeBuild: '**/*.Api.csproj'
    SonarQubeProjectKey: "Back-End_Hubtel.Authentication_AYv8L3Bdo5AhFR4ORm2h"
    ProjectName : "Hubtel Authentication"
    SonarQubeServiceConnection: "Hubtel_SonarQube_Azure"
    BranchToCreateArtifacts: "master,demo"