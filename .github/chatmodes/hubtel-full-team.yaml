team:
  id: hubtel-full-team
  name: Hubtel Full Development Team
  version: 1.0
  description: Complete Hubtel development team with task creation, frontend, backend, and integration coordination capabilities
  
  # Team composition
  agents:
    - hubtel-project-onboarder.md
    - hubtel-task-creator.md
    - hubtel-task-processor.md
    - hubtel-frontend-dev.md
    - hubtel-backend-dev.md
    - hubtel-integration-coordinator.md
  
  # Shared dependencies across all agents
  shared_dependencies:
    data:
      - hubtel-kb.md
    utils:
      - azure-devops-integration.md
    checklists:
      - hubtel-task-quality-checklist.md
  
  # Team workflows and coordination
  workflows:
    primary:
      - hubtel-development-workflow.yaml
    
    specialized:
      - azure-import-workflow.yaml
      - task-description-workflow.yaml
      - idea-to-implementation-workflow.yaml
  
  # Entry points configuration
  entry_points:
    project_onboarding:
      description: "Comprehensive project onboarding for engineers new to any codebase"
      primary_agent: "hubtel-project-onboarder"
      workflow: "project-onboarding-workflow"
      rapid_productivity: true
    
    azure_import:
      description: "Import and enhance existing Azure DevOps tasks"
      primary_agent: "hubtel-task-creator"
      workflow: "azure-import-workflow"
      supports_batch: true
    
    task_description:
      description: "Process developer-provided task descriptions"
      primary_agent: "hubtel-task-processor"
      workflow: "task-description-workflow"
      creates_azure_tasks: true
    
    planning_phase:
      description: "Full CQT planning workflow with Hubtel context"
      primary_agent: "hubtel-task-creator"
      includes_core_agents: true
      workflow: "hubtel-development-workflow"
    
    idea_to_tasks:
      description: "Convert ideas to Azure DevOps work items"
      primary_agent: "hubtel-task-creator"
      workflow: "idea-to-implementation-workflow"
      auto_create_azure: true
  
  # Coordination patterns
  coordination:
    frontend_backend:
      coordinator: "hubtel-integration-coordinator"
      triggers:
        - api_changes
        - environment_updates
        - deployment_coordination
    
    task_enhancement:
      processor: "hubtel-task-processor"
      quality_gate: "hubtel-task-quality-checklist"
      junior_engineer_readiness: true
    
    azure_integration:
      manager: "hubtel-task-creator"
      batch_operations: true
      relationship_management: true
  
  # Team capabilities
  capabilities:
    technologies:
      frontend:
        - Next.js
        - Nuxt.js
        - React
        - Vue.js
        - TypeScript
      backend:
        - .NET Core
        - Entity Framework Core
        - PostgreSQL
        - MongoDB
      testing:
        - Vitest
        - Playwright
        - Karate
        - Mutation Testing
      observability:
        - OpenTelemetry
      integration:
        - Azure DevOps
        - Docker Compose
        - Teams notifications
    
    development_patterns:
      - RESTful API design
      - Microservices architecture
      - Clean architecture
      - SOLID principles
      - Contract-first development
      - Test-driven development
    
    quality_standards:
      - Junior engineer readiness
      - 1-hour task sizing
      - Comprehensive testing
      - Code review preparation
      - Hubtel coding standards
  
  # Usage instructions
  usage:
    startup: |
      Choose your entry point based on your current situation:
      
      0. **Project Onboarding**: If you're new to a project or codebase
         - Use: @hubtel-project-onboarder *analyze
         - Provides comprehensive project understanding and rapid productivity
         - Essential for engineers jumping between projects
      
      1. **Import from Azure DevOps**: If you have existing Azure tasks
         - Use: @hubtel-task-creator *import-azure {task-ids}
         - Supports batch processing of multiple tasks
      
      2. **Process Task Descriptions**: If you have task descriptions to structure
         - Use: @hubtel-task-processor *process-description {description}
         - Creates structured, implementable tasks
      
      3. **Create from Ideas**: If you have business ideas to implement
         - Use: @hubtel-task-creator *create-from-idea {idea}
         - Automatically creates Azure DevOps work item hierarchy
      
      4. **Full Planning**: If you need comprehensive planning
         - Use standard CQT planning agents first
         - Then use @hubtel-task-creator to convert to Azure tasks
    
    development_flow: |
      After task creation/enhancement:
      
      1. **Frontend Development**:
         - Use: @hubtel-frontend-dev *implement {task-id}
         - Handles Next.js/Nuxt.js implementation with full testing
      
      2. **Backend Development**:
         - Use: @hubtel-backend-dev *implement {task-id}
         - Handles .NET Core API implementation with comprehensive testing
      
      3. **Integration Coordination**:
         - Use: @hubtel-integration-coordinator when API changes occur
         - Handles Docker updates and cross-team communication
    
    quality_assurance: |
      All implementations include:
      - Comprehensive testing (unit + E2E)
      - Code review preparation
      - Azure DevOps integration
      - Hubtel coding standards compliance
      - Junior engineer implementation readiness
  
  # Team configuration
  configuration:
    azure_devops:
      organization: "hubtel"
      authentication: ["pat", "service_principal"]
      batch_size: 20
      rate_limiting: true
    
    task_standards:
      sizing: "1_hour_maximum"
      acceptance_criteria: "comprehensive"
      testing_requirements: "unit_and_e2e"
      documentation_links: true
    
    coordination:
      notification_channels: ["teams", "swagger", "postman"]
      environment_management: "docker_compose"
      api_documentation: "openapi_swagger"
    
    quality_gates:
      junior_engineer_test: true
      code_review_preparation: true
      testing_coverage: "85_percent_minimum"
      hubtel_standards_compliance: true