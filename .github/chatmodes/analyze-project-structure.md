# Analyze Project Structure

## ⚠️ CRITICAL EXECUTION NOTICE ⚠️

**THIS IS AN EXECUTABLE WORKFLOW - NOT REFERENCE MATERIAL**

When this task is invoked:
1. **COMPREHENSIVE ANALYSIS** - Deep dive into project structure, architecture, and patterns
2. **CONTEXT AWARENESS** - Understand business purpose and technical decisions
3. **ACTIONABLE INSIGHTS** - Provide practical knowledge for immediate productivity
4. **CONFIDENCE BUILDING** - Structure information to build engineer confidence

## Overview

This workflow performs comprehensive project analysis to enable rapid engineer onboarding and productivity. It analyzes architecture, patterns, dependencies, and provides actionable insights for immediate contribution.

## Analysis Framework

### Phase 1: Project Discovery

```yaml
step: discover_project_context
description: Understand project purpose, scope, and business context
activities:
  - identify_project_type:
    - frontend_app: "React/Next.js/Nuxt.js user interface"
    - backend_api: ".NET Core/Node.js API service"
    - full_stack: "Complete application with UI and API"
    - microservice: "Specialized service in larger ecosystem"
    - library: "Shared code package or utility"
  
  - extract_business_purpose:
    - analyze_readme: "Extract project description and goals"
    - identify_user_personas: "Who uses this system and how"
    - understand_value_proposition: "What problem does this solve"
    - map_business_flows: "Critical user journeys and processes"
  
  - assess_project_maturity:
    - development_stage: "prototype | mvp | production | mature"
    - team_size: "Number of active contributors"
    - change_velocity: "Frequency and size of changes"
    - stability_level: "How stable is the codebase"
```

### Phase 2: Technical Architecture Analysis

```yaml
step: analyze_technical_architecture
description: Deep dive into system design, patterns, and technology choices
activities:
  - technology_stack_analysis:
    - primary_frameworks: "Main development frameworks and versions"
    - language_patterns: "Programming language usage and conventions"
    - database_technologies: "Data storage and persistence strategies"
    - external_dependencies: "Third-party libraries and services"
  
  - architectural_pattern_identification:
    - design_patterns: "MVC, Repository, Factory, Observer, etc."
    - architectural_style: "Layered, Clean Architecture, Microservices"
    - data_flow_patterns: "How data moves through the system"
    - error_handling_patterns: "How errors are managed and communicated"
  
  - system_boundaries_mapping:
    - internal_components: "What code is maintained by this project"
    - external_integrations: "APIs, databases, and services consumed"
    - data_inputs_outputs: "What data enters and leaves the system"
    - security_boundaries: "Authentication and authorization points"
```

### Phase 3: Code Organization Analysis

```yaml
step: analyze_code_organization
description: Understand how code is structured and organized for maintainability
activities:
  - directory_structure_analysis:
    - folder_hierarchy: "How directories are organized and why"
    - naming_conventions: "File and folder naming patterns"
    - separation_of_concerns: "How different responsibilities are separated"
    - shared_vs_specific: "What code is reusable vs feature-specific"
  
  - component_relationship_mapping:
    - dependency_graph: "How components depend on each other"
    - abstraction_layers: "Levels of abstraction and their purposes"
    - interface_definitions: "How components communicate"
    - coupling_analysis: "How tightly components are connected"
  
  - configuration_management:
    - environment_configuration: "How different environments are configured"
    - feature_flags: "Runtime configuration and feature toggles"
    - secrets_management: "How sensitive data is handled"
    - build_configuration: "How the project is built and packaged"
```

### Phase 4: Development Workflow Analysis

```yaml
step: analyze_development_workflow
description: Understand how engineers work with this codebase effectively
activities:
  - local_development_setup:
    - environment_requirements: "Prerequisites for development"
    - setup_complexity: "How difficult is it to get started"
    - common_setup_issues: "Known problems and their solutions"
    - verification_steps: "How to confirm setup is working"
  
  - testing_strategy_analysis:
    - test_types: "Unit, integration, E2E testing approaches"
    - test_coverage: "How comprehensive is the test suite"
    - test_execution: "How to run tests locally and in CI"
    - test_patterns: "Common testing patterns and utilities"
  
  - deployment_pipeline_analysis:
    - build_process: "How code is compiled and packaged"
    - deployment_stages: "Development → Staging → Production flow"
    - rollback_procedures: "How to undo problematic deployments"
    - monitoring_integration: "How deployments are monitored"
```

### Phase 5: Change Impact Analysis

```yaml
step: analyze_change_patterns
description: Understand how different types of changes affect the system
activities:
  - common_change_scenarios:
    - feature_additions: "How new features are typically added"
    - bug_fixes: "Common bug patterns and resolution approaches"
    - performance_optimizations: "How performance issues are addressed"
    - security_updates: "How security concerns are handled"
  
  - risk_assessment_mapping:
    - high_risk_areas: "Code that requires extra caution when changing"
    - safe_change_zones: "Areas where changes have minimal impact"
    - testing_requirements: "What level of testing different changes need"
    - review_requirements: "When additional code review is needed"
  
  - change_propagation_analysis:
    - upstream_dependencies: "How changes in dependencies affect this project"
    - downstream_impacts: "How changes in this project affect other systems"
    - cross_cutting_concerns: "Changes that affect multiple parts of system"
    - data_migration_needs: "When changes require data updates"
```

## Analysis Output Structure

### Executive Summary
```yaml
project_analysis_summary:
  project_identity:
    name: "{project_name}"
    type: "{frontend|backend|fullstack|microservice|library}"
    primary_purpose: "{business_purpose_in_one_sentence}"
    user_base: "{who_uses_this_and_how}"
  
  technical_overview:
    technology_stack: "{primary_technologies}"
    architecture_style: "{architectural_approach}"
    complexity_level: "{low|medium|high}"
    maturity_stage: "{prototype|mvp|production|mature}"
  
  engineer_readiness:
    onboarding_difficulty: "{easy|moderate|challenging}"
    time_to_productivity: "{hours_or_days}"
    mentorship_needed: "{none|light|moderate|heavy}"
    confidence_builders: ["{list_of_easy_first_tasks}"]
```

### Detailed Technical Analysis
```yaml
technical_deep_dive:
  architecture_analysis:
    design_patterns: ["{pattern_list_with_explanations}"]
    data_flow: "{how_data_moves_through_system}"
    key_abstractions: ["{main_classes_and_their_roles}"]
    external_integrations: ["{apis_databases_services}"]
  
  code_organization:
    directory_structure: "{organized_by_feature|layer|type}"
    naming_conventions: "{consistent|inconsistent|mixed}"
    separation_of_concerns: "{clear|moderate|unclear}"
    code_quality_indicators: "{metrics_and_observations}"
  
  development_experience:
    setup_complexity: "{simple|moderate|complex}"
    development_tools: ["{required_tools_and_versions}"]
    testing_maturity: "{comprehensive|adequate|limited}"
    documentation_quality: "{excellent|good|limited|poor}"
```

### Practical Guidance
```yaml
practical_onboarding_guide:
  immediate_next_steps:
    - step: "Environment setup with specific commands"
    - step: "Run project locally and verify functionality"
    - step: "Execute test suite and understand coverage"
    - step: "Make small, safe change to build confidence"
  
  learning_path:
    phase_1: "Understand core business logic and data flow"
    phase_2: "Learn testing patterns and debugging approaches"
    phase_3: "Master deployment and monitoring processes"
    phase_4: "Contribute to complex features and architecture decisions"
  
  quick_wins:
    - task: "{easy_task_that_builds_familiarity}"
      complexity: "low"
      learning_value: "high"
      risk_level: "minimal"
    
  watch_out_areas:
    - area: "{complex_or_risky_code_area}"
      why_risky: "{explanation_of_risk}"
      mitigation: "{how_to_approach_safely}"
```

## Confidence Building Framework

### Understanding Validation
```yaml
comprehension_checkpoints:
  architecture_understanding:
    question: "Can you explain how data flows from user input to database?"
    validation: "Look for understanding of request/response cycle"
  
  change_capability:
    question: "How would you add a new API endpoint?"
    validation: "Should mention routing, validation, business logic, testing"
  
  debugging_readiness:
    question: "Where would you start investigating a performance issue?"
    validation: "Should mention logging, monitoring, profiling tools"
  
  deployment_confidence:
    question: "How do you know if a deployment was successful?"
    validation: "Should mention monitoring, health checks, rollback procedures"
```

### Mentorship Recommendations
```yaml
mentorship_strategy:
  pairing_opportunities:
    - scenario: "First complex feature implementation"
      mentor_focus: "Design decisions and testing strategy"
    
    - scenario: "Production issue investigation"
      mentor_focus: "Debugging techniques and system knowledge"
    
    - scenario: "Performance optimization"
      mentor_focus: "Profiling tools and optimization strategies"
  
  independence_indicators:
    - indicator: "Can implement features without design guidance"
    - indicator: "Can debug issues using logs and monitoring"
    - indicator: "Can assess change impact and risk appropriately"
    - indicator: "Can contribute to architectural discussions"
```

## Integration with Development Workflow

### Task Identification
```yaml
task_readiness_assessment:
  immediate_capability:
    - "Bug fixes with clear reproduction steps"
    - "Feature additions following established patterns"
    - "Test additions for existing functionality"
    - "Documentation improvements and updates"
  
  near_term_capability:
    - "Performance optimizations with guidance"
    - "New feature design with architectural input"
    - "Complex bug investigation and resolution"
    - "Integration with new external services"
  
  future_capability:
    - "System architecture changes"
    - "Major refactoring initiatives"
    - "Cross-system integration design"
    - "Performance architecture decisions"
```

This analysis framework ensures engineers gain comprehensive, practical understanding that enables immediate productivity and builds long-term confidence in any Hubtel project.