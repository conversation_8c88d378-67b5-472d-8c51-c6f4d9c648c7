# Hubtel CQT Workflow Expansion Pack

## Overview

The Hubtel CQT Workflow Expansion Pack provides specialized AI agents designed for Hubtel's development workflow, featuring Azure DevOps integration, Next.js/Nuxt.js frontend development, .NET Core backend development, and comprehensive team coordination.

## Key Features

- **Azure DevOps Integration**: Complete work item management, batch processing, and status synchronization
- **Multi-Entry Point Support**: Import Azure tasks, process descriptions, full planning, or idea-to-implementation
- **Technology Stack Optimization**: Specialized for Next.js/Nuxt.js frontend and .NET Core backend
- **Junior Engineer Ready**: All tasks enhanced for 1-hour implementation windows
- **Comprehensive Testing**: Vitest/Playwright frontend testing, Karate backend testing
- **Quality Assurance**: Built-in checklists and review preparation
- **Team Coordination**: Frontend/backend integration and Docker environment management

## Agents

### 🎓 Hubtel Project Onboarder (`hubtel-project-onboarder`)
**Essential first step for engineers new to any project or jumping between codebases**

- Comprehensive project analysis and architecture explanation
- Rapid onboarding with immediate productivity focus
- Confidence building through structured learning approach
- Hands-on guidance for common change scenarios
- Debugging and troubleshooting skill development

**Key Commands:**
- `*analyze` - Complete project analysis with executive summary
- `*architecture` - Deep dive into system design and patterns
- `*codebase-tour` - Interactive walkthrough of key files
- `*change-scenarios` - Step-by-step guides for common tasks
- `*quick-wins` - Identify easy first tasks to build confidence
- `*confidence-check` - Validate project understanding

### 🎯 Hubtel Task Creator (`hubtel-task-creator`)
**Primary entry point agent for task creation and Azure DevOps integration**

- Import and enhance existing Azure DevOps tasks
- Convert business ideas to complete task hierarchies
- Create structured work items from descriptions
- Batch process multiple tasks simultaneously
- Generate comprehensive acceptance criteria

**Key Commands:**
- `*import-azure {task-ids}` - Import and enhance Azure tasks
- `*create-from-idea {idea}` - Convert business idea to Azure work items
- `*process-description {description}` - Structure task descriptions
- `*enhance-batch {task-ids}` - Batch enhance multiple tasks

### 💻 Hubtel Frontend Developer (`hubtel-frontend-dev`)
**Next.js/Nuxt.js implementation specialist**

- Implement responsive components with mobile-first design
- Process HTML artifacts from UX team
- Integrate APIs with comprehensive error handling
- Create Vitest unit tests and Playwright E2E tests
- Ensure WCAG AA accessibility compliance

**Key Commands:**
- `*implement {task-id}` - Implement frontend task
- `*process-html {html-file}` - Convert HTML artifacts to components
- `*integrate-api {api-spec}` - Implement API integration
- `*test {component}` - Create comprehensive test suite

### ⚙️ Hubtel Backend Developer (`hubtel-backend-dev`)
**·NET Core API and service implementation specialist**

- Build RESTful APIs with OpenAPI documentation
- Implement Entity Framework Core with PostgreSQL/MongoDB
- Add OpenTelemetry logging and monitoring
- Create Karate API tests and comprehensive unit tests
- Ensure security and performance standards

**Key Commands:**
- `*implement {task-id}` - Implement backend task
- `*create-api {specification}` - Create API endpoints
- `*create-migration {schema}` - Create database migration
- `*add-logging {component}` - Add OpenTelemetry logging

### 🔄 Hubtel Integration Coordinator (`hubtel-integration-coordinator`)
**Frontend/backend coordination and environment management**

- Coordinate API changes between teams
- Manage Docker Compose environments
- Update Swagger documentation and Postman collections
- Handle Teams notifications for breaking changes
- Resolve integration conflicts

**Key Commands:**
- `*coordinate-api {api-change}` - Coordinate API changes
- `*update-docker {services}` - Update Docker environment
- `*sync-environments` - Synchronize configurations
- `*notify-teams {message}` - Send team notifications

### 🧪 Hubtel Test Engineer (`hubtel-test-engineer`)
**Comprehensive testing specialist and quality assurance expert**

- Analyze project testing setup and framework configuration
- Create comprehensive test cases covering all scenarios
- Write high-quality unit tests with proper mocking
- Implement integration tests for API endpoints
- Generate detailed testing reports with coverage analysis
- Ensure accessibility testing compliance (WCAG AA)

**Key Commands:**
- `*analyze-setup` - Analyze current testing configuration
- `*create-test-plan {task-id}` - Create comprehensive test plan
- `*write-unit-tests {component-path}` - Generate unit tests
- `*write-integration-tests {api-spec}` - Create API integration tests
- `*create-e2e-tests {user-story}` - Generate end-to-end tests
- `*generate-report` - Create detailed testing report
- `*test-accessibility {component}` - Create accessibility tests

### 📋 Hubtel Task Processor (`hubtel-task-processor`)
**Task enhancement and standardization specialist**

- Transform vague requirements into specific tasks
- Generate comprehensive acceptance criteria
- Add technical context and implementation guidance
- Ensure junior engineer readiness
- Validate 1-hour task sizing

**Key Commands:**
- `*enhance {task-id}` - Enhance existing task
- `*process-description {description}` - Convert description to structured task
- `*validate-requirements {task-id}` - Validate task quality
- `*coordinate-dependencies {task-ids}` - Analyze dependencies

## Entry Points

### Entry Point 0: Project Onboarding
**Essential for engineers new to a project or jumping between codebases**

```bash
# Comprehensive project analysis and onboarding
@hubtel-project-onboarder *analyze

# Specific deep dives
@hubtel-project-onboarder *architecture
@hubtel-project-onboarder *codebase-tour
@hubtel-project-onboarder *change-scenarios
```

**Process:**
1. Analyzes current project structure and technology stack
2. Explains business purpose and system architecture
3. Provides hands-on walkthroughs of critical code paths
4. Identifies quick wins for building confidence
5. Validates understanding through practical scenarios
6. Creates personalized learning path for continued growth

**Perfect for:**
- Engineers joining new teams or projects
- Switching between frontend/backend codebases
- Understanding legacy systems or complex architectures
- Building confidence before making significant changes
- Mentoring junior developers on project structure

### Entry Point A: Azure DevOps Import
**For existing Azure DevOps tasks that need implementation**

```bash
# Import single task
@hubtel-task-creator *import-azure AZ-123

# Import multiple tasks
@hubtel-task-creator *import-azure AZ-123,AZ-124,AZ-125
```

**Process:**
1. Connects to Azure DevOps and retrieves work items
2. Analyzes tasks within current project context and codebase
3. Identifies related files, patterns, and dependencies
4. Maps technical complexity and implementation approach
5. Routes to appropriate development agents with project context

### Entry Point B: Task Description Processing
**For free-form task descriptions that need structuring**

```bash
@hubtel-task-processor *process-description "Create a responsive user dashboard with real-time notifications"
```

**Process:**
1. Analyzes task description and extracts requirements
2. Adds technical context and implementation guidance
3. Generates comprehensive acceptance criteria
4. Optionally creates Azure DevOps work items
5. Ensures junior engineer readiness

### Entry Point C: Planning Phase Integration
**For comprehensive requirement gathering and planning**

```bash
# Use standard CQT planning agents first
@analyst *brainstorm "Customer support ticket system"
@pm *create-prd
@architect *create-architecture

# Then convert to Hubtel tasks
@hubtel-task-creator *create-epic "Support ticket system with all planning artifacts"
```

**Process:**
1. Complete CQT planning workflow (Analyst → PM → Architect)
2. Convert planning artifacts to Azure DevOps hierarchy
3. Break down into 1-hour implementable tasks
4. Apply Hubtel technical context and standards
5. Create complete Epic → Feature → Story → Task structure

### Entry Point D: Idea to Implementation
**For converting business ideas directly to implementable tasks**

```bash
@hubtel-task-creator *create-from-idea "Allow customers to upload documents for loan applications with automated validation"
```

**Process:**
1. Analyzes business idea and extracts requirements
2. Automatically creates Azure DevOps epic/feature/story hierarchy
3. Generates detailed tasks with 1-hour sizing
4. Adds comprehensive technical context and testing requirements
5. Links work items with proper dependencies

## Technology Stack Support

### Frontend Technologies
- **Frameworks**: Next.js, Nuxt.js, React, Vue.js
- **Testing**: Vitest (unit), Playwright (E2E)
- **Styling**: Tailwind CSS, CSS Modules, styled-components
- **Accessibility**: WCAG AA compliance validation

### Backend Technologies
- **Framework**: .NET Core Web API
- **ORM**: Entity Framework Core
- **Databases**: PostgreSQL, MongoDB
- **Testing**: Karate (API), NUnit (unit), Mutation testing
- **Observability**: OpenTelemetry logging and tracing

### DevOps & Integration
- **Containerization**: Docker Compose
- **API Documentation**: OpenAPI/Swagger
- **Project Management**: Azure DevOps work items
- **Communication**: Teams notifications
- **Version Control**: Git with Azure Repos

## Quality Standards

### Task Quality
- **1-Hour Sizing**: All tasks scoped for 1-hour implementation
- **Junior Engineer Ready**: Clear guidance for developers at all levels
- **Comprehensive Testing**: Unit and E2E testing requirements included
- **Acceptance Criteria**: Detailed, testable completion conditions

### Code Quality
- **Hubtel Standards**: Adherence to internal coding guidelines
- **Code Review**: All implementations prepared for review
- **Testing Coverage**: Minimum 85% unit test coverage
- **Performance**: Sub-200ms API response times
- **Security**: Input validation, authentication, authorization

### Integration Quality
- **API Contracts**: Contract-first development approach
- **Environment Parity**: Consistent Docker configurations
- **Documentation**: Comprehensive API and code documentation
- **Observability**: OpenTelemetry logging throughout

## Usage Examples

### Complete Development Workflow

```bash
# 1. Import Azure tasks
@hubtel-task-creator *import-azure AZ-100,AZ-101,AZ-102

# 2. Implement frontend task
@hubtel-frontend-dev *implement AZ-100

# 3. Implement backend task
@hubtel-backend-dev *implement AZ-101

# 4. Coordinate integration
@hubtel-integration-coordinator *coordinate-api "User authentication API endpoint changed"

# 5. Review and commit
# (Developer reviews implementation and approves)
# Agent commits with proper task ID and updates Azure DevOps
```

### Idea to Deployment

```bash
# 1. Convert idea to tasks
@hubtel-task-creator *create-from-idea "Real-time notification system for mobile app"

# 2. Development automatically routes to appropriate agents
# 3. Integration coordination handled automatically
# 4. Quality gates ensure review readiness
# 5. Azure DevOps automatically updated with progress
```

## Installation

### Web UI Usage (ChatGPT, Claude, Gemini)
1. Navigate to `dist/expansion-packs/hubtel-workflow/teams/`
2. Copy `hubtel-full-team.txt` content
3. Create new custom GPT or upload to Claude/Gemini
4. Add instruction: "Follow the configuration exactly as specified in this bundle"

### IDE Integration (Cursor, Claude Code, etc.)
```bash
# Install CQT-Agent with Hubtel expansion
npx cqt-agent install --expansion-packs hubtel-workflow

# Or install to existing CQT setup
npx cqt-agent install --expansion-only --expansion-packs hubtel-workflow
```

## Configuration

### Azure DevOps Setup
```yaml
azure_devops:
  organization: "your-org"
  project: "your-project"
  pat: "${AZURE_PAT}"  # Personal Access Token
  # or
  service_principal:
    client_id: "${AZURE_CLIENT_ID}"
    client_secret: "${AZURE_CLIENT_SECRET}"
    tenant_id: "${AZURE_TENANT_ID}"
```

### Environment Variables
```bash
AZURE_PAT=your-personal-access-token
HUBTEL_DEV_DOCS_URL=https://dev-docs.hubtel.com/introduction.html
TEAMS_WEBHOOK_URL=your-teams-webhook-url
```

## Support and Documentation

- **Hubtel Documentation**: https://dev-docs.hubtel.com/introduction.html
- **CQT-Agent Framework**: https://github.com/bmadcode/CQT-AGENT
- **Issue Reporting**: Create issues in the main CQT-Agent repository
- **Feature Requests**: Submit via GitHub issues with "hubtel-expansion" label

## Version History

- **v1.0.0**: Initial release with complete Azure DevOps integration and Hubtel workflow support

---

**🤖 Generated with CQT-Agent Framework**
**Co-Authored-By: Hubtel Development Team**