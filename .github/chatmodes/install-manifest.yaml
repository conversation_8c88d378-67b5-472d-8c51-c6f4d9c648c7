version: 0.0.9
installed_at: '2025-08-13T12:02:13.707Z'
install_type: full
agent: null
ides_setup:
  - cursor
  - gemini
  - github-copilot
expansion_packs: []
files:
  - path: .cqt-core\working-in-the-brownfield.md
    hash: ddcd2276f9a68dc6
    modified: false
  - path: .cqt-core\user-guide.md
    hash: cc1af38bf87695b2
    modified: false
  - path: .cqt-core\README.md
    hash: '11926e8424572594'
    modified: false
  - path: .cqt-core\enhanced-ide-development-workflow.md
    hash: 7aa8ed80be19e93e
    modified: false
  - path: .cqt-core\config.yaml
    hash: bcef64c49fd17ef2
    modified: false
  - path: .cqt-core\workflows\hubtel-development-workflow.yaml
    hash: cf60e4d088a253ad
    modified: false
  - path: .cqt-core\utils\workflow-management.md
    hash: 313b48caf8ac4501
    modified: false
  - path: .cqt-core\utils\task-pre-cache-system.md
    hash: 78489593921e06d7
    modified: false
  - path: .cqt-core\utils\bmad-doc-template.md
    hash: b488b5af3e642ddc
    modified: false
  - path: .cqt-core\utils\azure-devops-integration.md
    hash: 654f90b204faf0be
    modified: false
  - path: .cqt-core\utils\alternative-retrieval-methods.md
    hash: fc775dcc312c2d98
    modified: false
  - path: .cqt-core\templates\vitest-unit-test-tmpl.ts
    hash: df3f30b075475dca
    modified: false
  - path: .cqt-core\templates\playwright-e2e-test-tmpl.ts
    hash: 3d80d972d20caa5c
    modified: false
  - path: .cqt-core\templates\karate-api-test-tmpl.feature
    hash: 92d13981a657d221
    modified: false
  - path: .cqt-core\templates\azure-task-tmpl.yaml
    hash: ff1bcc53fcb09c22
    modified: false
  - path: .cqt-core\checklists\project-understanding-checklist.md
    hash: d6e42c0bb81eb895
    modified: false
  - path: .cqt-core\checklists\hubtel-task-quality-checklist.md
    hash: eee2cfa77516126a
    modified: false
  - path: .cqt-core\checklists\frontend-implementation-checklist.md
    hash: 58cff6795514f263
    modified: false
  - path: .cqt-core\checklists\backend-implementation-checklist.md
    hash: b11a7c913cc37ad8
    modified: false
  - path: .cqt-core\data\hubtel-kb.md
    hash: db86206b258248a6
    modified: false
  - path: .cqt-core\agent-teams\hubtel-full-team.yaml
    hash: f640c065cb05de4e
    modified: false
  - path: .cqt-core\agents\hubtel-test-engineer.md
    hash: 7751a02495391c80
    modified: false
  - path: .cqt-core\agents\hubtel-task-processor.md
    hash: 382029e9fe2e16f5
    modified: false
  - path: .cqt-core\agents\hubtel-task-creator.md
    hash: ab985d509bf5ddf6
    modified: false
  - path: .cqt-core\agents\hubtel-project-onboarder.md
    hash: b69d556050f5dc4a
    modified: false
  - path: .cqt-core\agents\hubtel-integration-coordinator.md
    hash: 51d97675ac9e0f03
    modified: false
  - path: .cqt-core\agents\hubtel-frontend-dev.md
    hash: 4fff1e8ec353d8a8
    modified: false
  - path: .cqt-core\agents\hubtel-backend-dev.md
    hash: 22076beaeabd23e6
    modified: false
  - path: .cqt-core\tasks\unit-test-generator.md
    hash: 5f4f115e5369e465
    modified: false
  - path: .cqt-core\tasks\test-report-generator.md
    hash: 3490a6814c441a0a
    modified: false
  - path: .cqt-core\tasks\test-project-analysis.md
    hash: f0d8553a8b383d93
    modified: false
  - path: .cqt-core\tasks\hubtel-task-enhancer.md
    hash: 1b5ffe946b3b8d7f
    modified: false
  - path: .cqt-core\tasks\execute-checklist.md
    hash: 4ff0f6c69b5787dd
    modified: false
  - path: .cqt-core\tasks\create-onboarding-guide.md
    hash: 6802d6b0b5290952
    modified: false
  - path: .cqt-core\tasks\create-doc.md
    hash: b8657c9eb66deff0
    modified: false
  - path: .cqt-core\tasks\coordinate-integration.md
    hash: 43dca7cd2727f568
    modified: false
  - path: .cqt-core\tasks\azure-task-processor.md
    hash: 4580d38541d204aa
    modified: false
  - path: .cqt-core\tasks\analyze-project-structure.md
    hash: f59fcc274dad9d6f
    modified: false
