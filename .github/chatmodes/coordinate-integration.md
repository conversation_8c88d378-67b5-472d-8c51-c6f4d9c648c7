# Coordinate Integration

## ⚠️ CRITICAL EXECUTION NOTICE ⚠️

**THIS IS AN EXECUTABLE WORKFLOW - NOT REFERENCE MATERIAL**

When this task is invoked:
1. **CROSS-TEAM COORDINATION** - This workflow manages frontend/backend integration
2. **API CONTRACT MANAGEMENT** - Handles API changes and compatibility
3. **ENVIRONMENT SYNCHRONIZATION** - Updates Docker Compose and configurations
4. **COMMUNICATION ORCHESTRATION** - Coordinates Teams, Swagger, and Postman updates

## Overview

This workflow coordinates integration activities between frontend and backend development teams, managing API changes, environment updates, and cross-team communication to ensure smooth development workflows and prevent integration conflicts.

## Input Parameters

### Required Parameters
- **integration_type**: "api_change" | "environment_update" | "deployment_coordination" | "conflict_resolution"
- **affected_services**: Array of services requiring coordination

### Optional Parameters
- **notification_channels**: ["teams", "swagger", "postman"] (default: all)
- **urgency_level**: "low" | "normal" | "high" | "critical" (default: "normal")
- **rollback_plan**: boolean (default: true)
- **validation_required**: boolean (default: true)

## Coordination Workflows

### API Change Coordination

```yaml
workflow: api_change_coordination
description: Manage API changes between frontend and backend teams
phases:
  - phase: "change_detection"
    activities:
      - analyze_api_changes: Compare current vs new API specifications
      - identify_breaking_changes: Flag changes that affect existing contracts
      - assess_impact: Determine affected frontend components and services
      - categorize_urgency: Classify change urgency and timeline requirements
  
  - phase: "impact_analysis"
    activities:
      - frontend_impact_assessment:
        - identify_affected_components: Find components using changed APIs
        - estimate_modification_effort: Calculate required frontend changes
        - compatibility_analysis: Assess backward compatibility requirements
      
      - backend_impact_assessment:
        - service_dependency_analysis: Identify dependent backend services
        - database_migration_needs: Determine schema change requirements
        - performance_impact: Assess performance implications of changes
  
  - phase: "coordination_planning"
    activities:
      - development_sequencing:
        - backend_first: API implementation and testing completion
        - frontend_adaptation: Frontend updates based on new API contract
        - integration_testing: End-to-end validation of changes
      
      - rollback_strategy:
        - version_management: API versioning for backward compatibility
        - deployment_sequence: Safe rollback procedures if needed
        - data_migration_rollback: Database change reversal procedures
  
  - phase: "communication_execution"
    activities:
      - team_notifications:
        - breaking_change_alerts: Immediate notification for breaking changes
        - timeline_communication: Expected completion and deployment dates
        - testing_coordination: Shared testing plans and responsibilities
      
      - documentation_updates:
        - swagger_regeneration: Updated OpenAPI specifications
        - postman_collection_updates: Refreshed API testing collections
        - integration_examples: Updated code examples and patterns
```

### Environment Update Coordination

```yaml
workflow: environment_update_coordination
description: Synchronize Docker Compose and environment configurations
phases:
  - phase: "environment_analysis"
    activities:
      - service_inventory: Catalog all services in Docker Compose setup
      - dependency_mapping: Map service-to-service communication patterns
      - configuration_audit: Review environment variables and secrets
      - network_analysis: Assess service networking and port requirements
  
  - phase: "update_planning"
    activities:
      - change_categorization:
        - new_services: Services being added to the environment
        - configuration_updates: Environment variable and setting changes
        - network_modifications: Port mappings and service communication updates
        - volume_changes: Persistent storage and shared volume updates
      
      - impact_assessment:
        - service_disruption: Assess which services need restart or recreation
        - data_persistence: Ensure data safety during environment updates
        - development_workflow_impact: Minimize disruption to ongoing development
  
  - phase: "coordinated_deployment"
    activities:
      - staged_rollout:
        - backup_current_environment: Save current working configuration
        - incremental_updates: Apply changes in small, testable increments
        - validation_checkpoints: Verify functionality at each stage
      
      - team_coordination:
        - synchronized_updates: Coordinate team environment refresh timing
        - troubleshooting_support: Provide assistance for update issues
        - rollback_procedures: Quick recovery if updates cause problems
```

### Deployment Coordination

```yaml
workflow: deployment_coordination
description: Coordinate service deployment sequences and dependencies
phases:
  - phase: "deployment_planning"
    activities:
      - dependency_analysis:
        - service_dependencies: Map required deployment order
        - database_migrations: Coordinate schema changes with deployments
        - configuration_dependencies: Environment and secret updates
      
      - risk_assessment:
        - breaking_change_identification: Flag changes requiring careful sequencing
        - rollback_complexity: Assess rollback difficulty and requirements
        - business_impact: Evaluate user-facing impact of deployment
  
  - phase: "coordination_execution"
    activities:
      - deployment_sequencing:
        - backend_services_first: Deploy API changes before frontend updates
        - database_migrations: Execute schema changes during maintenance windows
        - frontend_deployment: Deploy UI changes after backend stabilization
      
      - monitoring_coordination:
        - health_check_validation: Verify service health after each deployment
        - integration_testing: Execute cross-service validation tests
        - performance_monitoring: Track system performance during rollout
  
  - phase: "post_deployment_coordination"
    activities:
      - validation_orchestration:
        - end_to_end_testing: Comprehensive system validation
        - user_acceptance_testing: Coordinate UAT with stakeholders
        - performance_verification: Validate system performance metrics
      
      - communication_closure:
        - success_notifications: Inform teams of successful deployment
        - documentation_updates: Update deployment procedures and lessons learned
        - incident_response_readiness: Prepare for potential post-deployment issues
```

## Communication Templates

### API Change Notification Template

```yaml
api_change_notification:
  urgency_indicators:
    critical: "🚨 CRITICAL API CHANGE 🚨"
    high: "⚠️ HIGH PRIORITY API CHANGE ⚠️"
    normal: "📋 API Change Notification"
    low: "ℹ️ Minor API Update"
  
  message_structure:
    header:
      service: "{affected_service_name}"
      change_type: "{breaking|non-breaking|enhancement}"
      timeline: "{implementation_timeline}"
    
    impact_summary:
      frontend_impact: "{specific_frontend_changes_required}"
      backend_impact: "{backend_service_modifications}"
      testing_impact: "{additional_testing_requirements}"
    
    implementation_plan:
      backend_completion: "{backend_implementation_date}"
      frontend_updates: "{frontend_modification_timeline}"
      integration_testing: "{testing_and_validation_period}"
    
    resources:
      updated_documentation: "{swagger_documentation_url}"
      postman_collection: "{updated_postman_collection_url}"
      code_examples: "{implementation_examples_and_patterns}"
    
    contact_information:
      backend_contact: "{backend_team_contact}"
      frontend_contact: "{frontend_team_contact}"
      integration_coordinator: "{coordinator_contact}"
```

### Environment Update Template

```yaml
environment_update_notification:
  update_categories:
    new_service: "🆕 NEW SERVICE ADDED"
    configuration_change: "⚙️ CONFIGURATION UPDATE"
    network_modification: "🌐 NETWORK CHANGES"
    volume_update: "💾 STORAGE CHANGES"
  
  message_structure:
    summary:
      changes: "{list_of_environment_changes}"
      impact: "{development_workflow_impact}"
      update_required: "{action_required_by_developers}"
    
    update_instructions:
      backup_current: "docker-compose down && cp docker-compose.yml docker-compose.yml.backup"
      get_updates: "git pull origin main"
      apply_changes: "docker-compose up -d"
      verify_services: "docker-compose ps"
    
    new_environment_variables:
      - variable: "{VARIABLE_NAME}"
        description: "{variable_purpose_and_usage}"
        default_value: "{default_or_example_value}"
    
    troubleshooting:
      common_issues: "{frequently_encountered_problems}"
      resolution_steps: "{step_by_step_problem_resolution}"
      escalation_contact: "{support_contact_information}"
```

## Integration Monitoring

### Health Check Coordination

```yaml
health_monitoring:
  service_health_checks:
    - service: "frontend_app"
      endpoint: "https://code-confidence-index.hubtel.com/health"
      expected_status: 200
      timeout_seconds: 5
    
    - service: "backend_api"
      endpoint: "http://localhost:5000/health"
      expected_status: 200
      timeout_seconds: 3
    
    - service: "database"
      connection: "postgresql://localhost:5432/hubtel"
      timeout_seconds: 2
  
  integration_validations:
    - validation: "frontend_to_backend_api"
      test: "Login flow end-to-end test"
      endpoint: "POST /api/auth/login"
      expected_result: "JWT token returned and user redirected"
    
    - validation: "backend_to_database"
      test: "User data persistence test"
      operation: "User creation and retrieval"
      expected_result: "Data correctly stored and retrieved"
```

### Conflict Resolution Procedures

```yaml
conflict_resolution:
  conflict_types:
    - type: "api_version_mismatch"
      detection: "Frontend expecting different API version than backend provides"
      resolution: "Coordinate API versioning strategy and update frontend"
    
    - type: "environment_configuration_mismatch"
      detection: "Services unable to communicate due to configuration differences"
      resolution: "Synchronize environment configurations and restart affected services"
    
    - type: "database_schema_conflict"
      detection: "Application code incompatible with database schema"
      resolution: "Coordinate migration sequencing and application updates"
  
  escalation_procedures:
    level_1: "Integration coordinator attempts automated resolution"
    level_2: "Involve frontend and backend team leads for manual coordination"
    level_3: "Escalate to technical architect for design decision"
    level_4: "Involve product owner for business impact assessment"
```

## Usage Examples

### API Change Coordination
```yaml
*coordinate-integration
integration_type: "api_change"
affected_services: ["user-service", "frontend-app"]
change_description: "User authentication endpoint changed from /auth to /api/v2/auth"
urgency_level: "high"
```

### Environment Update
```yaml
*coordinate-integration
integration_type: "environment_update"
affected_services: ["redis-cache", "frontend-app", "backend-api"]
change_description: "Add Redis service for session management"
notification_channels: ["teams", "docker-compose-update"]
```

### Deployment Coordination
```yaml
*coordinate-integration
integration_type: "deployment_coordination"
affected_services: ["backend-api", "frontend-app", "database"]
deployment_sequence: ["database-migration", "backend-api", "frontend-app"]
validation_required: true
```

This workflow ensures seamless coordination between development teams, preventing integration conflicts and maintaining development velocity through proactive communication and environment management.