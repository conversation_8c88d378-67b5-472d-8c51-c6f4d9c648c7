workflow:
  id: hubtel-development-workflow
  name: Hubtel Development Workflow
  description: >-
    Complete Hubtel development workflow supporting all entry points with Azure DevOps integration,
    frontend/backend coordination, and junior engineer optimization.
  type: hubtel-specific
  project_types:
    - web-application
    - api-service
    - full-stack
    - microservice
    - frontend-only
    - backend-only

  # Entry point sequences
  entry_point_workflows:
    
    # Entry Point A: Azure DevOps Import
    azure_import:
      sequence:
        - agent: hubtel-task-creator
          action: import_azure_tasks
          creates: project_contextualized_tasks
          requires: azure_task_ids
          notes: |
            Import existing Azure DevOps tasks and analyze within current project context.
            Supports batch processing of multiple tasks simultaneously.
            OUTPUT: Tasks understood within codebase context, ready for implementation.
        
        - agent: hubtel-task-processor
          action: analyze_project_context
          uses: project-context-analysis
          notes: |
            Analyze tasks within current project structure and identify related code.
            Map dependencies and understand implementation complexity.
        
        - coordination_point: route_to_development
          notes: Route to implementation agents with project context and related files
    
    # Entry Point B: Task Description Processing
    task_description:
      sequence:
        - agent: hubtel-task-processor
          action: process_descriptions
          creates: structured_tasks
          requires: task_descriptions
          notes: |
            Convert free-form task descriptions into structured, implementable work items.
            Add comprehensive acceptance criteria and technical context.
        
        - agent: hubtel-task-creator
          action: create_azure_tasks
          creates: azure_work_items
          condition: create_azure_tasks_enabled
          notes: |
            OPTIONAL: Create Azure DevOps work items from structured tasks.
            Include proper hierarchy and relationship management.
        
        - coordination_point: route_to_development
          notes: Proceed to implementation phase with structured tasks
    
    # Entry Point C: Planning Phase Integration
    planning_phase:
      sequence:
        - agent: analyst
          creates: project_brief
          optional_steps:
            - market_research
            - competitive_analysis
          notes: "Standard CQT planning phase - gather requirements and create project brief"
        
        - agent: pm
          creates: prd
          requires: project_brief
          notes: "Create comprehensive Product Requirements Document with Hubtel context"
        
        - agent: architect
          creates: system_architecture
          requires: prd
          notes: "Design system architecture using Hubtel technology stack preferences"
        
        - agent: hubtel-task-creator
          action: convert_planning_to_tasks
          creates: azure_epic_hierarchy
          requires: [prd, system_architecture]
          notes: |
            Convert planning artifacts into Azure DevOps epic/feature/story hierarchy.
            Break down into 1-hour implementable tasks with full Hubtel context.
        
        - coordination_point: route_to_development
          notes: Proceed to implementation with complete task hierarchy
    
    # Entry Point D: Idea to Implementation
    idea_to_implementation:
      sequence:
        - agent: hubtel-task-creator
          action: analyze_business_idea
          creates: requirements_analysis
          requires: business_idea
          notes: |
            Analyze business idea and extract functional/technical requirements.
            Apply Hubtel technology stack constraints and patterns.
        
        - agent: hubtel-task-creator
          action: generate_task_hierarchy
          creates: azure_work_item_hierarchy
          requires: requirements_analysis
          notes: |
            Generate complete Epic → Feature → Story → Task hierarchy in Azure DevOps.
            Automatically create work items with proper relationships and assignments.
        
        - agent: hubtel-task-processor
          action: enhance_generated_tasks
          uses: hubtel-task-enhancer
          notes: |
            Apply comprehensive enhancement to all generated tasks.
            Ensure junior engineer readiness and testing completeness.
        
        - coordination_point: route_to_development
          notes: Proceed to implementation with auto-created Azure task hierarchy

  # Development implementation phase (common to all entry points)
  implementation_phase:
    sequence:
      
      # Task assignment and coordination
      - coordination_point: analyze_task_dependencies
        agent: hubtel-integration-coordinator
        action: coordinate_dependencies
        notes: |
          Analyze task dependencies and coordination requirements.
          Plan frontend/backend development sequence and integration points.
      
      # Frontend implementation cycle
      - agent: hubtel-frontend-dev
        action: implement_frontend_tasks
        repeats: for_each_frontend_task
        creates: frontend_implementation
        requires: [azure_task, html_artifacts, api_specifications]
        notes: |
          Frontend Development Cycle:
          - Parse Azure task and acceptance criteria
          - Process HTML artifacts from UX team
          - Implement responsive Next.js/Nuxt.js components
          - Add comprehensive Vitest unit tests and Playwright E2E tests
          - Integrate with backend APIs with proper error handling
          - Prepare for code review (no direct commits)
      
      # Backend implementation cycle  
      - agent: hubtel-backend-dev
        action: implement_backend_tasks
        repeats: for_each_backend_task
        creates: backend_implementation
        requires: [azure_task, api_specifications, database_requirements]
        notes: |
          Backend Development Cycle:
          - Parse Azure task and technical requirements
          - Implement .NET Core API endpoints with proper validation
          - Add Entity Framework Core models and migrations
          - Include OpenTelemetry logging and monitoring
          - Create comprehensive Karate API tests and unit tests
          - Prepare for code review (no direct commits)
      
      # Integration coordination
      - agent: hubtel-integration-coordinator
        action: coordinate_integration_points
        condition: has_frontend_backend_dependencies
        creates: integration_coordination
        notes: |
          Integration Coordination Activities:
          - Manage API contract changes between teams
          - Update Docker Compose configurations
          - Coordinate environment variable synchronization
          - Send Teams notifications for breaking changes
          - Update Swagger documentation and Postman collections
      
      # Code review and validation cycle
      - coordination_point: developer_review_gate
        notes: |
          Developer Review Process:
          1. Present implementation summary with files changed
          2. Show test results and coverage metrics
          3. Display review checklist and quality metrics
          4. Wait for developer approval or feedback
          5. Handle revision requests and re-implementation
          6. Proceed to commit only after approval
      
      # Commit and Azure update cycle
      - coordination_point: commit_implementation
        notes: |
          Post-Review Commit Process:
          1. Generate commit message with proper task ID reference
          2. Execute git commit with CQT Agent co-authorship
          3. Optionally update Azure DevOps work item status (if configured)
          4. Trigger any configured CI/CD pipelines
      
      # Task completion validation
      - coordination_point: validate_task_completion
        uses: [frontend-implementation-checklist, backend-implementation-checklist]
        notes: |
          Final Validation:
          1. Verify all acceptance criteria met
          2. Confirm testing requirements satisfied
          3. Validate code review completion
          4. Check Azure DevOps status updates
          5. Ensure coordination requirements addressed

  # Workflow decision guidance
  decision_guidance:
    when_to_use_azure_import:
      - "Existing Azure DevOps tasks need enhancement for implementation"
      - "Tasks created by engineering managers need technical context"
      - "Batch processing of multiple related tasks required"
      - "Tasks need junior engineer readiness improvement"
    
    when_to_use_task_descriptions:
      - "Free-form requirements need structuring"
      - "Business requirements need technical translation"
      - "New Azure tasks need to be created from descriptions"
      - "Acceptance criteria need comprehensive development"
    
    when_to_use_planning_phase:
      - "Starting with high-level business ideas or requirements"
      - "Need comprehensive requirement gathering and architecture"
      - "Project requires full CQT planning methodology"
      - "Multiple stakeholders need alignment on requirements"
    
    when_to_use_idea_to_implementation:
      - "Converting business ideas directly to implementable tasks"
      - "Need rapid prototyping with proper task structure"
      - "Automatic Azure DevOps hierarchy creation required"
      - "Streamlined idea-to-code pipeline needed"

  # Coordination patterns
  coordination_patterns:
    
    frontend_backend_coordination:
      triggers:
        - api_specification_changes
        - database_schema_modifications
        - authentication_integration_updates
        - real_time_feature_implementation
      
      coordination_agent: hubtel-integration-coordinator
      
      activities:
        - api_contract_validation
        - docker_compose_updates
        - environment_synchronization
        - teams_notifications
        - swagger_documentation_updates
    
    azure_devops_integration:
      manager: hubtel-task-creator
      
      capabilities:
        - batch_work_item_operations
        - hierarchy_relationship_management
        - status_synchronization
        - commit_linking
        - time_tracking_integration
      
      quality_gates:
        - junior_engineer_readiness_validation
        - acceptance_criteria_completeness_check
        - testing_requirement_verification
        - hubtel_standards_compliance_audit
    
    quality_assurance:
      framework:
        - task_enhancement_validation
        - implementation_quality_checks
        - testing_coverage_verification
        - code_review_preparation
        - azure_integration_validation
      
      tools:
        - hubtel-task-quality-checklist
        - frontend-implementation-checklist
        - backend-implementation-checklist
        - azure-devops-integration-validation

  # Technology stack integration
  technology_integration:
    frontend:
      frameworks: [Next.js, Nuxt.js]
      testing: [Vitest, Playwright]
      patterns: [Component composition, API integration, State management]
      quality: [Responsive design, Accessibility, Performance optimization]
    
    backend:
      framework: .NET Core
      orm: Entity Framework Core
      databases: [PostgreSQL, MongoDB]
      testing: [Karate, NUnit, Mutation testing]
      patterns: [Clean architecture, Repository pattern, SOLID principles]
      observability: OpenTelemetry
    
    integration:
      containerization: Docker Compose
      api_documentation: OpenAPI/Swagger
      communication: Teams notifications
      version_control: Azure DevOps Git
      project_management: Azure DevOps Work Items

  # Flow diagram
  flow_diagram: |
    ```mermaid
    graph TD
        A[Choose Entry Point] --> B{Entry Point Type}
        
        B -->|Azure Import| C[hubtel-task-creator: Import Azure Tasks]
        B -->|Task Descriptions| D[hubtel-task-processor: Structure Tasks]
        B -->|Planning Phase| E[Standard CQT Planning Agents]
        B -->|Idea to Tasks| F[hubtel-task-creator: Analyze Idea]
        
        C --> G[Enhance with Hubtel Context]
        D --> H{Create Azure Tasks?}
        E --> I[hubtel-task-creator: Convert to Tasks]
        F --> J[Generate Azure Task Hierarchy]
        
        H -->|Yes| K[Create Azure Work Items]
        H -->|No| L[Use Structured Tasks]
        G --> M[Quality Validation]
        I --> M
        J --> M
        K --> M
        L --> M
        
        M --> N[hubtel-integration-coordinator: Analyze Dependencies]
        N --> O{Task Type}
        
        O -->|Frontend| P[hubtel-frontend-dev: Implement]
        O -->|Backend| Q[hubtel-backend-dev: Implement]
        O -->|Both| R[Coordinate Implementation]
        
        P --> S[Code Review Gate]
        Q --> S
        R --> S
        
        S --> T{Developer Approval}
        T -->|Approved| U[Commit with Task ID]
        T -->|Changes Needed| V[Revise Implementation]
        
        U --> W[Update Azure DevOps]
        V --> P
        V --> Q
        
        W --> X{More Tasks?}
        X -->|Yes| N
        X -->|No| Y[Workflow Complete]
        
        style A fill:#e1f5fe
        style Y fill:#c8e6c9
        style S fill:#fff3e0
        style T fill:#fce4ec
    ```

  # Handoff prompts
  handoff_prompts:
    azure_import_complete: "Azure tasks imported and analyzed within project context. Ready for implementation by frontend/backend agents."
    task_processing_complete: "Task descriptions processed into structured work items. Proceed with implementation or Azure creation."
    planning_to_tasks: "Planning artifacts complete. Converting to Azure DevOps task hierarchy with Hubtel context."
    idea_analysis_complete: "Business idea analyzed and converted to implementable Azure task hierarchy. Ready for development."
    implementation_ready: "All tasks analyzed and contextualized. Route to appropriate development agents for implementation."
    coordination_needed: "Cross-team coordination required. Integration coordinator will manage API changes and environment updates."
    review_prepared: "Implementation complete and prepared for developer review. All quality gates passed."
    implementation_committed: "Code committed with task reference. Azure DevOps status updated automatically."

  # Success metrics
  success_metrics:
    task_quality:
      - junior_engineer_implementation_success_rate: ">95%"
      - task_completion_within_1_hour: ">90%"
      - acceptance_criteria_completeness: "100%"
      - testing_requirement_coverage: ">90%"
    
    development_efficiency:
      - code_review_first_pass_rate: ">85%"
      - integration_conflict_rate: "<5%"
      - azure_devops_synchronization_accuracy: ">99%"
      - cross_team_coordination_effectiveness: ">90%"
    
    quality_standards:
      - hubtel_coding_standards_compliance: "100%"
      - testing_coverage_achievement: ">85%"
      - accessibility_compliance: "WCAG AA"
      - performance_benchmark_achievement: ">95%"