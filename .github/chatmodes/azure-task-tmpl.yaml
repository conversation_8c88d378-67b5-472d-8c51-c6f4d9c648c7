template:
  id: azure-task-template
  name: Azure DevOps Work Item Template
  version: 1.0
  output:
    format: azure_work_item
    work_item_type: "{{work_item_type}}"
    title: "{{task_title}}"

workflow:
  mode: structured
  enhancement_level: comprehensive

sections:
  - id: task-header
    title: Task Information
    instruction: |
      Capture essential task identification and classification information.
      This section establishes the foundation for Azure DevOps work item creation.
    sections:
      - id: title
        title: Task Title
        type: single-line
        instruction: Create clear, specific title that includes technical context
        examples:
          - "Implement responsive user dashboard with real-time notifications"
          - "Create JWT authentication middleware with refresh token support"
          - "Add PostgreSQL migration for user preferences table"

      - id: work_item_type
        title: Work Item Type
        type: choice
        choices: ["Epic", "Feature", "User Story", "Task"]
        instruction: Select appropriate Azure DevOps work item type based on scope and hierarchy

      - id: area_path
        title: Area Path
        type: single-line
        instruction: Azure DevOps area path for team assignment
        default: "{{project_name}}\\{{team_name}}"

      - id: iteration_path
        title: Iteration Path
        type: single-line
        instruction: Sprint or iteration assignment
        default: "{{project_name}}\\Sprint {{sprint_number}}"

  - id: business-context
    title: Business Context
    instruction: |
      Provide business justification and user value for this task.
      Help developers understand the "why" behind the implementation.
    sections:
      - id: business-purpose
        title: Business Purpose
        type: paragraph
        instruction: Explain why this task is needed and its business value
        examples:
          - "Improve user engagement by providing real-time account information"
          - "Enhance security by implementing industry-standard JWT authentication"

      - id: user-impact
        title: User Impact
        type: paragraph
        instruction: Describe how this task affects end users
        examples:
          - "Users can view their account balance and recent transactions without page refresh"
          - "Users experience secure, seamless authentication across all services"

  - id: technical-requirements
    title: Technical Requirements
    instruction: |
      Define technical specifications and implementation requirements using Hubtel's technology stack.
      Include framework-specific patterns and integration requirements.
    elicit: true
    sections:
      - id: functional-requirements
        title: Functional Requirements
        type: numbered-list
        prefix: FR
        instruction: Specific functional requirements with testable conditions
        examples:
          - "FR1: Dashboard displays user account balance updated in real-time"
          - "FR2: Authentication API returns JWT token with 24-hour expiration"

      - id: technical-specifications
        title: Technical Specifications
        type: numbered-list
        prefix: TS
        instruction: Technical implementation requirements and constraints
        examples:
          - "TS1: Use Next.js API routes for frontend-backend communication"
          - "TS2: Implement Entity Framework Core with PostgreSQL for data persistence"

      - id: integration-requirements
        title: Integration Requirements
        type: numbered-list
        prefix: IR
        instruction: API integrations and service dependencies
        examples:
          - "IR1: Integrate with user authentication service API"
          - "IR2: Connect to real-time notification service via WebSocket"

  - id: hubtel-context
    title: Hubtel Development Context
    instruction: |
      Add Hubtel-specific technical context, patterns, and standards.
      Reference internal documentation and established practices.
    sections:
      - id: technology-stack
        title: Technology Stack
        type: structured-list
        instruction: Specify relevant technologies from Hubtel's stack
        structure:
          frontend: "{{frontend_framework}}"  # Next.js or Nuxt.js
          backend: "{{backend_framework}}"    # .NET Core
          database: "{{database_type}}"       # PostgreSQL or MongoDB
          testing: "{{testing_frameworks}}"   # Vitest/Playwright or Karate

      - id: coding-standards
        title: Coding Standards Reference
        type: reference
        instruction: Link to relevant Hubtel coding standards and documentation
        default: "https://dev-docs.hubtel.com/introduction.html"

      - id: architectural-patterns
        title: Architectural Patterns
        type: bullet-list
        instruction: Recommended patterns and approaches for implementation
        examples:
          - "Use repository pattern for data access layer"
          - "Implement service layer for business logic separation"
          - "Apply component composition for UI development"

  - id: acceptance-criteria
    title: Acceptance Criteria
    instruction: |
      Generate comprehensive, testable acceptance criteria that define "done".
      Include functional, technical, and quality criteria.
    elicit: true
    sections:
      - id: functional-criteria
        title: Functional Acceptance Criteria
        type: numbered-list
        item_template: "Given {{precondition}}, when {{action}}, then {{expected_result}}"
        instruction: Define expected behavior from user perspective
        examples:
          - "Given user is authenticated, when dashboard loads, then account balance displays within 2 seconds"

      - id: technical-criteria
        title: Technical Acceptance Criteria
        type: numbered-list
        instruction: Define technical implementation requirements
        examples:
          - "API endpoints return proper HTTP status codes and error messages"
          - "Components are responsive and accessible across all target devices"

      - id: quality-criteria
        title: Quality Acceptance Criteria
        type: numbered-list
        instruction: Define quality standards and testing requirements
        examples:
          - "Unit test coverage minimum 85% for all business logic"
          - "End-to-end tests cover critical user journeys"
          - "Code passes Hubtel coding standard validations"

  - id: implementation-guidance
    title: Implementation Guidance
    instruction: |
      Provide technical guidance and implementation patterns to support development.
      Include code examples, references, and common pitfalls to avoid.
    sections:
      - id: implementation-approach
        title: Implementation Approach
        type: structured-text
        instruction: Step-by-step development approach and sequence
        examples:
          - "1. Create database models and migrations\n2. Implement API endpoints with validation\n3. Create frontend components with error handling\n4. Add comprehensive test coverage"

      - id: code-patterns
        title: Code Patterns and Examples
        type: code-block
        instruction: Relevant code snippets and patterns for implementation
        examples:
          - "// Next.js API route example\nexport async function GET(request: Request) {\n  // Implementation\n}"

      - id: reference-materials
        title: Reference Materials
        type: bullet-list
        instruction: Links to documentation, similar implementations, and resources
        examples:
          - "Similar dashboard implementation: /components/AccountDashboard.tsx"
          - "Authentication pattern documentation: https://dev-docs.hubtel.com/auth-patterns"

  - id: testing-requirements
    title: Testing Requirements
    instruction: |
      Define comprehensive testing strategy including unit, integration, and E2E tests.
      Specify testing frameworks and coverage requirements.
    sections:
      - id: unit-testing
        title: Unit Testing
        type: structured-list
        instruction: Unit testing requirements and specifications
        structure:
          framework: "{{unit_test_framework}}"  # Vitest or NUnit
          coverage_minimum: "85%"
          critical_paths: "100% coverage of business logic"
          test_categories: ["Component logic", "API endpoints", "Business services"]

      - id: integration-testing
        title: Integration Testing
        type: structured-list
        instruction: Integration and E2E testing requirements
        structure:
          framework: "{{integration_test_framework}}"  # Playwright or Karate
          scenarios: ["Critical user journeys", "API contract validation", "Cross-browser compatibility"]
          test_data: "Use test data factories and fixtures"

      - id: accessibility-testing
        title: Accessibility Testing
        type: structured-list
        instruction: Accessibility compliance and testing requirements
        structure:
          compliance_level: "WCAG AA"
          automated_tools: ["axe-core", "Lighthouse accessibility audit"]
          manual_testing: ["Keyboard navigation", "Screen reader compatibility"]

  - id: coordination-requirements
    title: Team Coordination Requirements
    instruction: |
      Identify cross-team coordination needs and integration points.
      Specify dependencies and communication requirements.
    condition: "has_cross_team_dependencies"
    sections:
      - id: frontend-backend-coordination
        title: Frontend/Backend Coordination
        type: structured-text
        instruction: API dependencies and coordination requirements between frontend and backend teams
        examples:
          - "Backend API must be deployed and tested before frontend integration"
          - "API contract changes require notification to frontend team 24 hours in advance"

      - id: environment-coordination
        title: Environment Coordination
        type: bullet-list
        instruction: Docker, configuration, or infrastructure coordination needs
        examples:
          - "New Redis service requires Docker Compose update"
          - "Environment variables need synchronization across dev/staging/prod"

      - id: deployment-coordination
        title: Deployment Coordination
        type: structured-text
        instruction: Special deployment considerations or sequencing requirements
        examples:
          - "Database migration must complete before application deployment"
          - "Feature flag coordination required for gradual rollout"

  - id: definition-of-done
    title: Definition of Done
    instruction: |
      Comprehensive checklist defining when this task is considered complete.
      Include code quality, testing, documentation, and review requirements.
    sections:
      - id: implementation-checklist
        title: Implementation Checklist
        type: checklist
        items:
          - "Code implemented following Hubtel coding standards"
          - "All functional requirements implemented and tested"
          - "Error handling and edge cases addressed"
          - "Responsive design implemented and tested"
          - "Accessibility requirements met (WCAG AA)"

      - id: testing-checklist
        title: Testing Checklist
        type: checklist
        items:
          - "Unit tests written with minimum 85% coverage"
          - "Integration tests cover critical user paths"
          - "End-to-end tests validate complete user journeys"
          - "Performance testing completed (API < 200ms response)"
          - "Security validation performed"

      - id: quality-checklist
        title: Quality Checklist
        type: checklist
        items:
          - "Code reviewed and approved by senior developer"
          - "Documentation updated (API docs, README, comments)"
          - "OpenTelemetry logging implemented where applicable"
          - "Environment configuration updated if needed"
          - "Changes deployed to staging and validated"

  - id: azure-metadata
    title: Azure DevOps Metadata
    instruction: |
      Azure DevOps-specific metadata and field mappings for work item creation.
      This section is processed automatically during Azure integration.
    sections:
      - id: effort-estimation
        title: Effort Estimation
        type: structured-data
        instruction: Time and complexity estimates for planning
        structure:
          original_estimate: "1"  # Hours
          remaining_work: "1"     # Hours
          story_points: "{{story_points}}"  # If using story points
          complexity: "{{complexity_level}}"  # Low/Medium/High

      - id: tags-and-labels
        title: Tags and Labels
        type: comma-separated
        instruction: Azure DevOps tags for filtering and organization
        default: "Hubtel, CQT-Enhanced, {{technology_stack}}"

      - id: priority-and-severity
        title: Priority and Severity
        type: structured-data
        structure:
          priority: "{{priority_level}}"  # 1-4
          severity: "{{severity_level}}"  # 1-4 (if applicable)
          risk: "{{risk_assessment}}"     # Low/Medium/High