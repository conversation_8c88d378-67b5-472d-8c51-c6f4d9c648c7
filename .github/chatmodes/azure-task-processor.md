# Azure Task Processor

## ⚠️ CRITICAL EXECUTION NOTICE ⚠️

**THIS IS AN EXECUTABLE WORKFLOW - NOT REFERENCE MATERIAL**

When this task is invoked:
1. **MANDATORY STEP-BY-STEP EXECUTION** - Each phase must be completed sequentially
2. **AZURE API INTEGRATION** - This workflow requires Azure DevOps API access
3. **BATCH PROCESSING SUPPORT** - Can handle multiple task IDs simultaneously
4. **ENHANCEMENT REQUIRED** - All tasks must be enhanced for junior engineer readiness

## Overview

This workflow processes Azure DevOps work items by importing, enhancing, and standardizing them for Hubtel's development workflow. It supports all entry points and ensures tasks meet junior engineer implementation standards.

## Input Parameters

### Required Parameters
- **task_ids**: Array of Azure DevOps work item IDs (e.g., ["AZ-123", "AZ-124"])
- **azure_config**: Azure DevOps configuration object

### Optional Parameters
- **enhancement_level**: "basic" | "comprehensive" | "enterprise" (default: "comprehensive")
- **create_implementation_tasks**: boolean (default: true)
- **batch_size**: number (default: 5, max: 20)
- **auto_link_dependencies**: boolean (default: true)

## Execution Steps

### Phase 1: Authentication & Validation

```yaml
step: authenticate_azure
description: Establish Azure DevOps connection and validate access
actions:
  - validate_credentials: Check PAT or Service Principal access
  - test_connection: Verify API connectivity and permissions
  - validate_project_access: Ensure access to specified project
  - check_rate_limits: Verify API quota and usage limits
error_handling:
  - invalid_credentials: Request credential refresh
  - insufficient_permissions: Escalate to admin
  - rate_limit_exceeded: Implement backoff strategy
```

### Phase 2: Task Retrieval & Analysis

```yaml
step: retrieve_tasks
description: Fetch work items from Azure DevOps and perform initial analysis
actions:
  - batch_retrieve: GET /wit/workitems?ids={task_ids}
  - parse_work_items: Extract title, description, acceptance criteria
  - analyze_relationships: Identify parent-child and dependency links
  - assess_current_status: Check work item states and assignments
  - extract_metadata: Gather area path, iteration, tags, and custom fields
processing:
  - filter_invalid: Remove inaccessible or deleted work items
  - categorize_types: Group by work item type (Epic, Story, Task)
  - priority_sorting: Order by business priority and dependencies
```

### Phase 3: Hubtel Context Enhancement

```yaml
step: enhance_with_hubtel_context
description: Add Hubtel-specific technical and business context
enhancements:
  - technology_stack_context:
    - frontend: Add Next.js/Nuxt.js implementation patterns
    - backend: Add .NET Core and Entity Framework guidance
    - testing: Include Vitest, Playwright, and Karate requirements
    - observability: Add OpenTelemetry logging requirements
  
  - hubtel_standards:
    - coding_standards: Reference https://dev-docs.hubtel.com/introduction.html
    - architecture_patterns: Apply Hubtel architectural guidelines
    - security_requirements: Include authentication and authorization patterns
    - performance_expectations: Add performance criteria and benchmarks
  
  - development_context:
    - environment_setup: Docker Compose configuration requirements
    - integration_patterns: API integration and communication standards
    - deployment_considerations: CI/CD and deployment requirements
```

### Phase 4: Task Standardization

```yaml
step: standardize_tasks
description: Apply Hubtel task format and sizing standards
standardization:
  - task_sizing:
    - validate_1_hour_scope: Ensure tasks fit 1-hour implementation window
    - split_oversized: Break large tasks into smaller, manageable pieces
    - merge_undersized: Combine trivial tasks into meaningful work units
  
  - acceptance_criteria_generation:
    - functional_criteria: Define expected behavior and user interactions
    - technical_criteria: Specify implementation requirements and constraints
    - testing_criteria: Define unit and E2E testing expectations
    - performance_criteria: Set response time and quality benchmarks
  
  - implementation_guidance:
    - technical_approach: Suggest implementation patterns and strategies
    - code_examples: Provide relevant code snippets and references
    - common_pitfalls: Highlight potential issues and solutions
    - best_practices: Include Hubtel-specific development practices
```

### Phase 5: Dependency Analysis

```yaml
step: analyze_dependencies
description: Identify and coordinate task dependencies and relationships
dependency_analysis:
  - technical_dependencies:
    - api_contracts: Identify frontend/backend API dependencies
    - database_changes: Coordinate schema migrations and data updates
    - shared_components: Identify reusable component dependencies
    - configuration_changes: Environment and infrastructure dependencies
  
  - execution_order:
    - topological_sort: Create dependency-ordered execution sequence
    - parallel_opportunities: Identify tasks that can run simultaneously
    - blocking_dependencies: Flag tasks that block other work
    - critical_path: Identify sequence that determines overall timeline
  
  - coordination_requirements:
    - frontend_backend: Tasks requiring cross-team coordination
    - environment_updates: Docker Compose and configuration changes
    - integration_testing: End-to-end validation requirements
```

### Phase 6: Quality Validation

```yaml
step: validate_quality
description: Ensure all tasks meet junior engineer readiness standards
validation_criteria:
  - clarity_check:
    - objective_clarity: Task purpose is immediately obvious
    - scope_definition: Clear boundaries of what's included/excluded
    - success_criteria: Unambiguous definition of completion
  
  - completeness_validation:
    - technical_context: Sufficient implementation information
    - testing_requirements: Comprehensive testing specifications
    - documentation_links: References to relevant standards and examples
  
  - implementation_readiness:
    - junior_engineer_test: Can be implemented without extensive guidance
    - pattern_references: Links to similar implementations and examples
    - troubleshooting_guidance: Common issues and resolution approaches
```

### Phase 7: Project Context Analysis

```yaml
step: analyze_project_context
description: Understand task within current Hubtel project structure and codebase
context_analysis:
  - codebase_analysis:
    - identify_related_files: Find existing components, services, or modules
    - analyze_architecture: Understand current system design and patterns
    - detect_dependencies: Map internal and external service dependencies
  
  - technology_alignment:
    - frontend_context: Identify Next.js/Nuxt.js patterns and components
    - backend_context: Analyze .NET Core services and data models
    - integration_points: Map API contracts and service interactions
  
  - implementation_readiness:
    - assess_complexity: Evaluate technical complexity within project context
    - identify_blockers: Detect potential implementation dependencies
    - plan_approach: Outline implementation strategy using existing patterns
```

### Phase 8: Implementation Routing

```yaml
step: route_for_implementation
description: Route tasks to appropriate agents with project context
routing_activities:
  - agent_assignment:
    - frontend_tasks: Route to hubtel-frontend-dev with codebase context
    - backend_tasks: Route to hubtel-backend-dev with service architecture context
    - integration_tasks: Route to hubtel-integration-coordinator with dependency map
  
  - context_packaging:
    - relevant_files: Provide related existing code and patterns
    - architecture_notes: Include system design context and constraints
    - dependency_map: Provide service and component dependency information
  
  - implementation_guidance:
    - existing_patterns: Reference similar implementations in the codebase
    - technical_constraints: Project-specific limitations and requirements
    - integration_points: Clear API contracts and service interaction details
```

## Output Format

### Task Processing Results
```yaml
processing_results:
  summary:
    total_tasks_processed: number
    successful_context_analysis: number
    failed_analysis: number
    routing_decisions_made: number
    processing_time_seconds: number
  
  analyzed_tasks:
    - task_id: "AZ-123"
      original_title: "User dashboard feature"
      project_context: "Frontend task - relates to existing dashboard components"
      technical_complexity: "medium"
      agent_assignment: "hubtel-frontend-dev"
      estimated_hours: 1
      dependencies: ["AZ-124", "AZ-125"]
      related_files: ["src/components/Dashboard.tsx", "src/pages/dashboard.tsx"]
      ready_for_implementation: true
  
  coordination_requirements:
    - type: "api_dependency"
      frontend_task: "AZ-123"
      backend_task: "AZ-124"
      coordination_agent: "hubtel-integration-coordinator"
    
  next_steps:
    - action: "route_to_development_agents"
      tasks: ["AZ-123", "AZ-124", "AZ-125"]
    - action: "coordinate_integration"
      coordinator: "hubtel-integration-coordinator"
    - action: "notify_teams"
      channel: "teams"
      message: "3 tasks enhanced and ready for implementation"
```

### Error Handling
```yaml
error_scenarios:
  - scenario: "azure_api_failure"
    action: "retry_with_exponential_backoff"
    max_retries: 3
    fallback: "cache_locally_and_sync_later"
  
  - scenario: "invalid_task_id"
    action: "skip_task_and_continue"
    notification: "log_warning_and_notify_user"
  
  - scenario: "enhancement_failure"
    action: "apply_basic_enhancement"
    escalation: "flag_for_manual_review"
```

## Usage Examples

### Single Task Processing
```yaml
input:
  task_ids: ["AZ-123"]
  azure_config: {organization: "hubtel", project: "main", pat: "..."}
```

### Batch Processing
```yaml
input:
  task_ids: ["AZ-123", "AZ-124", "AZ-125", "AZ-126", "AZ-127"]
  enhancement_level: "comprehensive"
  batch_size: 5
  auto_link_dependencies: true
```

### Enterprise Processing
```yaml
input:
  task_ids: ["AZ-100", "AZ-101", "AZ-102"]
  enhancement_level: "enterprise"
  create_implementation_tasks: true
  include_architecture_review: true
```

This workflow ensures that Azure DevOps tasks are properly enhanced, standardized, and prepared for successful implementation within Hubtel's development ecosystem.