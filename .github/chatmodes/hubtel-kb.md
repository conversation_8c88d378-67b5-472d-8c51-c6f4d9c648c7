# Hubtel Development Knowledge Base

## Overview

The Hubtel CQT Expansion Pack provides AI agents specialized for Hubtel's development workflow, including Azure DevOps integration, frontend/backend coordination, and automated task management.

## Hubtel Technology Stack

### Frontend Technologies
- **Next.js**: React-based framework for production-ready applications
- **Nuxt.js**: Vue.js framework for server-side rendered applications
- **Testing**: Vitest for unit testing, Playwright for end-to-end testing
- **Styling**: Tailwind CSS, CSS Modules, or styled-components depending on project

### Backend Technologies
- **.NET Core**: Primary backend framework for APIs and services
- **Entity Framework Core**: ORM for database operations
- **PostgreSQL**: Primary relational database
- **MongoDB**: Document database for specific use cases
- **Testing**: Karate for API testing, mutation testing for code quality

### Development Tools
- **Azure DevOps**: Project management, CI/CD, and code repositories
- **Docker**: Containerization for local development and deployment
- **OpenTelemetry**: Observability and logging framework
- **Git**: Version control with Azure Repos integration

## Development Workflow

### Task Management
- **Task Sizing**: All tasks should be completable within 1 hour
- **Acceptance Criteria**: Every task must have clear, testable acceptance criteria
- **Testing Requirements**: Unit tests and E2E tests are mandatory for all features
- **Code Review**: All code must be reviewed before merging

### Entry Points
1. **Azure DevOps Import**: Import existing tasks for enhancement and implementation
2. **Task Description**: Process free-form task descriptions into structured work
3. **Planning Phase**: Full requirement gathering and architecture planning
4. **Idea to Tasks**: Convert business ideas into implementable Azure work items

### Coordination Patterns
- **API Changes**: Coordinate between frontend and backend when APIs change
- **Docker Updates**: Share new compose files for local development
- **Documentation**: Maintain API documentation via Swagger/OpenAPI
- **Communication**: Use Teams for real-time coordination

## Quality Standards

### Code Standards
- Follow Hubtel coding guidelines: https://dev-docs.hubtel.com/introduction.html
- Use consistent naming conventions across frontend and backend
- Implement proper error handling and logging
- Include comprehensive unit and integration tests

### Testing Requirements
- **Frontend**: Vitest for unit tests, Playwright for E2E
- **Backend**: Karate for API tests, mutation testing for quality
- **Coverage**: Minimum 80% code coverage for new features
- **E2E**: Critical user journeys must have automated E2E tests

### Documentation Standards
- API documentation via OpenAPI/Swagger
- Code documentation for complex business logic
- README files for setup and development instructions
- Architecture decisions documented in ADRs

## Integration Patterns

### Azure DevOps Integration
- Work items linked to commits via task IDs
- Automatic status updates based on code commits
- Parent-child relationships for epic/feature/story hierarchy
- Time tracking for development effort estimation

### Cross-Team Coordination
- Shared Docker Compose files for consistent environments
- API contract-first development approach
- Regular API specification updates via Postman/Swagger
- Teams notifications for breaking changes

### Environment Management
- Local development via Docker Compose
- Environment-specific configuration management
- Secrets management via Azure Key Vault
- Consistent deployment pipelines across environments

## Best Practices

### Development Practices
- Branch naming: feature/AZ-{task-id}-{description}
- Commit messages: {type}(AZ-{task-id}): {description}
- Pull request templates with checklists
- Automated testing in CI/CD pipeline

### Performance Considerations
- Database query optimization with EF Core
- Frontend bundle optimization and code splitting
- API response caching strategies
- Monitoring and alerting via OpenTelemetry

### Security Practices
- Input validation on all API endpoints
- Authentication and authorization patterns
- Secure secret management
- Regular security scanning and updates

## Common Scenarios

### Frontend Task Implementation
1. Parse HTML artifacts from UX team
2. Implement responsive component with Next.js/Nuxt.js
3. Add Vitest unit tests for component logic
4. Create Playwright E2E tests for user interactions
5. Update API integration based on backend specifications

### Backend Task Implementation
1. Design API endpoints following REST principles
2. Implement .NET Core controllers and services
3. Add Entity Framework Core data models and migrations
4. Create Karate tests for API endpoints
5. Add OpenTelemetry logging and monitoring

### Integration Task Implementation
1. Coordinate API changes between frontend and backend
2. Update Docker Compose files for new services
3. Generate updated OpenAPI specifications
4. Notify teams of breaking changes
5. Validate end-to-end functionality

This knowledge base serves as the foundation for all Hubtel-specific agents, ensuring consistent development practices and quality standards across all projects.