# Karate API Test Template
Feature: API Testing for [API Name]

Background:
  * url apiBaseUrl
  * def authToken = karate.properties['auth.token']
  * header Authorization = 'Bearer ' + authToken
  * header Content-Type = 'application/json'

Scenario: Get resource by ID - Happy Path
  Given path 'api/resource/123'
  When method GET
  Then status 200
  And match response == 
  """
  {
    id: 123,
    name: '#string',
    status: 'active',
    createdAt: '#string',
    updatedAt: '#string'
  }
  """
  And match response.id == 123
  And match response.name == '#present'

Scenario: Create new resource - Valid data
  Given path 'api/resource'
  And request 
  """
  {
    name: 'Test Resource',
    description: 'Test description',
    category: 'test'
  }
  """
  When method POST
  Then status 201
  And match response.id == '#number'
  And match response.name == 'Test Resource'
  And match response.status == 'pending'

Scenario: Update existing resource - Partial update
  Given path 'api/resource/123'
  And request { name: 'Updated Name' }
  When method PATCH
  Then status 200
  And match response.name == 'Updated Name'
  And match response.id == 123

Scenario: Delete resource - Valid ID
  Given path 'api/resource/123'
  When method DELETE
  Then status 204

Scenario Outline: Create resource with invalid data - Validation errors
  Given path 'api/resource'
  And request <requestData>
  When method POST
  Then status 400
  And match response.error == '#string'
  And match response.message contains <expectedError>

  Examples:
    | requestData                              | expectedError    |
    | {}                                       | 'name is required' |
    | { name: '' }                            | 'name cannot be empty' |
    | { name: 'a', description: null }        | 'invalid description' |

Scenario: Get resource with invalid ID - Not Found
  Given path 'api/resource/99999'
  When method GET
  Then status 404
  And match response == 
  """
  {
    error: 'Resource not found',
    code: 'RESOURCE_NOT_FOUND',
    statusCode: 404
  }
  """

Scenario: Unauthorized access - Missing token
  Given path 'api/resource/123'
  And header Authorization = ''
  When method GET
  Then status 401
  And match response.error == 'Unauthorized'

Scenario: Forbidden access - Insufficient permissions
  Given path 'api/admin/resource'
  And def userToken = karate.properties['user.token']
  And header Authorization = 'Bearer ' + userToken
  When method GET
  Then status 403
  And match response.error == 'Forbidden'

Scenario: API Rate Limiting - Too many requests
  Given path 'api/resource/123'
  And def results = []
  # Make multiple rapid requests
  And def fun = function(x){ karate.http('GET', apiBaseUrl + '/api/resource/123', null, { Authorization: 'Bearer ' + authToken }) }
  And def responses = karate.repeat(100, fun)
  # At least one should be rate limited
  And def rateLimited = responses.filter(function(r){ return r.status == 429 })
  And assert rateLimited.length > 0

Scenario: Pagination - Get paginated results
  Given path 'api/resources'
  And param page = 1
  And param limit = 10
  When method GET
  Then status 200
  And match response == 
  """
  {
    data: '#[10] object',
    pagination: {
      page: 1,
      limit: 10,
      total: '#number',
      hasNext: '#boolean',
      hasPrev: false
    }
  }
  """
  And assert response.data.length <= 10

Scenario: Search functionality - Filter by criteria
  Given path 'api/resources'
  And param search = 'test'
  And param category = 'active'
  When method GET
  Then status 200
  And match each response.data contains { category: 'active' }
  And def names = response.data[*].name
  And match names contains only '#string'

Scenario: Bulk operations - Create multiple resources
  Given path 'api/resources/bulk'
  And request 
  """
  {
    resources: [
      { name: 'Resource 1', category: 'test' },
      { name: 'Resource 2', category: 'test' },
      { name: 'Resource 3', category: 'test' }
    ]
  }
  """
  When method POST
  Then status 201
  And match response.created == 3
  And match response.resources == '#[3] object'
  And match each response.resources contains { id: '#number' }

Scenario: File upload - Valid file
  Given path 'api/resource/123/upload'
  And multipart file file = { read: 'test-file.pdf', filename: 'document.pdf', contentType: 'application/pdf' }
  When method POST
  Then status 200
  And match response.filename == 'document.pdf'
  And match response.size == '#number'
  And match response.url == '#string'

Scenario: Async operation - Long running task
  Given path 'api/resource/123/process'
  And request { operation: 'complex_calculation' }
  When method POST
  Then status 202
  And match response.taskId == '#string'
  And def taskId = response.taskId
  
  # Poll for completion
  * def sleep = function(ms){ java.lang.Thread.sleep(ms) }
  Given path 'api/tasks/' + taskId
  And retry until responseStatus == 200 && response.status == 'completed'
  When method GET
  And call sleep 1000
  
  Then match response.status == 'completed'
  And match response.result == '#present'

Scenario: API versioning - Different versions
  Given path 'v1/api/resource/123'
  When method GET
  Then status 200
  And match response.version == 'v1'
  
  Given path 'v2/api/resource/123'
  When method GET
  Then status 200
  And match response.version == 'v2'
  And match response.metadata == '#present'

Scenario: Error handling - Server errors
  Given path 'api/resource/trigger-error'
  When method POST
  Then status 500
  And match response == 
  """
  {
    error: 'Internal server error',
    code: 'INTERNAL_ERROR',
    statusCode: 500,
    timestamp: '#string',
    requestId: '#string'
  }
  """

Scenario: Data integrity - Concurrent updates
  Given path 'api/resource/123'
  When method GET
  Then status 200
  And def version = response.version
  
  # Simulate concurrent update
  Given path 'api/resource/123'
  And request { name: 'Updated Name', version: version }
  When method PUT
  Then status 200
  
  # Second update with stale version should fail
  Given path 'api/resource/123'
  And request { name: 'Another Update', version: version }
  When method PUT
  Then status 409
  And match response.error == 'Conflict'

Scenario: Performance testing - Response time validation
  Given path 'api/resource/123'
  When method GET
  Then status 200
  * def responseTime = karate.get('responseTime')
  And assert responseTime < 1000