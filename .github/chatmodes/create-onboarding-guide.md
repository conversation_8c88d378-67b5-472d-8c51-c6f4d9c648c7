# Create Onboarding Guide

## ⚠️ CRITICAL EXECUTION NOTICE ⚠️

**THIS IS AN EXECUTABLE WORKFLOW - NOT REFERENCE MATERIAL**

When this task is invoked:
1. **PERSONALIZED GUIDANCE** - Create onboarding specific to the engineer's experience and project needs
2. **HANDS-ON APPROACH** - Provide practical, executable steps rather than theoretical knowledge
3. **CONFIDENCE BUILDING** - Structure learning to build competence progressively
4. **IMMEDIATE PRODUCTIVITY** - Focus on getting engineer contributing within hours, not days

## Overview

This workflow creates a comprehensive, personalized onboarding guide that transforms an engineer unfamiliar with a project into a confident contributor ready to make meaningful changes safely and effectively.

## Onboarding Guide Framework

### Phase 1: Project Context & Setup

```yaml
step: establish_project_context
description: Build foundational understanding of what this project does and why it exists
activities:
  - business_context_explanation:
    - project_purpose: "What business problem does this solve?"
    - user_impact: "How do users benefit from this system?"
    - hubtel_ecosystem_role: "How does this fit into Hubtel's broader platform?"
    - success_metrics: "How is project success measured?"
    - recent_priorities: "What are the current focus areas and initiatives?"
  
  - stakeholder_mapping:
    - primary_users: "Who are the end users and what do they need?"
    - internal_customers: "Which Hubtel teams depend on this project?"
    - external_dependencies: "What external systems or partners are involved?"
    - decision_makers: "Who makes product and technical decisions?"
  
  - project_timeline_context:
    - development_history: "Key milestones and architectural decisions"
    - current_phase: "Where the project is in its lifecycle"
    - upcoming_initiatives: "Major features or changes planned"
    - technical_debt_areas: "Known areas needing improvement"
```

### Phase 2: Technical Foundation Building

```yaml
step: build_technical_foundation
description: Establish deep technical understanding through hands-on exploration
activities:
  - guided_environment_setup:
    - prerequisites_installation: "Step-by-step tool and dependency setup"
    - configuration_walkthrough: "Environment variables and settings explanation"
    - verification_checklist: "How to confirm everything is working correctly"
    - troubleshooting_guide: "Common setup issues and their solutions"
  
  - architecture_deep_dive:
    - system_overview: "High-level architecture with visual diagrams"
    - component_relationships: "How major pieces fit together and communicate"
    - data_flow_tracing: "Follow data from input to output with examples"
    - design_pattern_explanation: "Why specific patterns were chosen and how they work"
  
  - hands_on_code_exploration:
    - critical_path_walkthrough: "Step through the most important code paths"
    - pattern_identification: "Show repeated patterns and conventions"
    - abstraction_layer_explanation: "How different layers separate concerns"
    - configuration_deep_dive: "How system behavior is configured and controlled"
```

### Phase 3: Practical Skills Development

```yaml
step: develop_practical_skills
description: Build hands-on capabilities through guided practice and real scenarios
activities:
  - development_workflow_mastery:
    - local_development_cycle: "Edit → Test → Debug → Verify cycle"
    - testing_strategy_practice: "How to write and run different types of tests"
    - debugging_technique_training: "Tools and approaches for investigating issues"
    - performance_monitoring_understanding: "How to assess and improve system performance"
  
  - change_implementation_training:
    - safe_change_identification: "Areas where changes have minimal risk"
    - impact_assessment_techniques: "How to understand change implications"
    - testing_requirement_determination: "What level of testing different changes need"
    - rollback_procedure_understanding: "How to undo changes if needed"
  
  - collaboration_pattern_learning:
    - code_review_process: "How to request and provide effective code reviews"
    - team_communication_norms: "How to ask questions and share updates"
    - documentation_maintenance: "When and how to update project documentation"
    - knowledge_sharing_practices: "How to contribute to team learning"
```

### Phase 4: Confidence Validation & Independence

```yaml
step: validate_readiness_and_build_independence
description: Confirm understanding through practical application and build self-sufficiency
activities:
  - guided_task_completion:
    - starter_task_selection: "Choose appropriate first task based on learning"
    - implementation_mentoring: "Provide guidance while engineer implements"
    - review_and_feedback: "Thorough review with learning-focused feedback"
    - success_celebration: "Acknowledge achievement and build confidence"
  
  - knowledge_validation_exercises:
    - scenario_based_questions: "How would you approach common situations?"
    - troubleshooting_scenarios: "Walk through investigating sample issues"
    - change_impact_assessments: "Analyze implications of proposed changes"
    - architectural_decision_discussions: "Understand rationale behind design choices"
  
  - independence_preparation:
    - resource_identification: "Where to find answers to future questions"
    - escalation_path_clarification: "When and how to ask for help"
    - continuous_learning_plan: "How to deepen understanding over time"
    - contribution_opportunity_mapping: "Areas where engineer can make meaningful impact"
```

## Personalized Onboarding Strategies

### Based on Engineer Experience Level

#### Junior Engineer Onboarding
```yaml
junior_engineer_approach:
  focus_areas:
    - fundamental_concepts: "Explain basic patterns and why they exist"
    - safety_first: "Emphasize testing and careful change practices"
    - learning_resources: "Point to documentation, tutorials, and references"
    - mentorship_heavy: "Frequent check-ins and guided practice"
  
  success_criteria:
    - can_complete_simple_tasks: "Bug fixes and small feature additions"
    - understands_testing: "Can write and run tests for their changes"
    - knows_help_sources: "Comfortable asking questions and finding resources"
    - follows_patterns: "Consistently applies established coding patterns"
```

#### Mid-Level Engineer Onboarding
```yaml
mid_level_engineer_approach:
  focus_areas:
    - architectural_understanding: "Why system is designed the way it is"
    - performance_awareness: "How changes affect system performance"
    - integration_complexity: "How this system interacts with others"
    - design_decision_context: "Trade-offs and alternatives considered"
  
  success_criteria:
    - can_design_solutions: "Can plan approach for medium complexity features"
    - understands_trade_offs: "Aware of performance, security, maintainability implications"
    - contributes_to_architecture: "Can participate in design discussions meaningfully"
    - mentors_others: "Can help onboard other team members"
```

#### Senior Engineer Onboarding
```yaml
senior_engineer_approach:
  focus_areas:
    - system_constraints: "Historical decisions and current limitations"
    - evolution_strategy: "How system is evolving and why"
    - cross_system_impacts: "How changes propagate through ecosystem"
    - organizational_context: "Team dynamics and decision-making processes"
  
  success_criteria:
    - can_lead_initiatives: "Can drive significant features or improvements"
    - identifies_improvements: "Spots opportunities for architectural enhancements"
    - influences_decisions: "Contributes meaningfully to strategic technical decisions"
    - drives_standards: "Helps establish and evolve team practices"
```

### Based on Project Complexity

#### Simple Project Onboarding
```yaml
simple_project_approach:
  characteristics: "Clear patterns, limited dependencies, straightforward architecture"
  onboarding_time: "2-4 hours"
  focus: "Quick productivity through pattern recognition and hands-on practice"
  
  onboarding_steps:
    - "30-minute architecture overview"
    - "1-hour hands-on setup and exploration"
    - "1-hour guided task completion"
    - "30-minute independence validation"
```

#### Complex Project Onboarding
```yaml
complex_project_approach:
  characteristics: "Multiple patterns, heavy dependencies, sophisticated architecture"
  onboarding_time: "1-2 days"
  focus: "Deep understanding building through systematic exploration and mentored practice"
  
  onboarding_steps:
    - "2-hour business context and architecture deep dive"
    - "4-hour guided code exploration and pattern learning"
    - "4-hour hands-on development with mentoring"
    - "2-hour advanced scenarios and independence preparation"
```

## Onboarding Deliverables

### Personalized Learning Guide
```markdown
# {Engineer Name}'s {Project Name} Onboarding Guide

## Your Learning Path
Based on your {experience_level} experience and the {project_complexity} complexity of {project_name}, 
here's your personalized path to productivity:

### Day 1: Foundation Building
- [ ] **9:00-10:30**: Business context and project purpose deep dive
- [ ] **10:45-12:00**: Architecture overview and system boundaries
- [ ] **13:00-14:30**: Environment setup and verification
- [ ] **14:45-16:00**: Guided code exploration and pattern identification
- [ ] **16:00-17:00**: Testing strategy and debugging tools overview

### Day 2: Hands-On Practice  
- [ ] **9:00-10:30**: First guided task implementation
- [ ] **10:45-12:00**: Code review and feedback session
- [ ] **13:00-14:30**: Independent task attempt with support
- [ ] **14:45-16:00**: Troubleshooting scenario practice
- [ ] **16:00-17:00**: Knowledge validation and next steps planning

## Your Success Indicators
✅ **Ready for Independent Work When:**
- Can explain system architecture to someone else
- Can implement small features following established patterns
- Can debug issues using logs and monitoring tools  
- Can assess impact of proposed changes
- Knows when and how to ask for help

## Your Go-To Resources
📚 **Documentation**: {links_to_key_docs}
🔧 **Tools**: {development_tools_and_shortcuts}
👥 **People**: {team_contacts_and_expertise_areas}
🚨 **Help**: {escalation_paths_and_communication_channels}
```

### Quick Reference Cards
```yaml
quick_reference_deliverables:
  architecture_cheat_sheet:
    - "System component diagram with responsibilities"
    - "Data flow diagram with typical scenarios"
    - "Integration points and external dependencies"
  
  development_workflow_card:
    - "Local development commands and shortcuts"
    - "Testing commands and coverage expectations"
    - "Debugging tools and common investigation steps"
    - "Deployment process and verification steps"
  
  common_scenarios_guide:
    - "How to add a new API endpoint"
    - "How to add a new UI component"
    - "How to investigate a performance issue"
    - "How to handle a production incident"
```

### Confidence Building Exercises
```yaml
hands_on_exercises:
  exploration_tasks:
    - "Find and explain the authentication flow"
    - "Trace a user request from UI to database and back"
    - "Locate and understand the error handling patterns"
    - "Identify the most critical business logic components"
  
  implementation_challenges:
    - "Add logging to an existing function"
    - "Write a test for an existing feature"
    - "Fix a simple bug with provided reproduction steps"
    - "Add a new field to an existing form/API"
  
  scenario_responses:
    - "A user reports slow page loading - what do you investigate?"
    - "A new feature needs to integrate with external API - what are the considerations?"
    - "Production logs show increasing error rates - what's your investigation approach?"
```

This comprehensive onboarding approach ensures engineers gain not just surface knowledge, but deep confidence and practical capability that enables immediate meaningful contribution to any Hubtel project! 🚀