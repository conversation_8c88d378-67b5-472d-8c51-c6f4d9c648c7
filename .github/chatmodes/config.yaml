name: hubtel-workflow
version: 1.0.0
short-title: Hubtel Development Workflow Pack
description: Hubtel-specific development workflow expansion pack with Azure DevOps integration, Next.js/Nuxt.js frontend support, and .NET Core backend development
author: Hubtel Development Team
slashPrefix: hubtel

# Hubtel-specific configuration
hubtel_config:
  # Technology stack
  frontend_frameworks:
    - nextjs
    - nuxtjs
  backend_framework: dotnet_core
  databases:
    - postgresql
    - mongodb
  testing_frameworks:
    frontend:
      - vitest
      - playwright
    backend:
      - karate
      - mutation
  observability:
    logging: opentelemetry

  # Development workflow
  task_size_hours: 1
  commit_strategy: review_first
  integration_channels:
    - teams
    - swagger
    - postman
    - azure_devops
  
  # Quality assurance
  testing_standards:
    minimum_coverage: 85
    accessibility_compliance: "WCAG AA"
    performance_benchmarks:
      api_response_time: 200
      page_load_time: 3000

  # Documentation
  internal_docs_url: "https://dev-docs.hubtel.com/introduction.html"
  coding_standards_url: "https://dev-docs.hubtel.com/coding-standards"

  # Azure DevOps integration
  azure_devops:
    default_organization: "hubtel"
    supported_auth_methods:
      - pat
      - service_principal
    work_item_types:
      - epic
      - feature
      - user_story
      - task
    default_effort_hours: 1
    status_flow:
      - "New"
      - "Active"
      - "Review" 
      - "Resolved"
      - "Closed"

  # Entry points configuration
  entry_points:
    azure_import:
      description: "Import and enhance existing Azure DevOps tasks"
      supports_batch: true
      max_batch_size: 20
    
    task_description:
      description: "Process developer-provided task descriptions"
      supports_azure_creation: true
      auto_enhance: true
    
    planning_phase:
      description: "Full CQT planning workflow with Hubtel context"
      creates_azure_hierarchy: true
      planning_depth: ["basic", "comprehensive", "enterprise"]
    
    idea_to_tasks:
      description: "Convert ideas to Azure DevOps work items"
      auto_create_azure: true
      supports_batch_creation: true

# Required CQT core dependencies
dependencies:
  core_tasks:
    - create-doc.md
    - advanced-elicitation.md
  core_templates:
    - story-tmpl.yaml
  core_utils:
    - template-format.md