# Test Project Analysis

## ⚠️ CRITICAL EXECUTION NOTICE ⚠️

**THIS IS AN EXECUTABLE WORKFLOW - NOT REFERENCE MATERIAL**

When this task is invoked:
1. **MANDATORY PROJECT SCANNING** - Analyze entire project structure for testing setup
2. **FRAMEWORK DETECTION** - Identify all testing frameworks and configurations
3. **COVERAGE ANALYSIS** - Assess current test coverage and quality
4. **COMPREHENSIVE REPORT** - Generate detailed analysis with actionable insights

## Overview

This workflow performs comprehensive analysis of a project's testing setup, identifies testing frameworks, analyzes coverage, and provides detailed recommendations for improving test quality and coverage.

## Input Parameters

### Required Parameters
- **project_path**: Absolute path to the project root directory
- **analysis_depth**: "basic" | "standard" | "comprehensive" (default: "comprehensive")

### Optional Parameters
- **include_dependencies**: boolean (default: true)
- **analyze_performance**: boolean (default: true)
- **check_accessibility**: boolean (default: true)
- **validate_security**: boolean (default: true)

## Analysis Framework

### Phase 1: Project Structure Analysis

```yaml
step: analyze_project_structure
description: Scan project structure to understand architecture and testing setup
analysis_activities:
  - directory_mapping:
    - scan_source_directories: Identify src/, lib/, components/ directories
    - find_test_directories: Locate __tests__/, test/, spec/ directories
    - detect_config_files: Find testing configuration files
    - map_file_patterns: Identify naming conventions and patterns
  
  - framework_detection:
    - frontend_frameworks: Detect React, Vue, Next.js, Nuxt.js
    - backend_frameworks: Identify .NET Core, Node.js, Express
    - testing_frameworks: Find Vitest, Jest, Playwright, Cypress, Karate, NUnit
    - build_tools: Identify Vite, Webpack, build configurations
```

### Phase 2: Testing Framework Analysis

```yaml
step: analyze_testing_frameworks
description: Deep analysis of configured testing frameworks and their setup
framework_analysis:
  - frontend_testing:
    - unit_test_runner: Vitest, Jest configuration and setup
    - component_testing: Testing Library, Enzyme setup
    - e2e_framework: Playwright, Cypress configuration
    - mocking_strategy: MSW, manual mocks, module mocking
  
  - backend_testing:
    - api_testing: Karate feature files and configuration
    - unit_testing: NUnit, xUnit test structure
    - integration_testing: Test containers, database testing
    - mutation_testing: Stryker.NET or similar setup
  
  - configuration_quality:
    - test_scripts: Package.json test commands
    - ci_integration: GitHub Actions, Azure DevOps pipelines
    - coverage_tools: Coverage reporters and thresholds
    - quality_gates: Lint rules, code quality checks
```

### Phase 3: Test Coverage Analysis

```yaml
step: analyze_test_coverage
description: Comprehensive analysis of existing test coverage and quality
coverage_analysis:
  - quantitative_metrics:
    - line_coverage: Percentage of lines covered by tests
    - branch_coverage: Percentage of code branches tested
    - function_coverage: Percentage of functions with tests
    - statement_coverage: Detailed statement-level coverage
  
  - qualitative_assessment:
    - test_quality: Assertion quality, test structure, maintainability
    - edge_case_coverage: Boundary conditions, error scenarios
    - integration_coverage: API endpoints, database interactions
    - user_journey_coverage: End-to-end workflow testing
  
  - gap_identification:
    - uncovered_files: Files without any test coverage
    - critical_paths: Important business logic without tests
    - error_handling: Missing error scenario testing
    - accessibility_gaps: Components without accessibility tests
```

### Phase 4: Test Quality Assessment

```yaml
step: assess_test_quality
description: Evaluate existing tests for quality, maintainability, and effectiveness
quality_metrics:
  - test_structure:
    - organization: Test file organization and naming
    - readability: Clear test descriptions and structure
    - maintainability: DRY principles, helper functions
    - performance: Test execution speed and reliability
  
  - assertion_quality:
    - meaningful_assertions: Tests verify actual behavior
    - error_messages: Clear failure messages for debugging
    - test_isolation: Independent tests without side effects
    - data_setup: Proper test data and mocking strategies
  
  - best_practices:
    - aaa_pattern: Arrange, Act, Assert structure
    - single_responsibility: One concept per test
    - descriptive_names: Clear test naming conventions
    - cleanup_procedures: Proper test cleanup and teardown
```

### Phase 5: Framework Compatibility Analysis

```yaml
step: analyze_framework_compatibility
description: Assess how well current testing setup aligns with Hubtel standards
compatibility_check:
  - hubtel_standards:
    - required_frameworks: Vitest, Playwright, Karate, NUnit alignment
    - coverage_requirements: 85% minimum coverage compliance
    - accessibility_testing: WCAG AA testing requirements
    - performance_benchmarks: Response time testing standards
  
  - integration_assessment:
    - ci_cd_integration: Pipeline testing integration
    - reporting_tools: Coverage and quality reporting
    - automation_level: Test automation coverage
    - monitoring_integration: Test result monitoring and alerting
```

## Output Format

### Comprehensive Analysis Report

```yaml
project_analysis_report:
  summary:
    project_name: "Project Name"
    analysis_timestamp: "2024-01-15T10:30:00Z"
    total_files_analyzed: 156
    test_files_found: 45
    overall_coverage_score: 67.5
    quality_score: 8.2
    
  framework_detection:
    frontend:
      primary_framework: "Next.js"
      testing_runner: "Vitest"
      e2e_framework: "Playwright"
      component_testing: "@testing-library/react"
    backend:
      primary_framework: ".NET Core"
      unit_testing: "NUnit"
      api_testing: "Karate"
      integration_testing: "TestContainers"
  
  coverage_analysis:
    overall_metrics:
      line_coverage: 67.5
      branch_coverage: 62.1
      function_coverage: 71.8
      statement_coverage: 68.2
    
    by_category:
      components: 78.5
      services: 65.2
      utilities: 82.1
      api_endpoints: 45.7
      business_logic: 71.3
    
    critical_gaps:
      - path: "src/services/payment-processor.ts"
        coverage: 23.4
        priority: "high"
        reason: "Critical business logic with low coverage"
      - path: "src/api/user-management.ts" 
        coverage: 31.2
        priority: "high"
        reason: "Security-sensitive code needs more tests"
  
  quality_assessment:
    test_quality_score: 8.2
    strengths:
      - "Well-organized test structure"
      - "Good use of testing utilities"
      - "Clear test descriptions"
    
    areas_for_improvement:
      - priority: "high"
        issue: "Missing error scenario testing"
        affected_files: 23
        recommendation: "Add error handling and edge case tests"
      - priority: "medium"
        issue: "Inconsistent mocking strategies"
        affected_files: 12
        recommendation: "Standardize mock patterns across tests"
  
  hubtel_compliance:
    standards_met: 6
    standards_total: 10
    compliance_score: 60
    
    compliance_gaps:
      - standard: "85% minimum coverage"
        current: "67.5%"
        gap: "17.5%"
        action: "Add tests for uncovered critical paths"
      - standard: "Accessibility testing"
        current: "15% of components tested"
        gap: "85% components missing a11y tests"
        action: "Implement WCAG AA testing for all components"
  
  recommendations:
    immediate_actions:
      - priority: 1
        action: "Add tests for payment-processor.ts"
        estimated_effort: "4 hours"
        impact: "High security and business impact"
      - priority: 2
        action: "Implement accessibility testing setup"
        estimated_effort: "6 hours"
        impact: "Compliance and user experience"
    
    strategic_improvements:
      - category: "Framework Optimization"
        recommendation: "Migrate remaining Jest tests to Vitest"
        benefit: "Consistent tooling and better performance"
        effort: "8 hours"
      - category: "Coverage Enhancement"
        recommendation: "Implement mutation testing"
        benefit: "Validate test quality and effectiveness"
        effort: "12 hours"
  
  detailed_file_analysis:
    high_priority_files:
      - path: "src/components/Dashboard.tsx"
        coverage: 45.2
        test_file: "src/components/__tests__/Dashboard.test.tsx"
        issues:
          - "Missing error state testing"
          - "No accessibility tests"
          - "Incomplete prop validation tests"
        recommendations:
          - "Add error boundary testing"
          - "Implement WCAG compliance tests"
          - "Test all prop combinations"
```

## Usage Examples

### Basic Project Analysis
```yaml
input:
  project_path: "/path/to/project"
  analysis_depth: "basic"
```

### Comprehensive Analysis
```yaml
input:
  project_path: "/path/to/project"
  analysis_depth: "comprehensive"
  include_dependencies: true
  analyze_performance: true
  check_accessibility: true
```

### Targeted Analysis
```yaml
input:
  project_path: "/path/to/project"
  focus_areas: ["coverage", "quality", "compliance"]
  exclude_patterns: ["node_modules", "dist", "build"]
```

This workflow provides comprehensive insights into project testing setup, identifies improvement opportunities, and generates actionable recommendations for achieving Hubtel testing standards.