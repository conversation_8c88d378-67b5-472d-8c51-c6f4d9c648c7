using Autofac.Core;
using FluentAssertions;
using Flurl.Http.Testing;
using Hubtel.Authentication.Commons.Models;
using Hubtel.Authentication.Core.Api.Data;
using Hubtel.Authentication.Core.Api.Data.Core;
using Hubtel.Authentication.Core.Api.Data.Entities;
using Hubtel.Authentication.Core.Api.Dtos;
using Hubtel.Authentication.Core.Api.Models;
using Hubtel.Authentication.Core.Api.Repositories.Interfaces;
using Hubtel.Authentication.Core.Api.Repositories.Provider;
using Hubtel.Authentication.Core.Api.Services;
using Hubtel.Authentication.Core.Api.Services.Interface;
using Hubtel.Redis.Sdk.Services;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using StackExchange.Redis;
using System.Linq.Expressions;

namespace Hubtel.Authentication.Core.Api.Tests.Services
{
    public class AuthenticationConfigStoreServiceTests : IDisposable
    {
        private readonly AuthenticationConfigStoreService _service;
        private readonly IMultiRedisHostCacheRepository _cacheRepository;
        private readonly IDatabase _memoryCacheService;
        private readonly ILogger<AuthenticationConfigStoreService> _logger;
        private readonly ApplicationDbContext _dbContext;
        private readonly IAuthConfigRepository<AuthenticationConfiguration> _authConfigRepository;
        private bool _disposed;

        public AuthenticationConfigStoreServiceTests()
        {
            string DatabaseName = Guid.NewGuid().ToString();
            var options = new DbContextOptionsBuilder<ApplicationDbContext>()
                .UseInMemoryDatabase(DatabaseName)
                .Options;
            _dbContext = new ApplicationDbContext(options);
            _authConfigRepository=new AuthConfigRepository(_dbContext);
            _cacheRepository = Substitute.For<IMultiRedisHostCacheRepository>();
            _memoryCacheService = Substitute.For<IDatabase>();
            _logger = Substitute.For<ILogger<AuthenticationConfigStoreService>>();
            _cacheRepository.GetDb(Arg.Any<string>(), Arg.Any<int>()).Returns(_memoryCacheService);
            _service = new AuthenticationConfigStoreService(_logger, _authConfigRepository, _cacheRepository);

        }

        [Fact]
        public async Task AddAsync_Should_ReturnSuccess_When_ValidInput()
        {

            // Arrange
            var request = new CreateAuthConfigStoreRequest
            {
                ProductName = "Example Product",
                ProductLogo = "https://example.com/logo.png",
                AppName = "ExampleApp",
                Primary = "#123456", // Hex code for the primary color
                SenderId = "ExampleSender123",
                BgLight = "#F1F1F1", // Hex code for a light background color
                HoverState = "#EFEFEF", // Hex code for hover state
                SigningKey = "**********abcdef**********abcdef", // 32 characters long
                ValidityPeriod = 365.25, // Valid for one year (including a day for leap year)
                CanSkipPhoneNumber = false, // Phone number is required
                CreatedBy = "<EMAIL>",
                AccountLookUpUrl = "https://example.com/account-lookup",
            };

            // Act
            var result = await _service.AddAsync<GetBackOfficeAuthConfigStoreResponse>(request);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("200");
                result.Data.Should().NotBeNull();
                result.Data.AppName.Should().Be("ExampleApp");
            }
        }

        [Fact]
        public async Task AddAsync_Should_ReturnBadRequest_When_SaveChangesFails()
        {
            var mockRepo = Substitute.For<IAuthConfigRepository<AuthenticationConfiguration>>();
            var service = new AuthenticationConfigStoreService(_logger, mockRepo, _cacheRepository);
            // Arrange
            var request = new CreateAuthConfigStoreRequest
            {
                ProductName = "Example Product",
                ProductLogo = "https://example.com/logo.png",
                AppName = "ExampleApp",
                Primary = "#123456", // Hex code for the primary color
                SenderId = "ExampleSender123",
                BgLight = "#F1F1F1", // Hex code for a light background color
                HoverState = "#EFEFEF", // Hex code for hover state
                SigningKey = "**********abcdef**********abcdef", // 32 characters long
                ValidityPeriod = 365.25, // Valid for one year (including a day for leap year)
                CanSkipPhoneNumber = false, // Phone number is required
                CreatedBy = "<EMAIL>",
                AccountLookUpUrl = "https://example.com/account-lookup",
            };

            mockRepo.AddConfigAsync(Arg.Any<AuthenticationConfiguration>(), default).Throws(new Exception());
            // Act
            var result = await service.AddAsync<GetBackOfficeAuthConfigStoreResponse>(request);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
                result.Message.Should().Be("An error occurred while adding a new authentication configuration");
            }
        }

        [Fact]
        public async Task AddAsync_Should_ReturnBadRequest_When_SaveChangesReturnsLessOrEqualToZero()
        {
            var mockRepo = Substitute.For<IAuthConfigRepository<AuthenticationConfiguration>>();
            var service = new AuthenticationConfigStoreService(_logger, mockRepo, _cacheRepository);
            // Arrange
            var request = new CreateAuthConfigStoreRequest
            {
                ProductName = "Example Product",
                ProductLogo = "https://example.com/logo.png",
                AppName = "ExampleApp",
                Primary = "#123456", // Hex code for the primary color
                SenderId = "ExampleSender123",
                BgLight = "#F1F1F1", // Hex code for a light background color
                HoverState = "#EFEFEF", // Hex code for hover state
                SigningKey = "**********abcdef**********abcdef", // 32 characters long
                ValidityPeriod = 365.25, // Valid for one year (including a day for leap year)
                CanSkipPhoneNumber = false, // Phone number is required
                CreatedBy = "<EMAIL>",
                AccountLookUpUrl = "https://example.com/account-lookup",
            };

           mockRepo.SaveChangesAsync(default).Returns(0);
            // Act
            var result = await service.AddAsync<GetBackOfficeAuthConfigStoreResponse>(request);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
                result.Message.Should().Be("Could not create authentication configuration! Please try again");
            }
        }

        [Fact]
        public async Task AddAsync_Should_ReturnOk_When_AppAlreadyNameExists()
        {
            // Arrange
            var request = new CreateAuthConfigStoreRequest
            {
                ProductName = "Example Product",
                ProductLogo = "https://example.com/logo.png",
                AppName = "TestApp",
                Primary = "#123456", // Hex code for the primary color
                SenderId = "ExampleSender123",
                BgLight = "#F1F1F1", // Hex code for a light background color
                HoverState = "#EFEFEF", // Hex code for hover state
                SigningKey = "**********abcdef**********abcdef", // 32 characters long
                ValidityPeriod = 365.25, // Valid for one year (including a day for leap year)
                CanSkipPhoneNumber = false, // Phone number is required
                CreatedBy = "<EMAIL>",
                AccountLookUpUrl = "https://example.com/account-lookup",
            };
            await _service.AddAsync<GetBackOfficeAuthConfigStoreResponse>(request);

            // Act
            var result = await _service.AddAsync<GetBackOfficeAuthConfigStoreResponse>(request);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("200");
                result.Data.AppName.Should().Be("TestApp");
            }
        }

        [Fact]
        public async Task DeleteAsync_Should_ReturnSuccess_When_ConfigExists()
        {
            // Arrange
            var request = new CreateAuthConfigStoreRequest
            {
                ProductName = "Example Product",
                ProductLogo = "https://example.com/logo.png",
                AppName = "TestApp",
                Primary = "#123456", // Hex code for the primary color
                SenderId = "ExampleSender123",
                BgLight = "#F1F1F1", // Hex code for a light background color
                HoverState = "#EFEFEF", // Hex code for hover state
                SigningKey = "**********abcdef**********abcdef", // 32 characters long
                ValidityPeriod = 365.25, // Valid for one year (including a day for leap year)
                CanSkipPhoneNumber = false, // Phone number is required
                CreatedBy = "<EMAIL>",
                AccountLookUpUrl = "https://example.com/account-lookup",
            };
            var addResult = await _service.AddAsync<GetBackOfficeAuthConfigStoreResponse>(request);
            var id = addResult.Data.Id;

            // Act
            var result = await _service.DeleteAsync(id);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("200");
                result.Data.Should().BeTrue();
            }
        }

        [Fact]
        public async Task DeleteAsync_Should_ReturnNotFound_When_ConfigDoesNotExist()
        {
            // Arrange
            var id = Guid.NewGuid();

            // Act
            var result = await _service.DeleteAsync(id);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("204");
                result.Message.Should().Be("Operation Succeeded");
            }
        }

        [Fact]
        public async Task DeleteAsync_Should_ReturnNotFound_When_DeletedAsyncReturnsLessThanOne()
        {
            // Arrange
            var id = Guid.NewGuid();
            var mockRepo = Substitute.For<IAuthConfigRepository<AuthenticationConfiguration>>();
            var service = new AuthenticationConfigStoreService(_logger, mockRepo, _cacheRepository);

            // Act
            mockRepo.DeleteAsync(Arg.Any<AuthenticationConfiguration>(), default).Returns(Task.FromResult(0));
            mockRepo.FindFirstOneAsync(Arg.Any<Expression<Func<AuthenticationConfiguration, bool>>>(), default).Returns(new AuthenticationConfiguration());
            var result = await service.DeleteAsync(id);


            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("404");
            }
        }

        [Fact]
        public async Task GetAsync_Should_ReturnPagedResult_When_ValidInput()
        {
            // Arrange

            var request = new CreateAuthConfigStoreRequest
            {
                ProductName = "Example Product",
                ProductLogo = "https://example.com/logo.png",
                AppName = "TestApp",
                Primary = "#123456", // Hex code for the primary color
                SenderId = "ExampleSender123",
                BgLight = "#F1F1F1", // Hex code for a light background color
                HoverState = "#EFEFEF", // Hex code for hover state
                SigningKey = "**********abcdef**********abcdef", // 32 characters long
                ValidityPeriod = 365.25, // Valid for one year (including a day for leap year)
                CanSkipPhoneNumber = false, // Phone number is required
                CreatedBy = "<EMAIL>",
                AccountLookUpUrl = "https://example.com/account-lookup",
            };
            await _service.AddAsync<GetBackOfficeAuthConfigStoreResponse>(request);
            var filter = new BaseFilter { Page = 1, PageSize = 10 };

            // Act
            var result = await _service.GetAsync<GetBackOfficeAuthConfigStoreResponse>(filter);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("200");
                result.Data.Results.Should().HaveCount(1);
                result.Data.Results[0].AppName.Should().Be("TestApp");
            }
        }

        [Fact]
        public async Task GetAsync_Should_ThrowException_When_ValidInputAndFilterProvided()
        {
            // Arrange

            var request = new CreateAuthConfigStoreRequest
            {
                ProductName = "Example Product",
                ProductLogo = "https://example.com/logo.png",
                AppName = "TestApp",
                Primary = "#123456", // Hex code for the primary color
                SenderId = "ExampleSender123",
                BgLight = "#F1F1F1", // Hex code for a light background color
                HoverState = "#EFEFEF", // Hex code for hover state
                SigningKey = "**********abcdef**********abcdef", // 32 characters long
                ValidityPeriod = 365.25, // Valid for one year (including a day for leap year)
                CanSkipPhoneNumber = false, // Phone number is required
                CreatedBy = "<EMAIL>",
                AccountLookUpUrl = "https://example.com/account-lookup",
            };
            await _service.AddAsync<GetBackOfficeAuthConfigStoreResponse>(request);
            var filter = new BaseFilter { Page = 1, PageSize = 10, SearchTerm = "App" };

            // Act
            var act = async () => await _service.GetAsync<GetBackOfficeAuthConfigStoreResponse>(filter);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                await act.Should().ThrowAsync<Exception>();
            }
        }

        [Fact]
        public async Task GetAsync_Should_ReturnConfig_When_ConfigExists()
        {
            // Arrange
            var request = new CreateAuthConfigStoreRequest
            {
                ProductName = "Example Product",
                ProductLogo = "https://example.com/logo.png",
                AppName = "TestApp",
                Primary = "#123456", // Hex code for the primary color
                SenderId = "ExampleSender123",
                BgLight = "#F1F1F1", // Hex code for a light background color
                HoverState = "#EFEFEF", // Hex code for hover state
                SigningKey = "**********abcdef**********abcdef", // 32 characters long
                ValidityPeriod = 365.25, // Valid for one year (including a day for leap year)
                CanSkipPhoneNumber = false, // Phone number is required
                CreatedBy = "<EMAIL>",
                AccountLookUpUrl = "https://example.com/account-lookup",
            };
            var addResult = await _service.AddAsync<GetBackOfficeAuthConfigStoreResponse>(request);
            var id = addResult.Data.Id;

            // Act
            var result = await _service.GetAsync<GetBackOfficeAuthConfigStoreResponse>(id);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Should().NotBeNull();
                result.AppName.Should().Be("TestApp");
            }
        }

        [Fact]
        public async Task GetAllPendingAsync_Should_ReturnPagedResult_When_ValidInput()
        {
            // Arrange
            var request = new CreateAuthConfigStoreRequest
            {
                ProductName = "Example Product",
                ProductLogo = "https://example.com/logo.png",
                AppName = "TestApp",
                Primary = "#123456", // Hex code for the primary color
                SenderId = "ExampleSender123",
                BgLight = "#F1F1F1", // Hex code for a light background color
                HoverState = "#EFEFEF", // Hex code for hover state
                SigningKey = "**********abcdef**********abcdef", // 32 characters long
                ValidityPeriod = 365.25, // Valid for one year (including a day for leap year)
                CanSkipPhoneNumber = false, // Phone number is required
                CreatedBy = "<EMAIL>",
                AccountLookUpUrl = "https://example.com/account-lookup",
            };
            await _service.AddAsync<GetBackOfficeAuthConfigStoreResponse>(request);
            var filter = new BaseFilter { Page = 1, PageSize = 10 };

            // Act
            var result = await _service.GetAllPendingAsync(filter, CancellationToken.None);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("200");
                result.Data.Results.Should().HaveCount(1);
                result.Data.Results[0].AppName.Should().Be("TestApp");
            }
        }

        [Fact]
        public async Task GetAllPendingAsync_Should_ReturnBadRequestResponse_When_ExceptionIsThrown()
        {
            // Arrange
            var mockRepo = Substitute.For<IAuthConfigRepository<AuthenticationConfiguration>>();
            var service = new AuthenticationConfigStoreService(_logger, mockRepo, _cacheRepository);
            var request = new CreateAuthConfigStoreRequest
            {
                ProductName = "Example Product",
                ProductLogo = "https://example.com/logo.png",
                AppName = "TestApp",
                Primary = "#123456", // Hex code for the primary color
                SenderId = "ExampleSender123",
                BgLight = "#F1F1F1", // Hex code for a light background color
                HoverState = "#EFEFEF", // Hex code for hover state
                SigningKey = "**********abcdef**********abcdef", // 32 characters long
                ValidityPeriod = 365.25, // Valid for one year (including a day for leap year)
                CanSkipPhoneNumber = false, // Phone number is required
                CreatedBy = "<EMAIL>",
                AccountLookUpUrl = "https://example.com/account-lookup",
            };
            mockRepo.GetQueryable().Throws(new Exception());
            var filter = new BaseFilter { Page = 1, PageSize = 10 };

            // Act
            var result = await service.GetAllPendingAsync(filter, CancellationToken.None);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
            }
        }

        [Fact]
        public async Task AddWhiteListedNumbersToAuthConfigAsync_Should_ReturnSuccess_When_ValidInput()
        {
            // Arrange
            var request = new CreateAuthConfigStoreRequest
            {
                ProductName = "Example Product",
                ProductLogo = "https://example.com/logo.png",
                AppName = "TestApp",
                Primary = "#123456", // Hex code for the primary color
                SenderId = "ExampleSender123",
                BgLight = "#F1F1F1", // Hex code for a light background color
                HoverState = "#EFEFEF", // Hex code for hover state
                SigningKey = "**********abcdef**********abcdef", // 32 characters long
                ValidityPeriod = 365.25, // Valid for one year (including a day for leap year)
                CanSkipPhoneNumber = false, // Phone number is required
                CreatedBy = "<EMAIL>",
                AccountLookUpUrl = "https://example.com/account-lookup",
            };
            var addResult = await _service.AddAsync<GetBackOfficeAuthConfigStoreResponse>(request);
            var id = addResult.Data.Id;
            var additions = new List<WhiteListedNumber> { new WhiteListedNumber { PhoneNumber = "**********" } };

            // Act
            var result = await _service.AddWhiteListedNumbersToAuthConfigAsync(id, additions, CancellationToken.None);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("200");
                result.Data.Should().HaveCount(1);
                result.Data[0].PhoneNumber.Should().Be("**********");
            }
        }

        [Fact]
        public async Task AddWhiteListedNumbersToAuthConfigAsync_Should_ReturnBadRequest_When_AdditionIsEmpty()
        {
            // Arrange
            var request = new CreateAuthConfigStoreRequest
            {
                ProductName = "Example Product",
                ProductLogo = "https://example.com/logo.png",
                AppName = "TestApp",
                Primary = "#123456", // Hex code for the primary color
                SenderId = "ExampleSender123",
                BgLight = "#F1F1F1", // Hex code for a light background color
                HoverState = "#EFEFEF", // Hex code for hover state
                SigningKey = "**********abcdef**********abcdef", // 32 characters long
                ValidityPeriod = 365.25, // Valid for one year (including a day for leap year)
                CanSkipPhoneNumber = false, // Phone number is required
                CreatedBy = "<EMAIL>",
                AccountLookUpUrl = "https://example.com/account-lookup",
            };
            var addResult = await _service.AddAsync<GetBackOfficeAuthConfigStoreResponse>(request);
            var id = addResult.Data.Id;
            var additions = new List<WhiteListedNumber> {  };

            // Act
            var result = await _service.AddWhiteListedNumbersToAuthConfigAsync(id, additions, CancellationToken.None);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
            }
        }

        [Fact]
        public async Task AddWhiteListedNumbersToAuthConfigAsync_Should_ReturnNotFound_When_AuthConfigIsNull()
        {
         
            // Arrange
            var request = new CreateAuthConfigStoreRequest
            {
                ProductName = "Example Product",
                ProductLogo = "https://example.com/logo.png",
                AppName = "TestApp",
                Primary = "#123456", // Hex code for the primary color
                SenderId = "ExampleSender123",
                BgLight = "#F1F1F1", // Hex code for a light background color
                HoverState = "#EFEFEF", // Hex code for hover state
                SigningKey = "**********abcdef**********abcdef", // 32 characters long
                ValidityPeriod = 365.25, // Valid for one year (including a day for leap year)
                CanSkipPhoneNumber = false, // Phone number is required
                CreatedBy = "<EMAIL>",
                AccountLookUpUrl = "https://example.com/account-lookup",
            };
            var addResult = await _service.AddAsync<GetBackOfficeAuthConfigStoreResponse>(request);
            var additions = new List<WhiteListedNumber> { new WhiteListedNumber { PhoneNumber = "2**********" } };

            // Act
            var result = await _service.AddWhiteListedNumbersToAuthConfigAsync(Guid.NewGuid(), additions, CancellationToken.None);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("404");
            }
        }

        [Fact]
        public async Task RemoveWhiteListedNumbersFromAuthConfigAsync_Should_ReturnSuccess_When_ValidInput()
        {
            // Arrange
            var request = new CreateAuthConfigStoreRequest
            {
                ProductName = "Example Product",
                ProductLogo = "https://example.com/logo.png",
                AppName = "TestApp",
                Primary = "#123456", // Hex code for the primary color
                SenderId = "ExampleSender123",
                BgLight = "#F1F1F1", // Hex code for a light background color
                HoverState = "#EFEFEF", // Hex code for hover state
                SigningKey = "**********abcdef**********abcdef", // 32 characters long
                ValidityPeriod = 365.25, // Valid for one year (including a day for leap year)
                CanSkipPhoneNumber = false, // Phone number is required
                CreatedBy = "<EMAIL>",
                AccountLookUpUrl = "https://example.com/account-lookup",
            };
            var addResult = await _service.AddAsync<GetBackOfficeAuthConfigStoreResponse>(request);
            var id = addResult.Data.Id;
            var additions = new List<WhiteListedNumber> { new WhiteListedNumber { PhoneNumber = "**********" } };
            await _service.AddWhiteListedNumbersToAuthConfigAsync(id, additions, CancellationToken.None);
            var removals = new List<string> { "**********" };

            // Act
            var result = await _service.RemoveWhiteListedNumbersFromAuthConfigAsync(id, removals, CancellationToken.None);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("200");
                result.Data.Should().BeTrue();
            }
        }

        [Fact]
        public async Task RemoveWhiteListedNumbersFromAuthConfigAsync_Should_ReturnBadRequest_When_ExceptionIsThrown()
        {
            // Arrange
            var mockRepo=Substitute.For<IAuthConfigRepository<AuthenticationConfiguration>>();
            var service = new AuthenticationConfigStoreService(_logger, mockRepo, _cacheRepository);
            var request = new CreateAuthConfigStoreRequest
            {
                ProductName = "Example Product",
                ProductLogo = "https://example.com/logo.png",
                AppName = "TestApp",
                Primary = "#123456", // Hex code for the primary color
                SenderId = "ExampleSender123",
                BgLight = "#F1F1F1", // Hex code for a light background color
                HoverState = "#EFEFEF", // Hex code for hover state
                SigningKey = "**********abcdef**********abcdef", // 32 characters long
                ValidityPeriod = 365.25, // Valid for one year (including a day for leap year)
                CanSkipPhoneNumber = false, // Phone number is required
                CreatedBy = "<EMAIL>",
                AccountLookUpUrl = "https://example.com/account-lookup",
            };
            mockRepo.GetQueryable().Throws(new Exception());
            var removals = new List<string> { "**********" };
            
            // Act
            var result = await service.RemoveWhiteListedNumbersFromAuthConfigAsync(Guid.NewGuid(), removals, CancellationToken.None);
            
            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("500");
            }
        }

        [Fact]
        public async Task RemoveWhiteListedNumbersFromAuthConfigAsync_Should_ReturnBadRequest_When_RemovalsIsZero()
        {
            // Arrange
            var request = new CreateAuthConfigStoreRequest
            {
                ProductName = "Example Product",
                ProductLogo = "https://example.com/logo.png",
                AppName = "TestApp",
                Primary = "#123456", // Hex code for the primary color
                SenderId = "ExampleSender123",
                BgLight = "#F1F1F1", // Hex code for a light background color
                HoverState = "#EFEFEF", // Hex code for hover state
                SigningKey = "**********abcdef**********abcdef", // 32 characters long
                ValidityPeriod = 365.25, // Valid for one year (including a day for leap year)
                CanSkipPhoneNumber = false, // Phone number is required
                CreatedBy = "<EMAIL>",
                AccountLookUpUrl = "https://example.com/account-lookup",
            };
            var removals = new List<string> { };

            // Act
            var result = await _service.RemoveWhiteListedNumbersFromAuthConfigAsync(Guid.NewGuid(), removals, CancellationToken.None);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
            }
        }

        [Fact]
        public async Task RemoveWhiteListedNumbersFromAuthConfigAsync_Should_ReturnNotFound_When_WhiteListIsNullOrZero()
        {
            // Arrange
            var request = new CreateAuthConfigStoreRequest
            {
                ProductName = "Example Product",
                ProductLogo = "https://example.com/logo.png",
                AppName = "TestApp",
                Primary = "#123456", // Hex code for the primary color
                SenderId = "ExampleSender123",
                BgLight = "#F1F1F1", // Hex code for a light background color
                HoverState = "#EFEFEF", // Hex code for hover state
                SigningKey = "**********abcdef**********abcdef", // 32 characters long
                ValidityPeriod = 365.25, // Valid for one year (including a day for leap year)
                CanSkipPhoneNumber = false, // Phone number is required
                CreatedBy = "<EMAIL>",
                AccountLookUpUrl = "https://example.com/account-lookup",
            };
            var removals = new List<string> { "***********"};

            // Act
            var result = await _service.RemoveWhiteListedNumbersFromAuthConfigAsync(Guid.NewGuid(), removals, CancellationToken.None);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("404");
            }
        }

        //[Fact]
        //public async Task GetAllAuthenticationWhitelistsAsync_Should_ReturnOK_When_ObjectExistsInCache()
        //{
        //    // Arrange
        //    var request = new CreateAuthConfigStoreRequest
        //    {
        //        ProductName = "Example Product",
        //        ProductLogo = "https://example.com/logo.png",
        //        AppName = "TestApp",
        //        Primary = "#123456", // Hex code for the primary color
        //        SenderId = "ExampleSender123",
        //        BgLight = "#F1F1F1", // Hex code for a light background color
        //        HoverState = "#EFEFEF", // Hex code for hover state
        //        SigningKey = "**********abcdef**********abcdef", // 32 characters long
        //        ValidityPeriod = 365.25, // Valid for one year (including a day for leap year)
        //        CanSkipPhoneNumber = false, // Phone number is required
        //        CreatedBy = "<EMAIL>",
        //        AccountLookUpUrl = "https://example.com/account-lookup",
        //    };
        //    var authRedisResponse = new AuthenticationConfiguration()
        //    {
        //        AccountLookUpUrl = "https://example.com/account-lookup",
        //    };
        //    _memoryCacheService.StringGetAsync(Arg.Any<string>()).Returns((RedisValue)JsonConvert.SerializeObject(authRedisResponse));   
        //    var addResult = await _service.AddAsync<GetBackOfficeAuthConfigStoreResponse>(request);
        //    var id = addResult.Data.Id;
        //    var additions = new List<WhiteListedNumber> { new WhiteListedNumber { PhoneNumber = "**********" } };

        //    // Act
        //    var result = await _service.GetAllAuthenticationWhitelistsAsync(id, CancellationToken.None);

        //    // Assert
        //    using (new FluentAssertions.Execution.AssertionScope())
        //    {
        //        result.Code.Should().Be("200");
        //        result.Data.Should().HaveCount(1);
        //        result.Data[0].PhoneNumber.Should().Be("**********");
        //    }
        //}

        [Fact]
        public async Task GetAllAuthenticationWhitelistsAsync_Should_ReturnWhitelists_When_ConfigExists()
        {
            // Arrange
            var request = new CreateAuthConfigStoreRequest
            {
                ProductName = "Example Product",
                ProductLogo = "https://example.com/logo.png",
                AppName = "TestApp",
                Primary = "#123456", // Hex code for the primary color
                SenderId = "ExampleSender123",
                BgLight = "#F1F1F1", // Hex code for a light background color
                HoverState = "#EFEFEF", // Hex code for hover state
                SigningKey = "**********abcdef**********abcdef", // 32 characters long
                ValidityPeriod = 365.25, // Valid for one year (including a day for leap year)
                CanSkipPhoneNumber = false, // Phone number is required
                CreatedBy = "<EMAIL>",
                AccountLookUpUrl = "https://example.com/account-lookup",
            };
            var addResult = await _service.AddAsync<GetBackOfficeAuthConfigStoreResponse>(request);
            var id = addResult.Data.Id;
            var additions = new List<WhiteListedNumber> { new WhiteListedNumber { PhoneNumber = "**********" } };
            await _service.AddWhiteListedNumbersToAuthConfigAsync(id, additions, CancellationToken.None);

            // Act
            var result = await _service.GetAllAuthenticationWhitelistsAsync(id, CancellationToken.None);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("200");
                result.Data.Should().HaveCount(1);
                result.Data[0].PhoneNumber.Should().Be("**********");
            }
        }

        [Fact]
        public async Task GetCachedClientAsync_Should_ReturnConfig_When_ConfigExists()
        {
            // Arrange
            var request = new CreateAuthConfigStoreRequest
            {
                ProductName = "Example Product",
                ProductLogo = "https://example.com/logo.png",
                AppName = "TestApp",
                Primary = "#123456", // Hex code for the primary color
                SenderId = "ExampleSender123",
                BgLight = "#F1F1F1", // Hex code for a light background color
                HoverState = "#EFEFEF", // Hex code for hover state
                SigningKey = "**********abcdef**********abcdef", // 32 characters long
                ValidityPeriod = 365.25, // Valid for one year (including a day for leap year)
                CanSkipPhoneNumber = false, // Phone number is required
                CreatedBy = "<EMAIL>",
                AccountLookUpUrl = "https://example.com/account-lookup",
            };
            var addResult = await _service.AddAsync<GetBackOfficeAuthConfigStoreResponse>(request);
            var id = addResult.Data.Id;

            // Act
            var result = await _service.GetCachedClientAsync<GetBackOfficeAuthConfigStoreResponse>(id);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Should().NotBeNull();
                result.AppName.Should().Be("TestApp");
            }
        }

        [Fact]
        public async Task GetCachedClientAsync_Should_ReturnNull_When_ExceptionOccurs()
        {
            // Arrange
            var service = new AuthenticationConfigStoreService(_logger, _authConfigRepository, _cacheRepository);
            var id = Guid.NewGuid();

            // Act
            var result = await service.GetCachedClientAsync<GetBackOfficeAuthConfigStoreResponse>(id);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Should().BeNull();
            }
        }

        [Fact]
        public async Task UpdateAsync_Should_ReturnSuccess_When_ValidInput()
        {
            // Arrange
            var request = new CreateAuthConfigStoreRequest
            {
                ProductName = "Example Product",
                ProductLogo = "https://example.com/logo.png",
                AppName = "TestApp",
                Primary = "#123456", // Hex code for the primary color
                SenderId = "ExampleSender123",
                BgLight = "#F1F1F1", // Hex code for a light background color
                HoverState = "#EFEFEF", // Hex code for hover state
                SigningKey = "**********abcdef**********abcdef", // 32 characters long
                ValidityPeriod = 365.25, // Valid for one year (including a day for leap year)
                CanSkipPhoneNumber = false, // Phone number is required
                CreatedBy = "<EMAIL>",
                AccountLookUpUrl = "https://example.com/account-lookup",
            };
            var addResult = await _service.AddAsync<GetBackOfficeAuthConfigStoreResponse>(request);
            var id = addResult.Data.Id;
            var updateRequest = new UpdateAuthConfigStoreRequest { AppName = "UpdatedApp" };

            // Act
            var (isSaved, data) = await _service.UpdateAsync<GetBackOfficeAuthConfigStoreResponse>(id, updateRequest);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                isSaved.Should().BeTrue();
                data.Should().NotBeNull();
                data.AppName.Should().Be("UpdatedApp");
            }
        }

        [Fact]
        public async Task UpdateAsync_Should_ReturnFalse_When_AppConfigIdDoesNotExist()
        {
            // Arrange
            var id = Guid.NewGuid();
            var updateRequest = new UpdateAuthConfigStoreRequest { AppName = "UpdatedApp" };

            // Act
            var (isSaved, data) = await _service.UpdateAsync<GetBackOfficeAuthConfigStoreResponse>(id, updateRequest);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                isSaved.Should().BeFalse();
                data.Should().BeNull();
            }
        }

        [Fact]
        public async Task UpdateAsync_Should_ReturnFalse_When_ExceptionIsThrown()
        {
            // Arrange
            var service = new AuthenticationConfigStoreService(_logger, _authConfigRepository, _cacheRepository);
            var id = Guid.NewGuid();
            var updateRequest = new UpdateAuthConfigStoreRequest { AppName = "UpdatedApp" };

            // Act
            var (isSaved, data) = await service.UpdateAsync<GetBackOfficeAuthConfigStoreResponse>(id, updateRequest);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                isSaved.Should().BeFalse();
                data.Should().BeNull();
            }
        }

        [Fact]
        public async Task PartialUpdateAsync_Should_ReturnSuccess_When_ValidInput()
        {
            // Arrange
            var request = new CreateAuthConfigStoreRequest
            {
                ProductName = "Example Product",
                ProductLogo = "https://example.com/logo.png",
                AppName = "TestApp",
                Primary = "#123456", // Hex code for the primary color
                SenderId = "ExampleSender123",
                BgLight = "#F1F1F1", // Hex code for a light background color
                HoverState = "#EFEFEF", // Hex code for hover state
                SigningKey = "**********abcdef**********abcdef", // 32 characters long
                ValidityPeriod = 365.25, // Valid for one year (including a day for leap year)
                CanSkipPhoneNumber = false, // Phone number is required
                CreatedBy = "<EMAIL>",
                AccountLookUpUrl = "https://example.com/account-lookup",
            };
            var addResult = await _service.AddAsync<GetBackOfficeAuthConfigStoreResponse>(request);
            var id = addResult.Data.Id;
            var patchDoc = new JsonPatchDocument<AuthenticationConfiguration>();
            patchDoc.Replace(x => x.AppName, "UpdatedApp");

            // Act
            var (isSaved, data) = await _service.PartialUpdateAsync<GetBackOfficeAuthConfigStoreResponse>(id, patchDoc);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                isSaved.Should().BeTrue();
                data.Should().NotBeNull();
                data.AppName.Should().Be("UpdatedApp");
            }
        }

        [Fact]
        public async Task PartialUpdateAsync_Should_ReturnNull_When_ExceptionIsThrown()
        {
            // Arrange
            var service = new AuthenticationConfigStoreService(_logger, _authConfigRepository, _cacheRepository);
            var id = Guid.NewGuid();
            var patchDoc = new JsonPatchDocument<AuthenticationConfiguration>();
            patchDoc.Replace(x => x.AppName, "UpdatedApp");

            // Act
            var (isSaved, data) = await service.PartialUpdateAsync<GetBackOfficeAuthConfigStoreResponse>(id, patchDoc);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                isSaved.Should().BeFalse();
                data.Should().BeNull();
            }
        }

        [Fact]
        public async Task GetAllAuthenticationChannelsAsync_Should_ReturnChannels_When_ConfigExists()
        {
            // Arrange
            var request = new CreateAuthConfigStoreRequest
            {
                ProductName = "Example Product",
                ProductLogo = "https://example.com/logo.png",
                AppName = "TestApp",
                Primary = "#123456", // Hex code for the primary color
                SenderId = "ExampleSender123",
                BgLight = "#F1F1F1", // Hex code for a light background color
                HoverState = "#EFEFEF", // Hex code for hover state
                SigningKey = "**********abcdef**********abcdef", // 32 characters long
                ValidityPeriod = 365.25, // Valid for one year (including a day for leap year)
                CanSkipPhoneNumber = false, // Phone number is required
                CreatedBy = "<EMAIL>",
                AccountLookUpUrl = "https://example.com/account-lookup",
            };
            var addResult = await _service.AddAsync<GetBackOfficeAuthConfigStoreResponse>(request);
            var id = addResult.Data.Id;
            var config = await _dbContext.AuthenticationConfigurations.FindAsync(id);
            config?.AddAuthenticationChannels(new List<SelectedAuthChannel> { new SelectedAuthChannel(AuthenticationChannel.Email) });
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await _service.GetAllAuthenticationChannelsAsync(id, CancellationToken.None);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("200");
                result.Data.Should().HaveCount(2);
                result.Data[0].Should().Be(AuthenticationChannel.Ussd);
                result.Data[1].Should().Be(AuthenticationChannel.Email);
            }
        }

        [Fact]
        public async Task AddAuthenticationChannelsAsync_Should_ReturnSuccess_When_ValidInput()
        {
            // Arrange
            var request = new CreateAuthConfigStoreRequest
            {
                ProductName = "Example Product",
                ProductLogo = "https://example.com/logo.png",
                AppName = "TestApp",
                Primary = "#123456", // Hex code for the primary color
                SenderId = "ExampleSender123",
                BgLight = "#F1F1F1", // Hex code for a light background color
                HoverState = "#EFEFEF", // Hex code for hover state
                SigningKey = "**********abcdef**********abcdef", // 32 characters long
                ValidityPeriod = 365.25, // Valid for one year (including a day for leap year)
                CanSkipPhoneNumber = false, // Phone number is required
                CreatedBy = "<EMAIL>",
                AccountLookUpUrl = "https://example.com/account-lookup",
            };
            var addResult = await _service.AddAsync<GetBackOfficeAuthConfigStoreResponse>(request);
            var id = addResult.Data.Id;
            var channels = new List<AuthenticationChannel> { AuthenticationChannel.Email };

            // Act
            var result = await _service.AddAuthenticationChannelsAsync(id, channels, CancellationToken.None);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("200");
                result.Data.Should().HaveCount(1);
                result.Data[0].Should().Be(AuthenticationChannel.Email);
            }
        }

        [Fact]
        public async Task RemoveAuthenticationChannelsAsync_Should_ReturnSuccess_When_ValidInput()
        {
            // Arrange
            var request = new CreateAuthConfigStoreRequest
            {
                ProductName = "Example Product",
                ProductLogo = "https://example.com/logo.png",
                AppName = "TestApp",
                Primary = "#123456", // Hex code for the primary color
                SenderId = "ExampleSender123",
                BgLight = "#F1F1F1", // Hex code for a light background color
                HoverState = "#EFEFEF", // Hex code for hover state
                SigningKey = "**********abcdef**********abcdef", // 32 characters long
                ValidityPeriod = 365.25, // Valid for one year (including a day for leap year)
                CanSkipPhoneNumber = false, // Phone number is required
                CreatedBy = "<EMAIL>",
                AccountLookUpUrl = "https://example.com/account-lookup",
            };
            var addResult = await _service.AddAsync<GetBackOfficeAuthConfigStoreResponse>(request);
            var id = addResult.Data.Id;
            var channels = new List<AuthenticationChannel> { AuthenticationChannel.Email };
            var config = await _dbContext.AuthenticationConfigurations.FindAsync(id);
            config?.AddAuthenticationChannels(new List<SelectedAuthChannel> { new SelectedAuthChannel(AuthenticationChannel.Email) });
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await _service.RemoveAuthenticationChannelsAsync(id, channels, CancellationToken.None);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("200");
                result.Data.Should().BeTrue();
            }
        }

        [Fact]
        public async Task GetExternalLoginConfigurationAsync_Should_ReturnExternalLogins_When_ConfigExists()
        {
            // Arrange
            var request = new CreateAuthConfigStoreRequest
            {
                ProductName = "Example Product",
                ProductLogo = "https://example.com/logo.png",
                AppName = "TestApp",
                Primary = "#123456", // Hex code for the primary color
                SenderId = "ExampleSender123",
                BgLight = "#F1F1F1", // Hex code for a light background color
                HoverState = "#EFEFEF", // Hex code for hover state
                SigningKey = "**********abcdef**********abcdef", // 32 characters long
                ValidityPeriod = 365.25, // Valid for one year (including a day for leap year)
                CanSkipPhoneNumber = false, // Phone number is required
                CreatedBy = "<EMAIL>",
                AccountLookUpUrl = "https://example.com/account-lookup",
            };
            var addResult = await _service.AddAsync<GetBackOfficeAuthConfigStoreResponse>(request);
            var id = addResult.Data.Id;
            var config = await _dbContext.AuthenticationConfigurations.FindAsync(id);
            config?.AddExternalLoginConfigurations(new List<ExternalLoginConfiguration> { new ExternalLoginConfiguration { Type = ExternalAuthenticationType.Google } });
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await _service.GetExternalLoginConfigurationAsync(id, CancellationToken.None);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("200");
                result.Data.Should().HaveCount(1);
                result.Data[0].Type.Should().Be(ExternalAuthenticationType.Google);
            }
        }

        [Fact]
        public async Task AddExternalLoginConfiguration_Should_ReturnSuccess_When_ValidInput()
        {
            // Arrange
            var request = new CreateAuthConfigStoreRequest
            {
                ProductName = "Example Product",
                ProductLogo = "https://example.com/logo.png",
                AppName = "TestApp",
                Primary = "#123456", // Hex code for the primary color
                SenderId = "ExampleSender123",
                BgLight = "#F1F1F1", // Hex code for a light background color
                HoverState = "#EFEFEF", // Hex code for hover state
                SigningKey = "**********abcdef**********abcdef", // 32 characters long
                ValidityPeriod = 365.25, // Valid for one year (including a day for leap year)
                CanSkipPhoneNumber = false, // Phone number is required
                CreatedBy = "<EMAIL>",
                AccountLookUpUrl = "https://example.com/account-lookup",
            };
            var addResult = await _service.AddAsync<GetBackOfficeAuthConfigStoreResponse>(request);
            var id = addResult.Data.Id;
            var externalLogins = new List<ExternalLoginConfiguration> { new ExternalLoginConfiguration { Type = ExternalAuthenticationType.Google } };

            // Act
            var result = await _service.AddExternalLoginConfiguration(id, externalLogins, CancellationToken.None);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("200");
                result.Data.Should().HaveCount(1);
                result.Data[0].Type.Should().Be(ExternalAuthenticationType.Google);
            }
        }

        [Fact]
        public async Task AddExternalLoginConfiguration_Should_ReturnBadRequest_When_ExceptionOccurs()
        {
            // Arrange
            var mockRepo = Substitute.For<IAuthConfigRepository<AuthenticationConfiguration>>();
            var service = new AuthenticationConfigStoreService(_logger, mockRepo, _cacheRepository);
            var request = new CreateAuthConfigStoreRequest
            {
                ProductName = "Example Product",
                ProductLogo = "https://example.com/logo.png",
                AppName = "TestApp",
                Primary = "#123456", // Hex code for the primary color
                SenderId = "ExampleSender123",
                BgLight = "#F1F1F1", // Hex code for a light background color
                HoverState = "#EFEFEF", // Hex code for hover state
                SigningKey = "**********abcdef**********abcdef", // 32 characters long
                ValidityPeriod = 365.25, // Valid for one year (including a day for leap year)
                CanSkipPhoneNumber = false, // Phone number is required
                CreatedBy = "<EMAIL>",
                AccountLookUpUrl = "https://example.com/account-lookup",
            };

            mockRepo.GetQueryable().Throws(new Exception());
            var externalLogins = new List<ExternalLoginConfiguration> { new ExternalLoginConfiguration { Type = ExternalAuthenticationType.Google } };

            // Act
            var result = await service.AddExternalLoginConfiguration(Guid.NewGuid(), externalLogins, CancellationToken.None);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
            }
        }

        [Fact]
        public async Task RemoveExternalLoginsAsync_Should_ReturnSuccess_When_ValidInput()
        {
            // Arrange
            var request = new CreateAuthConfigStoreRequest
            {
                ProductName = "Example Product",
                ProductLogo = "https://example.com/logo.png",
                AppName = "TestApp",
                Primary = "#123456", // Hex code for the primary color
                SenderId = "ExampleSender123",
                BgLight = "#F1F1F1", // Hex code for a light background color
                HoverState = "#EFEFEF", // Hex code for hover state
                SigningKey = "**********abcdef**********abcdef", // 32 characters long
                ValidityPeriod = 365.25, // Valid for one year (including a day for leap year)
                CanSkipPhoneNumber = false, // Phone number is required
                CreatedBy = "<EMAIL>",
                AccountLookUpUrl = "https://example.com/account-lookup",
            };
            var addResult = await _service.AddAsync<GetBackOfficeAuthConfigStoreResponse>(request);
            var id = addResult.Data.Id;
            var externalLogins = new List<ExternalLoginConfiguration> { new ExternalLoginConfiguration { Type = ExternalAuthenticationType.Google } };
            var config = await _dbContext.AuthenticationConfigurations.FindAsync(id);
            config?.AddExternalLoginConfigurations(externalLogins);
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await _service.RemoveExternalLoginsAsync(id, externalLogins, CancellationToken.None);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("200");
                result.Data.Should().BeTrue();
            }
        }

        [Fact]
        public async Task ApproveAuthConfiguration_Should_ReturnSuccess_When_ConfigExists()
        {
            // Arrange
            var request = new CreateAuthConfigStoreRequest
            {
                ProductName = "Example Product",
                ProductLogo = "https://example.com/logo.png",
                AppName = "TestApp",
                Primary = "#123456", // Hex code for the primary color
                SenderId = "ExampleSender123",
                BgLight = "#F1F1F1", // Hex code for a light background color
                HoverState = "#EFEFEF", // Hex code for hover state
                SigningKey = "**********abcdef**********abcdef", // 32 characters long
                ValidityPeriod = 365.25, // Valid for one year (including a day for leap year)
                CanSkipPhoneNumber = false, // Phone number is required
                CreatedBy = "<EMAIL>",
                AccountLookUpUrl = "https://example.com/account-lookup",
            };
            var addResult = await _service.AddAsync<GetBackOfficeAuthConfigStoreResponse>(request);
            var id = addResult.Data.Id;

            // Act
            var result = await _service.ApproveAuthConfiguration(id);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("200");
                result.Data.Should().BeTrue();
            }
        }

        [Fact]
        public async Task UpdateAllowedUrls_Should_ReturnSuccess_When_ValidInput()
        {
            // Arrange
            var request = new CreateAuthConfigStoreRequest
            {
                ProductName = "Example Product",
                ProductLogo = "https://example.com/logo.png",
                AppName = "TestApp",
                Primary = "#123456",
                SenderId = "ExampleSender123",
                BgLight = "#F1F1F1",
                HoverState = "#EFEFEF",
                SigningKey = "**********abcdef**********abcdef",
                ValidityPeriod = 365.25,
                CanSkipPhoneNumber = false,
                CreatedBy = "<EMAIL>",
                AccountLookUpUrl = "https://example.com/account-lookup",
            };
            var addResult = await _service.AddAsync<GetBackOfficeAuthConfigStoreResponse>(request);
            var id = addResult.Data.Id;
            var allowedUrls = new List<string> { "https://redirect1.com", "https://redirect2.com" };

            // Act
            var result = await _service.UpdateAllowedUrls(id, allowedUrls, CancellationToken.None);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("200");
                result.Data.Should().BeTrue();

                // Optionally verify the URLs were updated in the db
                var config = await _dbContext.AuthenticationConfigurations.FindAsync(id);
                config!.AllowedRedirectUrls.Should().BeEquivalentTo(allowedUrls);
            }
        }

        [Fact]
        public async Task UpdateAllowedUrls_Should_ReturnNotFound_When_ConfigDoesNotExist()
        {
            // Arrange
            var id = Guid.NewGuid();
            var allowedUrls = new List<string> { "https://redirect1.com" };

            // Act
            var result = await _service.UpdateAllowedUrls(id, allowedUrls, CancellationToken.None);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("404");
                result.Data.Should().BeFalse();
            }
        }


        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _dbContext?.Dispose();
                }

                _disposed = true;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
       
    }
}

