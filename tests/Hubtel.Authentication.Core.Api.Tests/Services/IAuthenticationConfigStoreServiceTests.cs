using FluentAssertions;
using Hubtel.Authentication.Core.Api.Dtos;
using Hubtel.Authentication.Core.Api.Models;
using Hubtel.Authentication.Core.Api.Services;
using NSubstitute;

namespace Hubtel.Authentication.Core.Api.Tests.Services
{
    public class IAuthenticationConfigStoreServiceTests
    {
        private readonly IAuthenticationConfigStoreService _service = Substitute.For<IAuthenticationConfigStoreService>();

        [Fact]
        public async Task AddAsync_Should_AddConfig_When_ValidRequest()
        {
            // Arrange
            var request = new CreateAuthConfigStoreRequest();
            _service.AddAsync<GetAuthConfigStoreResponse>(request).Returns(Task.FromResult(new ApiResponse<GetAuthConfigStoreResponse>()));

            // Act
            var result = await _service.AddAsync<GetAuthConfigStoreResponse>(request);

            // Assert
            result.Should().BeOfType<ApiResponse<GetAuthConfigStoreResponse>>();
        }

        [Fact]
        public async Task DeleteAsync_Should_DeleteConfig_When_ValidId()
        {
            // Arrange
            Guid id = Guid.NewGuid();
            _service.DeleteAsync(id).Returns(Task.FromResult(new ApiResponse<bool>()));

            // Act
            var result = await _service.DeleteAsync(id);

            // Assert
            result.Should().BeOfType<ApiResponse<bool>>();
        }

        [Fact]
        public async Task GetAsync_Should_GetPageResult_When_ValidFilter()
        {
            // Arrange
            var filter = new BaseFilter();
            CancellationToken ct = default(CancellationToken);
            _service.GetAsync<GetAuthConfigStoreResponse>(filter, ct)
                .Returns(Task.FromResult(new ApiResponse<PagedResult<GetAuthConfigStoreResponse>>()));

            // Act
            var result = await _service.GetAsync<GetAuthConfigStoreResponse>(filter, ct);

            // Assert
            result.Should().BeOfType<ApiResponse<PagedResult<GetAuthConfigStoreResponse>>>();
        }
    }
}
