using System;
using System.Collections.Generic;
using System.Threading.Channels;
using System.Threading.Tasks;
using FluentAssertions;
using Flurl.Http;
using Hubtel.Authentication.Commons.Models.Fraud;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using NSubstitute;
using Xunit;

namespace Hubtel.Authentication.Core.Api.Middlewares.Tests
{
    public class LoginEventsMiddlewareTests
    {
        private readonly Channel<FraudReport> _fraudReportChannel;
        private readonly ILogger<LoginEventsMiddleware> _logger;
        private readonly List<string> _excludedPaths;
        private readonly LoginEventsMiddleware _middleware;

        public LoginEventsMiddlewareTests()
        {
            _fraudReportChannel = Channel.CreateUnbounded<FraudReport>();
            _logger = Substitute.For<ILogger<LoginEventsMiddleware>>();
            _excludedPaths = new List<string> { "/excluded-path" };
            var options =Microsoft.Extensions.Options.Options.Create(_excludedPaths);
            var configuration = Substitute.For<IConfiguration>();

            _middleware = new LoginEventsMiddleware(_fraudReportChannel, options, configuration, _logger);
        }

        [Fact]
        public async Task InvokeAsync_Should_LogAndWriteFraudReport_When_PathIsNotExcluded()
        {
            // Arrange
            var context = new DefaultHttpContext();
            context.Request.Path = "/not-excluded-path";
            context.Request.Headers["X-Phone-Number"] = "1234567890";
            context.Request.Headers["X-App-Name"] = "TestApp";
            context.Request.Headers["x-Fingerprint-Data"] = "fingerprint";
            context.Request.Headers["X-App-Id"] = "appId";
            context.Request.Headers["X-Ip-Address"] = "127.0.0.1";
            var next = Substitute.For<RequestDelegate>();

            // Act
            await _middleware.InvokeAsync(context, next);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                _logger.Received().Log<object>(LogLevel.Debug,0,Arg.Any<object>(),Arg.Any<Exception>(),Arg.Any<Func<object, Exception, string>>()!);
                _fraudReportChannel.Reader.TryRead(out var fraudReport).Should().BeTrue();
                fraudReport?.PhoneNumber.Should().Be("1234567890");
                fraudReport?.Source.Should().Be("TestApp");
                fraudReport?.FingerPrintData.Should().Be("fingerprint");
                fraudReport?.App.Should().Be("appId");
                fraudReport?.IpAddress.Should().Be("127.0.0.1");
            }
        }

        [Fact]
        public async Task InvokeAsync_Should_NotLogAndWriteFraudReport_When_PathIsExcluded()
        {
            // Arrange
            var context = new DefaultHttpContext();
            context.Request.Path = "/excluded-path";
            var next = Substitute.For<RequestDelegate>();

            // Act
            await _middleware.InvokeAsync(context, next);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                _logger.Received().Log<object>(LogLevel.Debug, 0, Arg.Any<object>(), Arg.Any<Exception>(), Arg.Any<Func<object, Exception, string>>()!);
                _fraudReportChannel.Reader.TryRead(out var _).Should().BeFalse();
            }
        }

        [Fact]
        public async Task InvokeAsync_Should_InvokeNextMiddleware_When_Called()
        {
            // Arrange
            var context = new DefaultHttpContext();
            context.Request.Path = "/not-excluded-path";
            var next = Substitute.For<RequestDelegate>();

            // Act
            await _middleware.InvokeAsync(context, next);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                await next.Received(1).Invoke(context);
            }
        }

        [Fact]
        public async Task InvokeAsync_Should_LogDebug_When_ProcessingRequestHeaders()
        {
            // Arrange
            var context = new DefaultHttpContext();
            context.Request.Path = "/not-excluded-path";
            var next = Substitute.For<RequestDelegate>();

            // Act
            await _middleware.InvokeAsync(context, next);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                _logger.Received().Log<object>(LogLevel.Debug, 0, Arg.Any<object>(), Arg.Any<Exception>(), Arg.Any<Func<object, Exception, string>>()!);
            }
        }

        [Fact]
        public async Task InvokeAsync_Should_LogDebug_When_CreatingFraudReport()
        {
            // Arrange
            var context = new DefaultHttpContext();
            context.Request.Path = "/not-excluded-path";
            context.Request.Headers["X-Phone-Number"] = "1234567890";
            context.Request.Headers["X-App-Name"] = "TestApp";
            var next = Substitute.For<RequestDelegate>();

            // Act
            await _middleware.InvokeAsync(context, next);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                _logger.Received().Log<object>(LogLevel.Debug, 0, Arg.Any<object>(), Arg.Any<Exception>(), Arg.Any<Func<object, Exception, string>>()!);
            }
        }

        [Fact]
        public async Task InvokeAsync_Should_LogDebug_When_SkippingExcludedPath()
        {
            // Arrange
            var context = new DefaultHttpContext();
            context.Request.Path = "/excluded-path";
            var next = Substitute.For<RequestDelegate>();

            // Act
            await _middleware.InvokeAsync(context, next);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                _logger.Received().Log<object>(LogLevel.Debug, 0, Arg.Any<object>(), Arg.Any<Exception>(), Arg.Any<Func<object, Exception, string>>()!);
            }
        }
    }
}
