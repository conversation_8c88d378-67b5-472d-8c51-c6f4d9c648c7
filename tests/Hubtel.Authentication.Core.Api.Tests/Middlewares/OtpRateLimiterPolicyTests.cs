using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.RateLimiting;
using System.Threading.Tasks;
using FluentAssertions;
using Flurl.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.RateLimiting;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Xunit;

namespace Hubtel.Authentication.Core.Api.Middlewares.Tests
{
    public class OtpRateLimiterPolicyTests
    {
        private readonly ILogger<OtpRateLimiterPolicy> _logger;
        private readonly OtpRateLimiterPolicy _policy;

        public OtpRateLimiterPolicyTests()
        {
            _logger = Substitute.For<ILogger<OtpRateLimiterPolicy>>();
            _policy = new OtpRateLimiterPolicy(_logger);
        }

        [Fact]
        public void GetPartition_Should_ReturnPartition_When_ValidRequest()
        {
            // Arrange
            var httpContext = new DefaultHttpContext();
            var requestBody = JsonSerializer.Serialize(new { phoneNumber = "1234567890", appId = "testAppId" });
            httpContext.Request.Body = new MemoryStream(Encoding.UTF8.GetBytes(requestBody));
            httpContext.Request.ContentLength = requestBody.Length;

            // Act
            var partition = _policy.GetPartition(httpContext);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                partition.Should().NotBeNull();
                partition.PartitionKey.Should().Be("1234567890_testAppId");
            }
        }

        [Fact]
        public void GetPartition_Should_ReturnInvalidPartition_When_ExceptionOccurs()
        {
            // Arrange
            var httpContext = new DefaultHttpContext();
            var requestBody = "invalid json";
            httpContext.Request.Body = new MemoryStream(Encoding.UTF8.GetBytes(requestBody));
            httpContext.Request.ContentLength = requestBody.Length;

            // Act
            var partition = _policy.GetPartition(httpContext);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                partition.Should().NotBeNull();
                partition.PartitionKey.Should().Be("invalid");
                httpContext.Items["RateLimitParseError"].Should().Be(true);
                _logger.Received().Log<object>(
                      LogLevel.Error,
                      0,
                      Arg.Is<object>(o => o.ToString()!.Contains("Error occurred while getting rate limit partition")),
                      Arg.Any<Exception>(),
                      Arg.Any<Func<object, Exception, string>>()!);
            }
        }

        //[Fact]
        //public async Task OnRejected_Should_SetStatusCodeAndLogWarning_When_TooManyRequests()
        //{
        //    // Arrange
        //    var httpContext = new DefaultHttpContext();

        //    // Create a fake RateLimitLease that indicates the request was not permitted
        //    var lease = Substitute.For<RateLimitLease>();
        //    lease.IsAcquired.Returns(false);

        //    var onRejectedContext = new OnRejectedContext();
        //    var cancellationToken = new CancellationToken();

        //    // Act
        //    await _policy.OnRejected(onRejectedContext, cancellationToken);

        //    // Assert
        //    using (new FluentAssertions.Execution.AssertionScope())
        //    {
        //        httpContext.Response.StatusCode.Should().Be(StatusCodes.Status429TooManyRequests);
        //        _logger.Received(1).LogWarning(
        //            Arg.Is<string>(s => s.Contains("Too many request")),
        //            Arg.Any<object[]>());
        //    }
        //}


        //[Fact]
        //public async Task OnRejected_Should_SetStatusCodeAndLogWarning_When_BadRequestPayload()
        //{
        //    // Arrange
        //    var httpContext = new DefaultHttpContext();
        //    httpContext.Items["RateLimitParseError"] = true;
        //    var onRejectedContext = new OnRejectedContext(httpContext, null, null);
        //    var cancellationToken = new CancellationToken();

        //    // Act
        //    await _policy.OnRejected(onRejectedContext, cancellationToken);

        //    // Assert
        //    using (new FluentAssertions.Execution.AssertionScope())
        //    {
        //        httpContext.Response.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
        //        _logger.Received(1).LogWarning(Arg.Any<string>(), Arg.Any<object[]>());
        //    }
        //}
    }
}
