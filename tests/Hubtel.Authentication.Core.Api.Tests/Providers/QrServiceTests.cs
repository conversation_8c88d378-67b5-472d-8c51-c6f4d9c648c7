using Autofac.Core;
using FluentAssertions;
using FluentAssertions.Execution;
using Hubtel.Authentication.Commons.Services.QrCode;
using Hubtel.Authentication.Core.Api.Dtos;
using Hubtel.Authentication.Core.Api.Models;
using Hubtel.Authentication.Core.Api.Models.AppModels;
using Hubtel.Authentication.Core.Api.Models.Requests;
using Hubtel.Authentication.Core.Api.Models.Responses;
using Hubtel.Authentication.Core.Api.Options;
using Hubtel.Authentication.Core.Api.Services.Interface;
using Hubtel.Authentication.Core.Api.Services.Provider;
using JustEat.StatsD;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.Extensions.Options;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using QRCoder;

namespace Hubtel.Authentication.Core.Api.Tests.Providers;

public class QrServiceTests
{
    private readonly IAccountLookupService _accountLookupService;
    private readonly IStatsDPublisher _statsD;
    private readonly IJwtService _jwtService;

    private readonly IOptions<QrOptions> _qrOptions = Microsoft.Extensions.Options.Options.Create(
        new QrOptions()
        {
            Failure = "http://test.com",
            Success = "YourAuthorizationKey"
        });

    public QrServiceTests()
    {
        _accountLookupService = Substitute.For<IAccountLookupService>();
        _statsD = Substitute.For<IStatsDPublisher>();
        _jwtService = Substitute.For<IJwtService>();
    }

    [Fact]
    public async Task VerifyQrTokenAsync_Should_ReturnBadRequestResponse_When_NoQrDataParts()
    {
        var cipherService = Substitute.For<ICipherService>();
        var _qrService = new QrService(_jwtService, cipherService, _statsD, NullLogger<QrService>.Instance,
            _accountLookupService, _qrOptions);

        // Arrange
        var qrDataRequest = new QrDataRequest
        {
            PhoneNumber = "*********",
            QrData = "WxyZ",
            AppId = Guid.NewGuid(),
            DeviceId = "Device1"
        };
        cipherService.Decrypt(qrDataRequest.QrData).Returns(string.Empty);

        // Act
        var response = await _qrService.VerifyQrTokenAsync(qrDataRequest);

        // Assert
        response.Should().NotBeNull();
        response.Code.Should().Be((StatusCodes.Status400BadRequest).ToString());
    }

    [Fact]
    public async Task VerifyQrTokenAsync_Should_ReturnBadRequest_PhoneNumberMayNotExist_When_AccountDetailIsNull()
    {
        var cipherService = Substitute.For<ICipherService>();
        var _qrService = new QrService(_jwtService, cipherService, _statsD, NullLogger<QrService>.Instance,
            _accountLookupService, _qrOptions);
        // Arrange
        var qrDataRequest = new QrDataRequest
        {
            PhoneNumber = "*********",
            QrData = "WxyZ",
            AppId = Guid.NewGuid(),
            DeviceId = "Device1"
        };
        cipherService.DecryptQrData(qrDataRequest.QrData).Returns((new QrData
        {
            PlayerId = "Player1",
            PhoneNumber = qrDataRequest.PhoneNumber,
            AppId = qrDataRequest.AppId
        }, true));

        // Act
        var response = await _qrService.VerifyQrTokenAsync(qrDataRequest);

        // Assert
        using (new AssertionScope())
        {
            response.Should().NotBeNull();
            response.Code.Should().Be(StatusCodes.Status400BadRequest.ToString());
            response.Message.Should().Be(AppResponses.HttpRequestFailed);
        }
    }

    [Fact]
    public async Task VerifyQrTokenAsync_Should_ReturnOkResponse_When_AllInputIsValid()
    {
        // Arrange
        var cipherService = Substitute.For<ICipherService>();

        var qrDataRequest = new QrDataRequest
        {
            PhoneNumber = "*********",
            QrData = "WxyZ",
            AppId = Guid.NewGuid(),
            DeviceId = "Device1"
        };
        cipherService.DecryptQrData(qrDataRequest.QrData).Returns((new QrData
        {
            PlayerId = "Player1",
            PhoneNumber = qrDataRequest.PhoneNumber,
            AppId = qrDataRequest.AppId
        }, true));
        _accountLookupService.RetrieveCachedAccountInfo(qrDataRequest.AppId, qrDataRequest.PhoneNumber).Returns(
            Task.FromResult(new PhoneNumberLookupData
            {
                Email = "<EMAIL>",
                MobileNumber = "**********",
                TokenData = new Dictionary<string, string>
                {
                    { "extraData", "some extra data" }
                }
            }));
        _accountLookupService.ClientAppDetails(qrDataRequest.AppId)
            .Returns(Task.FromResult(new GetBackOfficeAuthConfigStoreResponse() { AccountLookUpUrl = "URL" }));
        var _qrService = new QrService(_jwtService, cipherService, _statsD, NullLogger<QrService>.Instance,
            _accountLookupService, _qrOptions);
        var expectedResponse = new VerifyOtpResponse
        {
            MobileNumber = qrDataRequest.PhoneNumber,
            RequestId = qrDataRequest.DeviceId
        };
        _jwtService.GenerateAppToken(Arg.Any<TokenGenerationModel>())
            .Returns(BaseAppResult.Success("success", expectedResponse));

        // Act
        var response = await _qrService.VerifyQrTokenAsync(qrDataRequest);

        // Assert
        using (new AssertionScope())
        {
            response.Should().NotBeNull();
            response.Code.Should().Be(StatusCodes.Status200OK.ToString());
        }
    }


    [Fact]
    public async Task VerifyQrTokenAsync_Should_ReturnFailedDependencyErrorResponse_When_ExceptionThrown()
    {
        // Arrange
        var cipherService = Substitute.For<ICipherService>();

        var qrDataRequest = new QrDataRequest
        {
            PhoneNumber = "*********",
            QrData = "WxyZ",
            AppId = Guid.NewGuid(),
            DeviceId = "Device1"
        };
        cipherService.DecryptQrData(qrDataRequest.QrData)
            .Returns(x => throw new DependencyResolutionException("Dependency error"));
        var _qrService = new QrService(_jwtService, cipherService, _statsD, NullLogger<QrService>.Instance,
            _accountLookupService, _qrOptions);
        // Act
        var response = await _qrService.VerifyQrTokenAsync(qrDataRequest);

        // Assert
        response.Should().NotBeNull();
        response.Code.Should().Be(StatusCodes.Status424FailedDependency.ToString());
    }

    [Fact]
    public async Task GenerateQrTokenAsync_Should_ReturnSuccessResponse_When_ValidRequest()
    {
        // Arrange
        var qrDataRequest = new QrData
        {
            PlayerId = "Player123",
            PhoneNumber = "**********",
            AppId = Guid.NewGuid()
        };

        var encryptedData = "encryptedData";
        ICipherService cipherService = Substitute.For<ICipherService>();
        var _qrService = new QrService(_jwtService, cipherService, _statsD, NullLogger<QrService>.Instance,
            _accountLookupService, _qrOptions);
        cipherService.Encrypt(Arg.Any<string>()).Returns(encryptedData);

        var qrLogoPath = Path.Combine(Environment.CurrentDirectory, @"wwwroot/images/qr_logo.png");
        var qrCodeImage = new QRCodeGenerator().CreateQrCode(encryptedData, QRCodeGenerator.ECCLevel.H);
        var qrCode = new ImageSharpQrCode(qrCodeImage);

        // Act
        var result = await _qrService.GenerateQrTokenAsync(qrDataRequest);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Message.Should().Be("Qr code");
            result.Data.Should().NotBeNull();
            result.Data.QrCodeData.Should().Be(encryptedData);
            result.Data.QrCodeDataUrl.Should().StartWith("data:image/png;base64,");
            result.Data.QrId.Should().NotBeNullOrEmpty();
        }
    }

    [Fact]
    public async Task GenerateQrTokenAsync_Should_LogErrorAndReturnFailedDependency_When_ExceptionOccurs()
    {
        // Arrange
        var qrDataRequest = new QrData
        {
            PlayerId = "Player123",
            PhoneNumber = "**********",
            AppId = Guid.NewGuid()
        };
        ICipherService cipherService = Substitute.For<ICipherService>();
        var logger = Substitute.For<ILogger<QrService>>();
        var _qrService = new QrService(_jwtService, cipherService, _statsD, logger,
            _accountLookupService, _qrOptions);
        cipherService.Encrypt(Arg.Any<string>()).Throws(new Exception("Encryption error"));

        // Act
        var result = await _qrService.GenerateQrTokenAsync(qrDataRequest);

        // Assert
        using (new AssertionScope())
        {
            result.Should().NotBeNull();
            result.Message.Should().Be("An error occured, try again later");
            result.Data.Should().BeNull();
        }

        
    }
}