using FluentAssertions;
using Hubtel.Authentication.Core.Api.Dtos;
using Hubtel.Authentication.Core.Api.Models;
using Hubtel.Authentication.Core.Api.Models.AppModels;
using Hubtel.Authentication.Core.Api.Models.Requests;
using Hubtel.Authentication.Core.Api.Models.Responses;
using Hubtel.Authentication.Core.Api.Options;
using Hubtel.Authentication.Core.Api.Services.Provider;
using Microsoft.Extensions.Options;
using NSubstitute;

namespace Hubtel.Authentication.Core.Api.Tests.Services.Provider
{
    public class JwtServiceTests
    {
        private readonly IOptions<JwtConfig> _jwtOptions;
        private readonly JwtService _jwtService;

        public JwtServiceTests()
        {
            _jwtOptions = Substitute.For<IOptions<JwtConfig>>();
            _jwtOptions.Value.Returns(new JwtConfig
            {
                Audience = "test_audience",
                Issuer = "test_issuer",
                SigningKey = "test_signing_key",
                ValidityPeriod = 7
            });
            _jwtService = new JwtService(_jwtOptions);
        }

        [Fact]
        public void GenerateAppToken_Should_ReturnError_When_AccountInformationIsNull()
        {
            // Arrange
            var generationModel = new TokenGenerationModel
            {
                AccountLookupData = null,
                AuthenticationConfiguration = new GetBackOfficeAuthConfigStoreResponse
                {
                    SigningKey = "test_signing_key",
                    ValidityPeriod = 7
                }
            };

            // Act
            var result = _jwtService.GenerateAppToken(generationModel);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Status.Should().Be(AppStatus.Error);
                result.Message.Should().Be(AppResponses.AccountInformationIsNull);
            }
        }

        [Fact]
        public void GenerateAppToken_Should_ReturnToken_When_ValidDataProvided()
        {
            // Arrange
            var generationModel = new TokenGenerationModel
            {
                AccountLookupData = new PhoneNumberLookupData
                {
                    MobileNumber = "**********",
                    Email = "<EMAIL>",
                    TokenData = new Dictionary<string, string>
                    {
                        { "sub", "**********" },
                        { "email", "<EMAIL>" }
                    }
                },
                AuthenticationConfiguration = new GetBackOfficeAuthConfigStoreResponse
                {
                    SigningKey = "test_signing_keytest_signing_keytest_signing_keytest_signing_keytest_signing_key",
                    ValidityPeriod = 7,
                    Issuer = "test_issuer",
                    Audience = "test_audience"
                },
                RequestId = "test_request_id"
            };

            // Act
            var result = _jwtService.GenerateAppToken(generationModel);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Status.Should().Be(AppStatus.Success);
                result.Data.Should().BeOfType<VerifyOtpResponse>();
                var tokenData = result.Data as VerifyOtpResponse;
                tokenData?.Email.Should().Be(generationModel.AccountLookupData.Email);
                tokenData?.MobileNumber.Should().Be(generationModel.AccountLookupData.MobileNumber);
                tokenData?.RequestId.Should().Be(generationModel.RequestId);
                tokenData?.Token.Should().NotBeNullOrEmpty();
            }
        }

        [Fact]
        public void GenerateAppToken_Should_ReturnTokenWithQrData_When_QrDataRequestProvided()
        {
            // Arrange
            var generationModel = new TokenGenerationModel
            {
                AccountLookupData = new PhoneNumberLookupData
                {
                    MobileNumber = "**********",
                    Email = "<EMAIL>",
                    TokenData = new Dictionary<string, string>
                    {
                        { "sub", "**********" },
                        { "email", "<EMAIL>" }
                    }
                },
                AuthenticationConfiguration = new GetBackOfficeAuthConfigStoreResponse
                {
                    SigningKey = "test_signing_keytest_signing_keytest_signing_keytest_signing_keytest_signing_keytest_signing_key",
                    ValidityPeriod = 7,
                    Issuer = "test_issuer",
                    Audience = "test_audience"
                },
                QrDataRequest = new QrData
                {
                    PlayerId = "test_player_id",
                    PhoneNumber = "**********",
                    AppId = Guid.NewGuid()
                },
                RequestId = "test_request_id"
            };

            // Act
            var result = _jwtService.GenerateAppToken(generationModel);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Status.Should().Be(AppStatus.Success);
                result.Data.Should().BeOfType<VerifyOtpResponse>();
                var tokenData = result.Data as VerifyOtpResponse;
                tokenData?.Email.Should().Be(generationModel.AccountLookupData.Email);
                tokenData?.MobileNumber.Should().Be(generationModel.AccountLookupData.MobileNumber);
                tokenData?.RequestId.Should().Be(generationModel.RequestId);
                tokenData?.Token.Should().NotBeNullOrEmpty();
                tokenData?.PlayerId.Should().Be(generationModel.QrDataRequest.PlayerId);
                tokenData?.Expiry.Should().NotBeNullOrEmpty();
            }
        }

        [Fact]
        public void GenerateAppToken_Should_ReturnError_When_ExceptionThrown()
        {
            // Arrange
            var generationModel = new TokenGenerationModel
            {
                AccountLookupData = new PhoneNumberLookupData
                {
                    MobileNumber = "**********",
                    Email = "<EMAIL>",
                    TokenData = new Dictionary<string, string>
                    {
                        { "sub", "**********" },
                        { "email", "<EMAIL>" }
                    }
                },
                AuthenticationConfiguration = new GetBackOfficeAuthConfigStoreResponse
                {
                    SigningKey = "invalid_signing_key",
                    ValidityPeriod = 7,
                    Issuer = "test_issuer",
                    Audience = "test_audience"
                },
                RequestId = "test_request_id"
            };

            // Act
            var result = _jwtService.GenerateAppToken(generationModel);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Status.Should().Be(AppStatus.Error);
                result.Message.Should().NotBeNullOrEmpty();
            }
        }
    }
}
