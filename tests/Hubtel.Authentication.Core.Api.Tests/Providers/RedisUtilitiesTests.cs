using FluentAssertions;
using Hubtel.Authentication.Commons.Extensions;
using NSubstitute;
using StackExchange.Redis;

namespace Hubtel.Authentication.Core.Api.Tests.Providers
{

    public class RedisUtilExtensionsTests
    {
        private readonly IDatabase _database;

        public RedisUtilExtensionsTests()
        {
            _database = Substitute.For<IDatabase>();
        }

        [Fact]
        public async Task AddToSortedSet_Should_ReturnTrue_When_AddedSuccessfully()
        {
            // Arrange
            var key = "testKey";
            var score = 1L;
            var model = new { Name = "Test" };
            _database.SortedSetAddAsync(Arg.Any<RedisKey>(), Arg.Any<RedisValue>(), Arg.Any<double>())
                .Returns(true);

            // Act
            var result = await _database.AddToSortedSet(key, score, model);

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public async Task RemoveFromSortedSet_Should_ReturnNumberOfRemovedElements()
        {
            // Arrange
            var key = "testKey";
            var score = 1.0;
            _database.SortedSetRemoveRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>())
                .Returns(1L);

            // Act
            var result = await _database.RemoveFromSortedSet(key, score);

            // Assert
            result.Should().Be(1L);
        }

        //public async Task FindInSortedSet_Should_ReturnDeserializedObjects()
        //{
        //    // Arrange
        //    var key = "testKey";
        //    var startScore = 1L;
        //    var endScore = 10L;
        //    var redisValues = new RedisValue[] { JsonConvert.SerializeObject(new { Name = "Test" }) };
        //    _database.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>())
        //        .Returns(redisValues);

        //    // Act
        //    var result = await _database.FindInSortedSet<object>(key, startScore, endScore);

        //    // Assert
        //    result.Should().HaveCount(1);
        //    result.First().Should().BeEquivalentTo(new { Name = "Test" });
        //}

        //public async Task FetchFromSortedSet_Should_ReturnDeserializedObjects()
        //{
        //    // Arrange
        //    var key = "testKey";
        //    var redisValues = new RedisValue[] { JsonConvert.SerializeObject(new { Name = "Test" }) };
        //    _database.SortedSetRangeByRankAsync(Arg.Any<RedisKey>(), Arg.Any<long>(), Arg.Any<long>(), Arg.Any<Order>())
        //        .Returns(redisValues);

        //    // Act
        //    var result = await _database.FetchFromSortedSet<object>(key);

        //    // Assert
        //    result.Should().HaveCount(1);
        //    result.First().Should().BeEquivalentTo(new { Name = "Test" });
        //}

        [Fact]
        public async Task SetKey_Should_ReturnTrue_When_SetSuccessfully()
        {
            // Arrange
            var key = "testKey";
            var model = new { Name = "Test" };
            _database.StringSetAsync(Arg.Any<RedisKey>(), Arg.Any<RedisValue>())
                .Returns(true);

            // Act
            var result = await _database.SetKey(key, model);

            // Assert
            result.Should().BeTrue();
        }

        //public async Task GetKey_Should_ReturnDeserializedObject()
        //{
        //    // Arrange
        //    var key = "testKey";
        //    var redisValue = JsonConvert.SerializeObject(new { Name = "Test" });
        //    _database.StringGetAsync(Arg.Any<RedisKey>())
        //        .Returns(redisValue);

        //    // Act
        //    var result = await _database.GetKey<object>(key);

        //    // Assert
        //    result.Should().BeEquivalentTo(new { Name = "Test" });
        //}

        [Fact]
        public async Task SortedSetCardinality_Should_ReturnNumberOfElements()
        {
            // Arrange
            var key = "testKey";
            _database.SortedSetLengthAsync(Arg.Any<RedisKey>())
                .Returns(5L);

            // Act
            var result = await _database.SortedSetCardinality(key);

            // Assert
            result.Should().Be(5L);
        }
    }

}
