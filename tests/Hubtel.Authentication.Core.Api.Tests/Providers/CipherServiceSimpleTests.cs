using System.Text;
using FluentAssertions;
using Hubtel.Authentication.Core.Api.Services.Provider;

namespace Hubtel.Authentication.Core.Api.Tests.Providers
{
    public class CipherServiceSimpleTests
    {
        private readonly CipherServiceSimple _cipherService;

        public CipherServiceSimpleTests()
        {
            _cipherService = new CipherServiceSimple();
        }

        [Fact]
        public void Encrypt_Should_ReturnBase64EncodedString_When_ValidPlainText()
        {
            // Arrange
            var plainText = "Hello, World!";

            // Act
            var result = _cipherService.Encrypt(plainText);

            // Assert
            result.Should().Be(Convert.ToBase64String(Encoding.UTF8.GetBytes(plainText)));
        }

        [Fact]
        public void Decrypt_Should_ReturnPlainText_When_ValidCipherText()
        {
            // Arrange
            var plainText = "Hello, World!";
            var cipherText = Convert.ToBase64String(Encoding.UTF8.GetBytes(plainText));

            // Act
            var result = _cipherService.Decrypt(cipherText);

            // Assert
            result.Should().Be(plainText);
        }

        [Fact]
        public void DecryptQrData_Should_ReturnQrDataAndSuccessTrue_When_ValidCipherText()
        {
            // Arrange
            var qrData = "Player1^d290f1ee-6c54-4b01-90e6-d701748f0851^1234567890^abcdefgh";
            var cipherText = Convert.ToBase64String(Encoding.UTF8.GetBytes(qrData));

            // Act
            var (result, success) = _cipherService.DecryptQrData(cipherText);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                success.Should().BeTrue();
                result.Should().NotBeNull();
                result.PlayerId.Should().Be("Player1");
                result.AppId.Should().Be(Guid.Parse("d290f1ee-6c54-4b01-90e6-d701748f0851"));
                result.PhoneNumber.Should().Be("1234567890");
            }
        }

        [Fact]
        public void DecryptQrData_Should_ReturnNullAndSuccessFalse_When_InvalidCipherText()
        {
            // Arrange
            var qrData = "InvalidData";
            var cipherText = Convert.ToBase64String(Encoding.UTF8.GetBytes(qrData));

            // Act
            var (result, success) = _cipherService.DecryptQrData(cipherText);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                success.Should().BeFalse();
                result.Should().BeNull();
            }
        }

        [Fact]
        public void DecryptQrData_Should_ReturnNullAndSuccessFalse_When_QrDataLengthIsLessThan4()
        {
            // Arrange
            var qrData = "Player1^d290f1ee-6c54-4b01-90e6-d701748f0851^1234567890";
            var cipherText = Convert.ToBase64String(Encoding.UTF8.GetBytes(qrData));

            // Act
            var (result, success) = _cipherService.DecryptQrData(cipherText);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                success.Should().BeFalse();
                result.Should().BeNull();
            }
        }

        [Fact]
        public void DecryptQrData_Should_ReturnNullAndSuccessFalse_When_QrDataFourthElementLengthIsLessThan8()
        {
            // Arrange
            var qrData = "Player1^d290f1ee-6c54-4b01-90e6-d701748f0851^1234567890^short";
            var cipherText = Convert.ToBase64String(Encoding.UTF8.GetBytes(qrData));

            // Act
            var (result, success) = _cipherService.DecryptQrData(cipherText);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                success.Should().BeFalse();
                result.Should().BeNull();
            }
        }
    }
}
