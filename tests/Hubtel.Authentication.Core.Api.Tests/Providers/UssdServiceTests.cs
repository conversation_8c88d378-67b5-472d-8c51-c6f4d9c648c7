using FluentAssertions;
using Hubtel.Authentication.Commons.Extensions;
using Hubtel.Authentication.Commons.Models;
using Hubtel.Authentication.Core.Api.Dtos;
using Hubtel.Authentication.Core.Api.Models;
using Hubtel.Authentication.Core.Api.Models.AppModels;
using Hubtel.Authentication.Core.Api.Models.Requests;
using Hubtel.Authentication.Core.Api.Models.Responses;
using Hubtel.Authentication.Core.Api.Services;
using Hubtel.Authentication.Core.Api.Services.Provider;
using Hubtel.Redis.Sdk.Services;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using StackExchange.Redis;

namespace Hubtel.Authentication.Core.Api.Tests.Providers
{
    public class UssdServiceTests
    {
        private readonly ILogger<UssdService> _logger;
        private readonly IDatabase _otpCacheRepository;
        private readonly IDatabase _memoryCacheService;
        private readonly IAuthenticationConfigStoreService _authenticationConfigStore;
        private readonly IDatabase _authConfigRedisStore;
        private readonly UssdService _ussdService;

        public UssdServiceTests()
        {
            var multiRedisHostCacheRepository = Substitute.For<IMultiRedisHostCacheRepository>();
            _logger = Substitute.For<ILogger<UssdService>>();
            _otpCacheRepository = Substitute.For<IDatabase>();
            _memoryCacheService = Substitute.For<IDatabase>();
            _authConfigRedisStore = Substitute.For<IDatabase>();
            _authenticationConfigStore = Substitute.For<IAuthenticationConfigStoreService>();

            multiRedisHostCacheRepository.GetDb(RedisConstants.MainRedisHostName, RedisConstants.DefaultOtpCacheDb).Returns(_otpCacheRepository);
            multiRedisHostCacheRepository.GetDb(RedisConstants.MainCustomerDataRedisHostName, RedisConstants.DefaultConsumerBlacklistDb).Returns(_memoryCacheService);
            multiRedisHostCacheRepository.GetDb(RedisConstants.MainRedisHostName, RedisConstants.AuthConfigurationDb).Returns(_authConfigRedisStore);


            _ussdService = new UssdService(multiRedisHostCacheRepository, _logger, _authenticationConfigStore);
        }

        [Fact]
        public async Task GenerateOtpAsync_Should_ReturnBadRequest_When_PhoneNumberIsBlacklisted()
        {
            // Arrange
            var model = new GenerateUssdOtpDto { PhoneNumber = "**********" };
            var accountDetails = new GetBackOfficeAuthConfigStoreResponse();
            var accountDetailsAccountLookupData = new PhoneNumberLookupData();
            _memoryCacheService.SetContainsAsync("ConsumerBlackListQS", model.PhoneNumber).Returns(true);

            // Act
            var result = await _ussdService.GenerateOtpAsync(model, accountDetailsAccountLookupData, accountDetails);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Message.Should().Be("There seem to be a problem with this account. Contact <EMAIL> for help");
                result.Code.Should().Be("400");
            }
        }
        [Fact]
        public async Task GenerateOtpAsync_Should_ReturnFailedDependency_When_ExceptionIsThrown()
        {
            // Arrange
            var model = new GenerateUssdOtpDto { PhoneNumber = "**********", AppId = Guid.NewGuid() };
            var accountDetails = new GetBackOfficeAuthConfigStoreResponse { SenderId = "senderId", AppName = "appName" };
            var accountDetailsAccountLookupData = new PhoneNumberLookupData { MobileNumber = "**********", Email = "<EMAIL>" };
            _memoryCacheService.SetContainsAsync("ConsumerBlackListQS", model.PhoneNumber).Throws(new Exception());
            var otp = new Otp { IsVerified = true, CreatedAt = DateTime.Now };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);

            // Act
            var result = await _ussdService.GenerateOtpAsync(model, accountDetailsAccountLookupData, accountDetails);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("424");
                result.Message.Should().Be("An error occured, try again later");
            }
        }

        [Fact]
        public async Task GenerateOtpAsync_Should_ReturnOkResponse_When_OtpGeneratedSuccessfullyAndPhoneNumberIsInternational()
        {
            // Arrange
            var model = new GenerateUssdOtpDto { PhoneNumber = "************", AppId = Guid.NewGuid() };
            var accountDetails = new GetBackOfficeAuthConfigStoreResponse { SenderId = "senderId", AppName = "appName" };
            var accountDetailsAccountLookupData = new PhoneNumberLookupData { MobileNumber = "************", Email = "<EMAIL>" };
            _memoryCacheService.SetContainsAsync("ConsumerBlackListQS", model.PhoneNumber).Returns(false);
            var otp = new Otp { IsVerified = true, CreatedAt = DateTime.Now };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);
            _otpCacheRepository.SortedSetAddAsync(Arg.Any<RedisKey>(), Arg.Any<RedisValue>(), Arg.Any<double>()).Returns(true);
            _authenticationConfigStore.GetAllAuthenticationChannelsAsync(Arg.Any<Guid>(),default).Returns(
                new ApiResponse<List<AuthenticationChannel>>
                {

                    Data = new List<AuthenticationChannel>() { AuthenticationChannel.Ussd }
                });
            // Act
            var result = await _ussdService.GenerateOtpAsync(model, accountDetailsAccountLookupData, accountDetails);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("200");
                result.Data.Should().NotBeNull();
                result.Data.OtpCode.Should().NotBeNullOrEmpty();
                result.Data.RequestId.Should().NotBeNullOrEmpty();
            }
        }

        [Fact]
        public async Task GenerateOtpAsync_Should_ReturnOkResponse_When_OtpGeneratedSuccessfullyAndWhiteListIsNotNull()
        {
            // Arrange
            var model = new GenerateUssdOtpDto { PhoneNumber = "************", AppId = Guid.NewGuid() };
            var accountDetails = new GetBackOfficeAuthConfigStoreResponse { SenderId = "senderId", AppName = "appName",
                WhiteLists = [new WhiteListedNumber() { Otp = "1234", PhoneNumber= "************" }]  };
            var accountDetailsAccountLookupData = new PhoneNumberLookupData { MobileNumber = "************", Email = "<EMAIL>" };
            _memoryCacheService.SetContainsAsync("ConsumerBlackListQS", model.PhoneNumber).Returns(false);
            var otp = new Otp { IsVerified = true, CreatedAt = DateTime.Now };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);
            _otpCacheRepository.SortedSetAddAsync(Arg.Any<RedisKey>(), Arg.Any<RedisValue>(), Arg.Any<double>()).Returns(true);
            _authenticationConfigStore.GetAllAuthenticationChannelsAsync(Arg.Any<Guid>(), default).Returns(
                new ApiResponse<List<AuthenticationChannel>>
                {

                    Data = new List<AuthenticationChannel>() { AuthenticationChannel.Ussd }
                });
            // Act
            var result = await _ussdService.GenerateOtpAsync(model, accountDetailsAccountLookupData, accountDetails);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("200");
                result.Data.Should().NotBeNull();
                result.Data.OtpCode.Should().NotBeNullOrEmpty();
                result.Data.RequestId.Should().NotBeNullOrEmpty();
            }
        }
        [Fact]
        public async Task GenerateOtpAsync_Should_ReturnOkResponse_When_FirstTimeLoginOtpGeneratedSuccessfully()
        {
            // Arrange
            var model = new GenerateUssdOtpDto { PhoneNumber = "************", AppId = Guid.NewGuid() };
            var accountDetails = new GetBackOfficeAuthConfigStoreResponse { SenderId = "senderId", AppName = "appName" };
            var accountDetailsAccountLookupData = new PhoneNumberLookupData { MobileNumber = "************", Email = "<EMAIL>" };
            _memoryCacheService.SetContainsAsync("ConsumerBlackListQS", model.PhoneNumber).Returns(false);
            var otp = new Otp { IsVerified = true, CreatedAt = DateTime.Now };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);
            _otpCacheRepository.SortedSetAddAsync(Arg.Any<RedisKey>(), Arg.Any<RedisValue>(), Arg.Any<double>()).Returns(true);
            _authenticationConfigStore.GetAllAuthenticationChannelsAsync(Arg.Any<Guid>(), default).Returns(
                new ApiResponse<List<AuthenticationChannel>>
                {

                    Data = new List<AuthenticationChannel>() { AuthenticationChannel.Ussd }
                });
            _authConfigRedisStore.StringGetAsync(Arg.Any<RedisKey>()).Returns("NotEmpty");

            // Act
            var result = await _ussdService.GenerateOtpAsync(model, accountDetailsAccountLookupData, accountDetails);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("200");
                result.Data.Should().NotBeNull();
                result.Data.OtpCode.Should().NotBeNullOrEmpty();
                result.Data.RequestId.Should().NotBeNullOrEmpty();
            }
        }



        [Fact]
        public async Task GenerateOtpAsync_Should_ReturnOkResponse_When_OtpGeneratedSuccessfully()
        {
            // Arrange
            var model = new GenerateUssdOtpDto { PhoneNumber = "************", AppId = Guid.NewGuid() };
            var accountDetails = new GetBackOfficeAuthConfigStoreResponse { SenderId = "senderId", AppName = "appName" };
            var accountDetailsAccountLookupData = new PhoneNumberLookupData { MobileNumber = "************", Email = "<EMAIL>" };
            _memoryCacheService.SetContainsAsync("ConsumerBlackListQS", model.PhoneNumber).Returns(false);
            var otp = new Otp { IsVerified = true, CreatedAt = DateTime.Now };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);
            _otpCacheRepository.SortedSetAddAsync(Arg.Any<RedisKey>(), Arg.Any<RedisValue>(), Arg.Any<double>()).Returns(true);

            // Act
            var result = await _ussdService.GenerateOtpAsync(model, accountDetailsAccountLookupData, accountDetails);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("200");
                result.Data.Should().NotBeNull();
                result.Data.OtpCode.Should().NotBeNullOrEmpty();
                result.Data.RequestId.Should().NotBeNullOrEmpty();
            }
        }

        [Fact]
        public async Task GenerateOtpAsync_Should_ReturnFailedDependencyError_When_StoringOtpFails()
        {
            // Arrange
            var model = new GenerateUssdOtpDto { PhoneNumber = "**********", AppId = Guid.NewGuid() };
            var accountDetails = new GetBackOfficeAuthConfigStoreResponse { SenderId = "senderId", AppName = "appName" };
            var accountDetailsAccountLookupData = new PhoneNumberLookupData { MobileNumber = "**********", Email = "<EMAIL>" };
            _memoryCacheService.SetContainsAsync("ConsumerBlackListQS", model.PhoneNumber).Returns(false);
            var otp = new Otp { IsVerified = true, CreatedAt = DateTime.Now };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);

            // Act
            var result = await _ussdService.GenerateOtpAsync(model, accountDetailsAccountLookupData, accountDetails);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("424");
                result.Message.Should().Be("An error occured, try again later");
            }
        }

        [Fact]
        public async Task GenerateOtpAsync_Should_ReturnFailedDependencyError_When_ExceptionThrown()
        {
            // Arrange
            var model = new GenerateUssdOtpDto { PhoneNumber = "**********", AppId = Guid.NewGuid() };
            var accountDetails = new GetBackOfficeAuthConfigStoreResponse { SenderId = "senderId", AppName = "appName" };
            var accountDetailsAccountLookupData = new PhoneNumberLookupData { MobileNumber = "**********", Email = "<EMAIL>" };
            _memoryCacheService.SetContainsAsync("ConsumerBlackListQS", model.PhoneNumber).Returns(false);
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Throws(new Exception("Failed Dependency"));

            // Act
            var result = await _ussdService.GenerateOtpAsync(model, accountDetailsAccountLookupData, accountDetails);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("424");
                result.Message.Should().Be("An error occured, try again later");
            }
        }

        [Fact]
        public async Task ValidateRequestId_Should_ReturnTrue_When_RequestIdIsValid()
        {
            // Arrange
            var phoneNumber = "**********";
            var requestId = "validRequestId";
            var otp = new ExtendedOtp { RequestId = requestId, CreatedAt = DateTime.UtcNow };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);

            // Act
            var result = await _ussdService.ValidateRequestId(phoneNumber, requestId);

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public async Task ValidateRequestId_Should_ReturnFalse_When_RequestIdIsInvalid()
        {
            // Arrange
            var phoneNumber = "**********";
            var requestId = "invalidRequestId";
            var otp = new Otp { IsVerified = true, CreatedAt = DateTime.Now,RequestId= "validRequestId" };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);

            // Act
            var result = await _ussdService.ValidateRequestId(phoneNumber, requestId);

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public async Task ValidateRequestId_Should_ReturnFalse_When_OtpNotFound()
        {
            // Arrange
            var phoneNumber = "**********";
            var requestId = "requestId";
            var otp = new Otp { IsVerified = true, CreatedAt = DateTime.Now };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);

            // Act
            var result = await _ussdService.ValidateRequestId(phoneNumber, requestId);

            // Assert
            result.Should().BeFalse();
        }
    }
}
