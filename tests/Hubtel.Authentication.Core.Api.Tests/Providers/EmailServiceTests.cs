using Akka.Hosting;
using FluentAssertions;
using Hubtel.Authentication.Commons.Extensions;
using Hubtel.Authentication.Commons.Models;
using Hubtel.Authentication.Core.Api.Components.Actors;
using Hubtel.Authentication.Core.Api.Dtos;
using Hubtel.Authentication.Core.Api.Models;
using Hubtel.Authentication.Core.Api.Models.AppModels;
using Hubtel.Authentication.Core.Api.Models.Requests;
using Hubtel.Authentication.Core.Api.Models.Responses;
using Hubtel.Authentication.Core.Api.Services.Interface;
using Hubtel.Authentication.Core.Api.Services.Provider;
using Hubtel.Redis.Sdk.Services;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using StackExchange.Redis;

namespace Hubtel.Authentication.Core.Api.Tests.Providers
{
    public class EmailServiceTests
    {
        private EmailService? _emailService;
        private readonly IDatabase _otpCacheRepository;
        private readonly IDatabase _memoryCacheService;
        private readonly IDistributedCache _cache;
        private readonly IJwtService _jwtService;
        private readonly ILogger<EmailService> _logger;
        private readonly IAccountLookupService _accountLookupService;
        private readonly IRequiredActor<MainActor> _mainActor;
        private readonly IMultiRedisHostCacheRepository multiRedisHostCacheRepository;
        public EmailServiceTests()
        {
             multiRedisHostCacheRepository = Substitute.For<IMultiRedisHostCacheRepository>();
            _otpCacheRepository = Substitute.For<IDatabase>();
            _memoryCacheService = Substitute.For<IDatabase>();
            _cache = Substitute.For<IDistributedCache>();
            _jwtService = Substitute.For<IJwtService>();
            _logger = Substitute.For<ILogger<EmailService>>();
            _accountLookupService = Substitute.For<IAccountLookupService>();
            _mainActor = Substitute.For<IRequiredActor<MainActor>>();

            multiRedisHostCacheRepository.GetDb(RedisConstants.MainRedisHostName, RedisConstants.DefaultOtpCacheDb).Returns(_otpCacheRepository);
            multiRedisHostCacheRepository.GetDb(RedisConstants.MainCustomerDataRedisHostName,
            RedisConstants.DefaultConsumerBlacklistDb).Returns(_memoryCacheService);

            
        }

        [Fact]
        public async Task SendOtpAsync_Should_ReturnBadRequest_When_PhoneNumberIsBlacklisted()
        {
            _emailService = new EmailService(multiRedisHostCacheRepository, _cache, _jwtService, _logger, _accountLookupService, _mainActor);
            // Arrange
            var model = new GenerateEmailOtpDto { PhoneNumber = "**********" };
            var appConfig = new GetBackOfficeAuthConfigStoreResponse();

            _memoryCacheService.SetContainsAsync("ConsumerBlackListQS", model.PhoneNumber).Returns(true);

            // Act
            var result = await _emailService.SendOtpAsync(model, appConfig);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
                result.Message.Should().Be("There seem to be a problem with this account. Contact <EMAIL> for help");
            }
        }

        [Fact]
        public async Task SendOtpAsync_Should_ReturnSuccess_When_ValidInput()
        {
            _emailService = new EmailService(multiRedisHostCacheRepository, _cache, _jwtService, _logger, _accountLookupService, _mainActor);
            // Arrange
            var model = new GenerateEmailOtpDto { PhoneNumber = "**********" };
            var appConfig = new GetBackOfficeAuthConfigStoreResponse();

            _memoryCacheService.SetContainsAsync("ConsumerBlackListQS", model.PhoneNumber).Returns(false);

            // Act
            var result = await _emailService.SendOtpAsync(model, appConfig);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("200");
                result.Data.Should().NotBeNull();
                result.Data.OtpPrefix.Should().NotBeNullOrEmpty();
                result.Data.RequestId.Should().NotBeNullOrEmpty();
            }
        }

        [Fact]
        public async Task SendOtpAsync_Should_ReturnFailedDependency_When_AnExceptionIsThrown()
        {
            _emailService = new EmailService(multiRedisHostCacheRepository, _cache, _jwtService, _logger, _accountLookupService, _mainActor);
            // Arrange
            var model = new GenerateEmailOtpDto { PhoneNumber = "**********" };
            var appConfig = new GetBackOfficeAuthConfigStoreResponse();

            _memoryCacheService.SetContainsAsync("ConsumerBlackListQS", model.PhoneNumber).Throws(new Exception());

            // Act
            var result = await _emailService.SendOtpAsync(model, appConfig);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("424");
            }
        }
        

        [Fact]
        public async Task VerifyOtpAsync_Should_ReturnBadRequest_When_OtpIsExpired()
        {
            // Arrange
            _emailService = new EmailService(multiRedisHostCacheRepository, _cache, _jwtService, _logger, _accountLookupService, _mainActor);
            var model = new VerifyEmailOtpDto { PhoneNumber = "**********", OtpCode = "1234", Prefix = "ABCD", RequestId = "requestId" };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(new Otp{ }) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>())
                .Returns(redisValues);

            // Act
            var result = await _emailService.VerifyOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
                result.Message.Should().Be("Hmmm... Something doesn't look right with your login request. Please start the login process again.");
            }
        }

        [Fact]
        public async Task VerifyOtpAsync_Should_ReturnForbidden_When_OtpIsAlreadyVerified()
        {
            // Arrange
            _emailService = new EmailService(multiRedisHostCacheRepository, _cache, _jwtService, _logger, _accountLookupService, _mainActor);
            var model = new VerifyEmailOtpDto { PhoneNumber = "**********", OtpCode = "1234", Prefix = "ABCD", RequestId = "requestId" };
            var otplist = new List<Otp> { new Otp { IsVerified = true, CreatedAt = DateTime.UtcNow } };
            var redisValues = otplist.Select(k => (RedisValue)JsonConvert.SerializeObject(k)).ToArray();
            
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(),order: Order.Descending)
                .Returns(redisValues);

            // Act
            var result = await _emailService.VerifyOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("403");
                result.Message.Should().Be("OTP already verified");
            }
        }

        [Fact]
        public async Task VerifyOtpAsync_Should_ReturnBadRequest_When_RequestIdDoesNotMatch()
        {
            // Arrange
            _emailService = new EmailService(multiRedisHostCacheRepository, _cache, _jwtService, _logger, _accountLookupService, _mainActor);
            var model = new VerifyEmailOtpDto { PhoneNumber = "**********", OtpCode = "1234", Prefix = "ABCD", RequestId = "requestId" };
            var otplist = new List<Otp> { new Otp { IsVerified = false, CreatedAt = DateTime.UtcNow, RequestId = "differentRequestId" } };

            var redisValues = otplist.Select(k => (RedisValue)JsonConvert.SerializeObject(k)).ToArray();

            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);

            // Act
            var result = await _emailService.VerifyOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
                result.Message.Should().Be(AppResponses.InvalidRequestId);
            }
        }

        [Fact]
        public async Task VerifyOtpAsync_Should_ReturnForbidden_When_OtpUseCountExceedsLimit()
        {
            // Arrange
            _emailService = new EmailService(multiRedisHostCacheRepository, _cache, _jwtService, _logger, _accountLookupService, _mainActor);
            var model = new VerifyEmailOtpDto { PhoneNumber = "**********", OtpCode = "1234", Prefix = "ABCD", RequestId = "requestId" };
            var otplist = new List<Otp> { new Otp { IsVerified = false, CreatedAt = DateTime.UtcNow, RequestId = "requestId", OTPUseCount = 3 } };

            var redisValues = otplist.Select(k => (RedisValue)JsonConvert.SerializeObject(k)).ToArray();

            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);

            // Act
            var result = await _emailService.VerifyOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("403");
                result.Message.Should().Be(AppResponses.OtpAttemptsExceed);
            }
        }

        [Fact]
        public async Task VerifyOtpAsync_Should_ReturnBadRequest_When_PhoneNumberDoesNotMatch()
        {
            // Arrange
            _emailService = new EmailService(multiRedisHostCacheRepository, _cache, _jwtService, _logger, _accountLookupService, _mainActor);
            var model = new VerifyEmailOtpDto { PhoneNumber = "**********", OtpCode = "1234", Prefix = "ABCD", RequestId = "requestId" };
            var otplist = new List<Otp> { new Otp { IsVerified = false, CreatedAt = DateTime.UtcNow, RequestId = "requestId", Msisdn = "**********" } };

            var redisValues = otplist.Select(k => (RedisValue)JsonConvert.SerializeObject(k)).ToArray();

            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);

            // Act
            var result = await _emailService.VerifyOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
                result.Message.Should().Be(AppResponses.InvalidMobileNumber);
            }
        }

        [Fact]
        public async Task VerifyOtpAsync_Should_ReturnBadRequest_When_OtpCodeDoesNotMatch()
        {
            // Arrange
            _emailService = new EmailService(multiRedisHostCacheRepository, _cache, _jwtService, _logger, _accountLookupService, _mainActor);
            var model = new VerifyEmailOtpDto { PhoneNumber = "**********", OtpCode = "1234", Prefix = "ABCD", RequestId = "requestId" };
            var otplist = new List<Otp> { new Otp { IsVerified = false, CreatedAt = DateTime.UtcNow, RequestId = "requestId", Msisdn = "**********", OtpCode = "ABCD-5678" } };

            var redisValues = otplist.Select(k => (RedisValue)JsonConvert.SerializeObject(k)).ToArray();

            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);

            // Act
            var result = await _emailService.VerifyOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
                result.Message.Should().Be(AppResponses.InvalidOtp);
            }
        }

        [Fact]
        public async Task VerifyOtpAsync_Should_ReturnSuccess_When_ValidInput()
        {
            // Arrange
            _emailService = new EmailService(multiRedisHostCacheRepository, _cache, _jwtService, _logger, _accountLookupService, _mainActor);
            var model = new VerifyEmailOtpDto { PhoneNumber = "**********", OtpCode = "1234", Prefix = "ABCD", RequestId = "requestId" };
            var otplist = new List<Otp> { new Otp { IsVerified = false, CreatedAt = DateTime.UtcNow, RequestId = "requestId", Msisdn = "**********", OtpCode = "ABCD-1234" } };
            var accountDetails = new PhoneNumberLookupData();
            var clientApp = new GetBackOfficeAuthConfigStoreResponse() { AccountLookUpUrl="example.com"};
            var authResult = new AppResult { IsSuccessful = true, Data = new { Token = "token", Expiry = "expiry" } };

            var redisValues = otplist.Select(k => (RedisValue)JsonConvert.SerializeObject(k)).ToArray();

            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);
            _accountLookupService.RetrieveCachedAccountInfo(Arg.Any<Guid>(), Arg.Any<string>()).Returns(accountDetails);
            _accountLookupService.ClientAppDetails(Arg.Any<Guid>()).Returns(clientApp);
            _jwtService.GenerateAppToken(Arg.Any<TokenGenerationModel>()).Returns(authResult);

            // Act
            var result = await _emailService.VerifyOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("200");
                result.Data.Should().NotBeNull();
                result.Data.Token.Should().Be("token");
                result.Data.Expiry.Should().Be("expiry");
                result.Data.MobileNumber.Should().Be("**********");
            }
        }
    }
}
