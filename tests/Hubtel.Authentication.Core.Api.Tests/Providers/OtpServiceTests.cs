using Akka.Hosting;
using FluentAssertions;
using Flurl.Http;
using Hubtel.Authentication.Commons.Extensions;
using Hubtel.Authentication.Commons.Models;
using Hubtel.Authentication.Core.Api.Components.Actors;
using Hubtel.Authentication.Core.Api.Dtos;
using Hubtel.Authentication.Core.Api.Models;
using Hubtel.Authentication.Core.Api.Models.AppModels;
using Hubtel.Authentication.Core.Api.Models.Requests;
using Hubtel.Authentication.Core.Api.Models.Responses;
using Hubtel.Authentication.Core.Api.Services.Interface;
using Hubtel.Authentication.Core.Api.Services.Provider;
using Hubtel.Authentication.Whatsapp.Sdk.Client;
using Hubtel.Authentication.Whatsapp.Sdk.Models;
using Hubtel.Redis.Sdk.Services;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using StackExchange.Redis;
using static System.Net.WebRequestMethods;

namespace Hubtel.Authentication.Core.Api.Tests.Providers
{
    public class OtpServiceTests
    {
        private readonly IDatabase _otpCacheRepository;
        private readonly IDatabase _memoryCacheService;
        private readonly IDistributedCache _cache;
        private readonly IJwtService _jwtService;
        private readonly ILogger<OtpService> _logger;
        private readonly IWhatsAppProxyApi _whatsAppApi;
        private readonly IAccountLookupService _accountLookupService;
        private readonly IRequiredActor<MainActor> _mainActor;
        private readonly OtpService _sut;

        public OtpServiceTests()
        {
            var multiRedisHostCacheRepository = Substitute.For<IMultiRedisHostCacheRepository>();
            _otpCacheRepository = Substitute.For<IDatabase>();
            _memoryCacheService = Substitute.For<IDatabase>();
            _cache = Substitute.For<IDistributedCache>();
            _jwtService = Substitute.For<IJwtService>();
            _logger = Substitute.For<ILogger<OtpService>>();
            _whatsAppApi = Substitute.For<IWhatsAppProxyApi>();
            _accountLookupService = Substitute.For<IAccountLookupService>();
            _mainActor = Substitute.For<IRequiredActor<MainActor>>();

            multiRedisHostCacheRepository.GetDb(RedisConstants.MainRedisHostName, RedisConstants.DefaultOtpCacheDb).Returns(_otpCacheRepository);
            multiRedisHostCacheRepository.GetDb(RedisConstants.MainCustomerDataRedisHostName, RedisConstants.DefaultConsumerBlacklistDb).Returns(_memoryCacheService);


            _sut = new OtpService(multiRedisHostCacheRepository, _cache, _jwtService, _logger, _whatsAppApi, _accountLookupService, _mainActor);
        }

        [Fact]
        public async Task SendOtpAsync_Should_ReturnBadRequest_When_PhoneNumberIsBlacklisted()
        {
            // Arrange
            var model = new CreateOtpRequest { PhoneNumber = "**********" };
            _memoryCacheService.SetContainsAsync("ConsumerBlackListQS", model.PhoneNumber).Returns(true);

            // Act
            var result = await _sut.SendOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Message.Should().Be("There seem to be a problem with this account. Contact <EMAIL> for help");
                result.Code.Should().Be("400");
            }
        }

        [Fact]
        public async Task SendOtpAsync_Should_ReturnOkResponse_When_OtpGeneratedSuccessfully()
        {
            // Arrange
            var model = new CreateOtpRequest { PhoneNumber = "**********", AppId = Guid.NewGuid() };
            var appDetails = new GetBackOfficeAuthConfigStoreResponse { SenderId = "senderId", OtpLiabilityPolicy = "policy" };
            _memoryCacheService.SetContainsAsync("ConsumerBlackListQS", model.PhoneNumber).Returns(false);
            _accountLookupService.ClientAppDetails(model.AppId).Returns(appDetails);

            // Act
            var result = await _sut.SendOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("200");
                result.Data.Should().NotBeNull();
                result.Data.OtpPrefix.Should().NotBeNullOrEmpty();
                result.Data.RequestId.Should().NotBeNullOrEmpty();
            }
        }

        [Fact]
        public async Task SendOtpAsync_Should_ReturnFailedDependencyError_When_ExceptionThrown()
        {
            // Arrange
            var model = new CreateOtpRequest { PhoneNumber = "**********" };
            _memoryCacheService.SetContainsAsync("ConsumerBlackListQS", model.PhoneNumber).Returns(false);
            _accountLookupService.ClientAppDetails(model.AppId).Throws(new Exception("Test exception"));

            // Act
            var result = await _sut.SendOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("424");
                result.Message.Should().Be("An error occured, try again later");
            }
        }

        [Fact]
        public async Task VerifyOtpAsync_Should_ReturnBadRequest_When_OtpExpired()
        {
            // Arrange
            var model = new VerifyOtpDto { PhoneNumber = "**********", OtpCode = "1234", Prefix = "ABCD", RequestId = "requestId" };
            var listOfOtp=new List<Otp>();
            var redisValues = listOfOtp.Select(otp => (RedisValue)JsonConvert.SerializeObject(otp)).ToArray();
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>())
                .Returns(redisValues);

            // Act
            var result = await _sut.VerifyOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
                result.Message.Should().Be(AppResponses.InvalidRequestId);
            }
        }

      

        [Fact]
        public async Task VerifyOtpAsync_Should_ReturnForbidden_When_OtpAlreadyVerified()
        {
            // Arrange
            var model = new VerifyOtpDto { PhoneNumber = "**********", OtpCode = "1234", Prefix = "ABCD", RequestId = "requestId" };
            var otp = new Otp { IsVerified = true , CreatedAt=DateTime.Now};
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(),order: Order.Descending)
                .Returns(redisValues);

            // Act
            var result = await _sut.VerifyOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("403");
                result.Message.Should().Be("OTP already verified");
            }
        }

        [Fact]
        public async Task VerifyOtpAsync_Should_ReturnBadRequest_When_InvalidRequestId()
        {
            // Arrange
            var model = new VerifyOtpDto { PhoneNumber = "**********", OtpCode = "1234", Prefix = "ABCD", RequestId = "requestId" };
            var otp = new Otp { OtpCode = "ABCD-1234", Msisdn = model.PhoneNumber, RequestId = "model.RequestId", CreatedAt = DateTime.Now };
            var clientApp = new GetBackOfficeAuthConfigStoreResponse { AccountLookUpUrl = "http://example.com" };
            var accountDetails = new PhoneNumberLookupResponse { Code = "200", Data = new PhoneNumberLookupData() };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);
            _accountLookupService.ClientAppDetails(model.AppId).Returns(clientApp);
            _accountLookupService.LookUpCustomerInfoByPhoneNumber(clientApp.AccountLookUpUrl, model.PhoneNumber).Returns(accountDetails);

            // Act
            var result = await _sut.VerifyOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
                result.Message.Should().Be(AppResponses.InvalidRequestId);
            }
        }

        [Fact]
        public async Task VerifyOtpAsync_Should_ReturnForbidden_When_OtpAttemptsExceed()
        {
            // Arrange
            var model = new VerifyOtpDto { PhoneNumber = "**********", OtpCode = "1234", Prefix = "ABCD", RequestId = "requestId" };
            var otp = new Otp { OTPUseCount = 3,CreatedAt=DateTime.Now,RequestId= "requestId", Prefix="ABCD",OtpCode="1234" };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(),order:Order.Descending)
                .Returns(redisValues);

            // Act
            var result = await _sut.VerifyOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("403");
                result.Message.Should().Be(AppResponses.OtpAttemptsExceed);
            }
        }

        [Fact]
        public async Task VerifyOtpAsync_Should_ReturnBadRequest_When_ClientAppIsNull()
        {
            // Arrange
            var model = new VerifyOtpDto { PhoneNumber = "**********", OtpCode = "1234", Prefix = "ABCD", RequestId = "requestId" };
            var otp = new Otp { OtpCode = "ABCD-1234", Msisdn = model.PhoneNumber, RequestId = model.RequestId, CreatedAt = DateTime.Now };
            GetBackOfficeAuthConfigStoreResponse clientApp = null!;
            var accountDetails = new PhoneNumberLookupResponse { Code = "200", Data = new PhoneNumberLookupData() };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);
            _accountLookupService.ClientAppDetails(model.AppId).Returns(clientApp);
            _accountLookupService.LookUpCustomerInfoByPhoneNumber(Arg.Any<string>(), model.PhoneNumber).Returns(accountDetails);
            _jwtService.GenerateAppToken(Arg.Any<TokenGenerationModel>()).Returns(AppResult.Success(data: new VerifyOtpResponse()));
            // Act
            var result = await _sut.VerifyOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
            }
        }

        [Fact]
        public async Task VerifyOtpAsync_Should_ReturnBadRequest_When_AccountLookupurlForClientAppIsNull()
        {
            // Arrange
            var model = new VerifyOtpDto { PhoneNumber = "**********", OtpCode = "1234", Prefix = "ABCD", RequestId = "requestId" };
            var otp = new Otp { OtpCode = "ABCD-1234", Msisdn = model.PhoneNumber, RequestId = model.RequestId, CreatedAt = DateTime.Now };
            var clientApp = new GetBackOfficeAuthConfigStoreResponse { AccountLookUpUrl = null };
            var accountDetails = new PhoneNumberLookupResponse { Code = "200", Data = new PhoneNumberLookupData() };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);
            _accountLookupService.ClientAppDetails(model.AppId).Returns(clientApp);
            _accountLookupService.LookUpCustomerInfoByPhoneNumber(clientApp.AccountLookUpUrl, model.PhoneNumber).Returns(accountDetails);
            _jwtService.GenerateAppToken(Arg.Any<TokenGenerationModel>()).Returns(AppResult.Success(data: new VerifyOtpResponse()));
            // Act
            var result = await _sut.VerifyOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
            }
        }

        [Fact]
        public async Task VerifyOtpAsync_Should_ReturnBadRequest_When_CustomerInfoByPhoneNumberIsNull()
        {
            // Arrange
            var model = new VerifyOtpDto { PhoneNumber = "**********", OtpCode = "1234", Prefix = "ABCD", RequestId = "requestId" };
            var otp = new Otp { OtpCode = "ABCD-1234", Msisdn = model.PhoneNumber, RequestId = model.RequestId, CreatedAt = DateTime.Now };
            var clientApp = new GetBackOfficeAuthConfigStoreResponse { AccountLookUpUrl = "http://example.com" };
            PhoneNumberLookupResponse accountDetails = null!;
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);
            _accountLookupService.ClientAppDetails(model.AppId).Returns(clientApp);
            _accountLookupService.LookUpCustomerInfoByPhoneNumber(clientApp.AccountLookUpUrl, model.PhoneNumber).Returns(accountDetails);
            _jwtService.GenerateAppToken(Arg.Any<TokenGenerationModel>()).Returns(AppResult.Success(data: new VerifyOtpResponse()));
            // Act
            var result = await _sut.VerifyOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
            }
        }

        [Fact]
        public async Task VerifyOtpAsync_Should_ReturnBadRequest_When_CustomerInfoByPhoneNumberReturnsForbidden()
        {
            // Arrange
            var model = new VerifyOtpDto { PhoneNumber = "**********", OtpCode = "1234", Prefix = "ABCD", RequestId = "requestId" };
            var otp = new Otp { OtpCode = "ABCD-1234", Msisdn = model.PhoneNumber, RequestId = model.RequestId, CreatedAt = DateTime.Now };
            var clientApp = new GetBackOfficeAuthConfigStoreResponse { AccountLookUpUrl = "http://example.com" };
            var accountDetails = new PhoneNumberLookupResponse { Code = "403", Data = new PhoneNumberLookupData() };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);
            _accountLookupService.ClientAppDetails(model.AppId).Returns(clientApp);
            _accountLookupService.LookUpCustomerInfoByPhoneNumber(clientApp.AccountLookUpUrl, model.PhoneNumber).Returns(accountDetails);
            _jwtService.GenerateAppToken(Arg.Any<TokenGenerationModel>()).Returns(AppResult.Success(data: new VerifyOtpResponse()));
            // Act
            var result = await _sut.VerifyOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("403");
            }
        }

        [Fact]
        public async Task VerifyOtpAsync_Should_ReturnBadRequest_When_CustomerInfoByPhoneNumberReturnsServerError()
        {
            // Arrange
            var model = new VerifyOtpDto { PhoneNumber = "**********", OtpCode = "1234", Prefix = "ABCD", RequestId = "requestId" };
            var otp = new Otp { OtpCode = "ABCD-1234", Msisdn = model.PhoneNumber, RequestId = model.RequestId, CreatedAt = DateTime.Now };
            var clientApp = new GetBackOfficeAuthConfigStoreResponse { AccountLookUpUrl = "http://example.com" };
            var accountDetails = new PhoneNumberLookupResponse { Code = "500", Data = new PhoneNumberLookupData() };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);
            _accountLookupService.ClientAppDetails(model.AppId).Returns(clientApp);
            _accountLookupService.LookUpCustomerInfoByPhoneNumber(clientApp.AccountLookUpUrl, model.PhoneNumber).Returns(accountDetails);
            _jwtService.GenerateAppToken(Arg.Any<TokenGenerationModel>()).Returns(AppResult.Success(data: new VerifyOtpResponse()));
            // Act
            var result = await _sut.VerifyOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("500");
            }
        }

        [Fact]
        public async Task VerifyOtpAsync_Should_ReturnFailedDependency_When_TokenGenerationFails()
        {
            // Arrange
            var model = new VerifyOtpDto { PhoneNumber = "**********", OtpCode = "1234", Prefix = "ABCD", RequestId = "requestId" };
            var otp = new Otp { OtpCode = "ABCD-1234", Msisdn = model.PhoneNumber, RequestId = model.RequestId, CreatedAt = DateTime.Now };
            var clientApp = new GetBackOfficeAuthConfigStoreResponse { AccountLookUpUrl = "http://example.com" };
            var accountDetails = new PhoneNumberLookupResponse { Code = "200", Data = new PhoneNumberLookupData() };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);
            _accountLookupService.ClientAppDetails(model.AppId).Returns(clientApp);
            _accountLookupService.LookUpCustomerInfoByPhoneNumber(clientApp.AccountLookUpUrl, model.PhoneNumber).Returns(accountDetails);
            _jwtService.GenerateAppToken(Arg.Any<TokenGenerationModel>()).Returns(AppResult.Fail(data: new VerifyOtpResponse()));
            // Act
            var result = await _sut.VerifyOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("424");
            }
        }

        [Fact]
        public async Task VerifyOtpAsync_Should_ReturnBadRequest_When_InvalidOtp()
        {
            // Arrange
            var model = new VerifyOtpDto { PhoneNumber = "**********", OtpCode = "1234", Prefix = "ABCD", RequestId = "requestId" };
            var otp = new Otp { OtpCode = "ABCD-5678", RequestId= "requestId", Msisdn= "**********",CreatedAt = DateTime.Now };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(),order:Order.Descending)
                .Returns(redisValues);

            // Act
            var result = await _sut.VerifyOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
                result.Message.Should().Be(AppResponses.InvalidOtp);
            }
        }

        [Fact]
        public async Task VerifyOtpAsync_Should_ReturnOkResponse_When_OtpVerifiedSuccessfully()
        {
            // Arrange
            var model = new VerifyOtpDto { PhoneNumber = "**********", OtpCode = "1234", Prefix = "ABCD", RequestId = "requestId" };
            var otp = new Otp { OtpCode = "ABCD-1234", Msisdn = model.PhoneNumber, RequestId = model.RequestId,CreatedAt=DateTime.Now };
            var clientApp = new GetBackOfficeAuthConfigStoreResponse { AccountLookUpUrl = "http://example.com" };
            var accountDetails = new PhoneNumberLookupResponse { Code = "200", Data = new PhoneNumberLookupData() };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(),order: Order.Descending)
                .Returns(redisValues);
            _accountLookupService.ClientAppDetails(model.AppId).Returns(clientApp);
            _accountLookupService.LookUpCustomerInfoByPhoneNumber(clientApp.AccountLookUpUrl, model.PhoneNumber).Returns(accountDetails);
            _jwtService.GenerateAppToken(Arg.Any<TokenGenerationModel>()).Returns(AppResult.Success(data: new VerifyOtpResponse()));

            // Act
            var result = await _sut.VerifyOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("200");
                result.Data.Should().NotBeNull();
            }
        }

        [Fact]
        public async Task VerifyOtpAsync_Should_ReturnFailedDependencyError_When_ExceptionThrown()
        {
            // Arrange
            var model = new VerifyOtpDto { PhoneNumber = "**********", OtpCode = "1234", Prefix = "ABCD", RequestId = "requestId" };
            var score = model.PhoneNumber.StringToInt(true);
            var otp = new Otp { OtpCode = "ABCD-1234", Msisdn = model.PhoneNumber, RequestId = model.RequestId };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(),order: Order.Descending)
                .Throws(new Exception("Test exception"));

            // Act
            var result = await _sut.VerifyOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("424");
                result.Message.Should().Be("An error occured, try again later");
            }
        }

        [Fact]
        public async Task ResendOtpAsync_Should_ReturnBadRequest_When_PhoneNumberIsBlacklisted()
        {
            // Arrange
            var model = new ResendOtpDto { PhoneNumber = "**********", RequestId = "valid-request-id" };
            _memoryCacheService.SetContainsAsync("ConsumerBlackListQS", model.PhoneNumber).Returns(true);

            // Act
            var result = await _sut.ResendOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
                result.Message.Should().Be("There seem to be a problem with this account. Contact <EMAIL> for help");
            }
        }

        [Fact]
        public async Task ResendOtpAsync_Should_ReturnBadRequest_When_ValidateOtpDataFails()
        {
            // Arrange
            var model = new ResendOtpDto { PhoneNumber = "**********", RequestId = "invalid-request-id" };
            _memoryCacheService.SetContainsAsync("ConsumerBlackListQS", model.PhoneNumber).Returns(false);

            // Mock ValidateOtpData to return invalid response
            var invalidResponse = CommonResponses.ErrorResponse.BadRequestResponse<GenerateOtpResponse>("Invalid Request Id");
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(new Otp { }) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);

            // Act
            var result = await _sut.ResendOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
                result.Message.Should().Be(AppResponses.InvalidRequestId);
            }
        }

        [Fact]
        public async Task ResendOtpAsync_Should_ReturnOkResponse_When_OtpResentSuccessfully()
        {
            // Arrange
            var model = new ResendOtpDto { PhoneNumber = "**********", RequestId = "valid-request-id" };
            _memoryCacheService.SetContainsAsync("ConsumerBlackListQS", model.PhoneNumber).Returns(false);

            var otp = new Otp
            {
                RequestId = model.RequestId,
                Msisdn = model.PhoneNumber,
                CreatedAt = DateTime.UtcNow,
                ResendUseCount = 0
            };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);

            // Act
            var result = await _sut.ResendOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("200");
                result.Message.Should().Be("OTP resent successfully");
                result.Data.Should().NotBeNull();
                result.Data.OtpPrefix.Should().Be(otp.Prefix);
                result.Data.RequestId.Should().Be(otp.RequestId);
            }
        }

        [Fact]
        public async Task ResendOtpAsync_Should_ReturnFailedDependencyError_When_ExceptionThrown()
        {
            // Arrange
            var model = new ResendOtpDto { PhoneNumber = "**********", RequestId = "valid-request-id" };
            _memoryCacheService.SetContainsAsync("ConsumerBlackListQS", model.PhoneNumber).Returns(false);
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(),order:Order.Descending)
                .Throws(new Exception("Test exception"));

            // Act
            var result = await _sut.ResendOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("424");
                result.Message.Should().Be("An error occured, try again later");
            }
        }
        [Fact]
        public async Task SendWhatsAppOtpAsync_Should_ReturnBadRequest_When_PhoneNumberIsBlacklisted()
        {
            // Arrange
            var model = new GenerateOtpDto { PhoneNumber = "**********", AppId = Guid.NewGuid() };
            _memoryCacheService.SetContainsAsync("ConsumerBlackListQS", model.PhoneNumber).Returns(true);

            // Act
            var result = await _sut.SendWhatsAppOtpAsync(model, "senderId");

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
                result.Message.Should().Be("There seem to be a problem with this account. Contact <EMAIL> for help");
            }
        }

        [Fact]
        public async Task SendWhatsAppOtpAsync_Should_ReturnOkResponse_When_OtpSentSuccessfully()
        {
            // Arrange
            var model = new GenerateOtpDto { PhoneNumber = "**********", AppId = Guid.NewGuid(), CountryCode = "GH" };
            _memoryCacheService.SetContainsAsync("ConsumerBlackListQS", model.PhoneNumber).Returns(false);

            var otp = new Otp
            {
                OtpCode = "1234",
                Prefix = "ABCD",
                RequestId = Guid.NewGuid().ToString("N"),
                Msisdn = model.PhoneNumber,
                SenderId = "senderId",
                CountryCode = model.CountryCode
            };

            _otpCacheRepository.SortedSetAddAsync(Arg.Any<RedisKey>(), Arg.Any<RedisValue>(), Arg.Any<double>()).Returns(true);

            _whatsAppApi.SendMessage(Arg.Any<WhatsAppMessage>())
                .Returns((true, null, null!));

            // Act
            var result = await _sut.SendWhatsAppOtpAsync(model, "senderId");

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("200");
                result.Data.Should().NotBeNull();
            }
        }

        [Fact]
        public async Task SendWhatsAppOtpAsync_Should_ReturnFailedDependencyError_When_WhatsAppApiFails()
        {
            // Arrange
            var model = new GenerateOtpDto { PhoneNumber = "**********", AppId = Guid.NewGuid(), CountryCode = "GH" };
            _memoryCacheService.SetContainsAsync("ConsumerBlackListQS", model.PhoneNumber).Returns(false);

            _whatsAppApi.SendMessage(Arg.Any<WhatsAppMessage>())
                .Throws(new Exception("Test exception"));

            // Act
            var result = await _sut.SendWhatsAppOtpAsync(model, "senderId");

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("424");
                result.Message.Should().Be("An error occured, try again later");
            }
        }

        [Fact]
        public async Task SendWhatsAppOtpAsync_Should_ReturnFailedDependencyError_When_ExceptionThrown()
        {
            // Arrange
            var model = new GenerateOtpDto { PhoneNumber = "**********", AppId = Guid.NewGuid(), CountryCode = "GH" };
            _memoryCacheService.SetContainsAsync("ConsumerBlackListQS", model.PhoneNumber).Returns(false);

            _whatsAppApi.SendMessage(Arg.Any<WhatsAppMessage>())
                .Throws(new ArgumentException("Test Exception"));

            // Act
            var result = await _sut.SendWhatsAppOtpAsync(model, "senderId");

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("424");
                result.Message.Should().Be("An error occured, try again later");
            }
        }

        [Fact]
        public async Task ResendWhatsAppOtpAsync_Should_ReturnBadRequest_When_ValidateOtpDataFails()
        {
            // Arrange
            var model = new ResendOtpDto { PhoneNumber = "**********", RequestId = "invalid-request-id" };

            // Mock ValidateOtpData to return invalid response
            var invalidResponse = CommonResponses.ErrorResponse.BadRequestResponse<GenerateOtpResponse>("Invalid Request Id");
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(new Otp()) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);

            // Act
            var result = await _sut.ResendWhatsAppOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
                result.Message.Should().Be("Hmmm... Something doesn't look right with your login request. Please start the login process again.");
            }
        }

        [Fact]
        public async Task ResendWhatsAppOtpAsync_Should_ReturnOkResponse_When_OtpResentSuccessfully()
        {
            // Arrange
            var model = new ResendOtpDto { PhoneNumber = "**********", RequestId = "valid-request-id" };

            var otp = new Otp
            {
                RequestId = model.RequestId,
                Msisdn = model.PhoneNumber,
                CreatedAt = DateTime.UtcNow,
                ResendUseCount = 0,
                Code = "1234",
                Prefix = "ABCD",
                SenderId = "senderId"
            };

            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);

            _whatsAppApi.SendMessage(Arg.Any<WhatsAppMessage>())
                .Returns((true, null, null!));

            // Act
            var result = await _sut.ResendWhatsAppOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("200");
                result.Data.Should().NotBeNull();
                result.Data.OtpPrefix.Should().Be(otp.Prefix);
                result.Data.RequestId.Should().Be(otp.RequestId);
            }
        }

        [Fact]
        public async Task VerifyOtp2Async_Should_ReturnBadRequest_When_OtpExpired()
        {
            // Arrange
            var model = new VerifyOtpDtoWebCheckout { PhoneNumber = "**********", OtpCode = "1234", RequestId = "requestId" };

            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(new Otp()) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);

            // Act
            var result = await _sut.VerifyOtp2Async(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
                result.Message.Should().Be(AppResponses.InvalidRequestId);
            }
        }

        [Fact]
        public async Task VerifyOtp2Async_Should_ReturnBadRequest_When_ClientAppIsNull()
        {
            // Arrange
            var model = new VerifyOtpDtoWebCheckout { PhoneNumber = "**********", OtpCode = "1234", RequestId = "requestId" };
            var otp = new Otp { OtpCode = "1234", Msisdn = model.PhoneNumber, RequestId = model.RequestId, CreatedAt = DateTime.UtcNow };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp )};
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);
            _accountLookupService.ClientAppDetails(model.AppId).Returns((GetBackOfficeAuthConfigStoreResponse)null!);
            // Act
            var result = await _sut.VerifyOtp2Async(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
            }
        }

        [Fact]
        public async Task VerifyOtp2Async_Should_ReturnBadRequest_When_LookupInformationIsNull()
        {
            // Arrange
            var model = new VerifyOtpDtoWebCheckout { PhoneNumber = "**********", OtpCode = "1234", RequestId = "requestId" };
            var clientApp = new GetBackOfficeAuthConfigStoreResponse { AccountLookUpUrl = "http://example.com" };
            var otp = new Otp { OtpCode = "1234", Msisdn = model.PhoneNumber, RequestId = model.RequestId, CreatedAt = DateTime.UtcNow };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);
            _accountLookupService.ClientAppDetails(model.AppId).Returns(clientApp);
            var accountDetails = new PhoneNumberLookupResponse { Code = "500", Data = new PhoneNumberLookupData() };
            _accountLookupService.LookUpCustomerInfoByPhoneNumber(clientApp.AccountLookUpUrl, model.PhoneNumber).Returns((PhoneNumberLookupResponse)null!);
            // Act
            var result = await _sut.VerifyOtp2Async(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
            }
        }

        [Fact]
        public async Task VerifyOtp2Async_Should_ReturnForbidden_When_LookupInformationReturnsForbidden()
        {
            // Arrange
            var model = new VerifyOtpDtoWebCheckout { PhoneNumber = "**********", OtpCode = "1234", RequestId = "requestId" };
            var clientApp = new GetBackOfficeAuthConfigStoreResponse { AccountLookUpUrl = "http://example.com" };
            var otp = new Otp { OtpCode = "1234", Msisdn = model.PhoneNumber, RequestId = model.RequestId, CreatedAt = DateTime.UtcNow };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);
            _accountLookupService.ClientAppDetails(model.AppId).Returns(clientApp);
            var accountDetails = new PhoneNumberLookupResponse { Code = "403", Data = new PhoneNumberLookupData() };
            _accountLookupService.LookUpCustomerInfoByPhoneNumber(clientApp.AccountLookUpUrl, model.PhoneNumber).Returns(accountDetails);
            // Act
            var result = await _sut.VerifyOtp2Async(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("403");
            }
        }

        [Fact]
        public async Task VerifyOtp2Async_Should_ReturnInternalServerError_When_LookupInformationReturns500()
        {
            // Arrange
            var model = new VerifyOtpDtoWebCheckout { PhoneNumber = "**********", OtpCode = "1234", RequestId = "requestId" };
            var clientApp = new GetBackOfficeAuthConfigStoreResponse { AccountLookUpUrl = "http://example.com" };
            var otp = new Otp { OtpCode = "1234", Msisdn = model.PhoneNumber, RequestId = model.RequestId, CreatedAt = DateTime.UtcNow };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);
            _accountLookupService.ClientAppDetails(model.AppId).Returns(clientApp);
            var accountDetails = new PhoneNumberLookupResponse { Code = "500", Data = new PhoneNumberLookupData() };
            _accountLookupService.LookUpCustomerInfoByPhoneNumber(clientApp.AccountLookUpUrl, model.PhoneNumber).Returns(accountDetails);
            // Act
            var result = await _sut.VerifyOtp2Async(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("500");
            }
        }
        [Fact]
        public async Task VerifyOtp2Async_Should_ReturnBadRequest_When_AccountLookUpUrlForClientAppIsNull()
        {
            // Arrange
            var model = new VerifyOtpDtoWebCheckout { PhoneNumber = "**********", OtpCode = "1234", RequestId = "requestId" };
            var otp = new Otp { OtpCode = "1234", Msisdn = model.PhoneNumber, RequestId = model.RequestId, CreatedAt = DateTime.UtcNow };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);
            _accountLookupService.ClientAppDetails(model.AppId).Returns(new GetBackOfficeAuthConfigStoreResponse()
            {
                AccountLookUpUrl = null
            });
            // Act
            var result = await _sut.VerifyOtp2Async(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
            }
        }

        [Fact]
        public async Task VerifyOtp2Async_Should_ReturnForbidden_When_OtpAlreadyVerified()
        {
            // Arrange
            var model = new VerifyOtpDtoWebCheckout { PhoneNumber = "**********", OtpCode = "1234", RequestId = "requestId" };
            var otp = new Otp { IsVerified = true, CreatedAt = DateTime.UtcNow };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);

            // Act
            var result = await _sut.VerifyOtp2Async(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("403");
                result.Message.Should().Be("OTP already verified");
            }
        }

        [Fact]
        public async Task VerifyOtp2Async_Should_ReturnBadRequest_When_InvalidRequestId()
        {
            // Arrange
            var model = new VerifyOtpDtoWebCheckout { PhoneNumber = "**********", OtpCode = "1234", RequestId = "requestId" };
            var otp = new Otp { RequestId = "invalidRequestId", CreatedAt = DateTime.UtcNow };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);

            // Act
            var result = await _sut.VerifyOtp2Async(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
                result.Message.Should().Be(AppResponses.InvalidRequestId);
            }
        }

        [Fact]
        public async Task VerifyOtp2Async_Should_ReturnForbidden_When_OtpAttemptsExceed()
        {
            // Arrange
            var model = new VerifyOtpDtoWebCheckout { PhoneNumber = "**********", OtpCode = "1234", RequestId = "requestId" };
            var otp = new Otp { OTPUseCount = 3, CreatedAt = DateTime.UtcNow, RequestId = "requestId" };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);

            // Act
            var result = await _sut.VerifyOtp2Async(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("403");
                result.Message.Should().Be(AppResponses.OtpAttemptsExceed);
            }
        }

        [Fact]
        public async Task VerifyOtp2Async_Should_ReturnBadRequest_When_InvalidMobileNumber()
        {
            // Arrange
            var model = new VerifyOtpDtoWebCheckout { PhoneNumber = "**********", OtpCode = "1234", RequestId = "requestId" };
            var otp = new Otp { Msisdn = "0987654321", CreatedAt = DateTime.UtcNow, RequestId = "requestId" };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);

            // Act
            var result = await _sut.VerifyOtp2Async(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
                result.Message.Should().Be(AppResponses.InvalidMobileNumber);
            }
        }

        [Fact]
        public async Task VerifyOtp2Async_Should_ReturnBadRequest_When_InvalidOtp()
        {
            // Arrange
            var model = new VerifyOtpDtoWebCheckout { PhoneNumber = "**********", OtpCode = "1234", RequestId = "requestId" };
            var otp = new Otp { OtpCode = "5678", Msisdn = model.PhoneNumber, RequestId = model.RequestId, CreatedAt = DateTime.UtcNow };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);

            // Act
            var result = await _sut.VerifyOtp2Async(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
                result.Message.Should().Be(AppResponses.InvalidOtp);
            }
        }

        [Fact]
        public async Task VerifyOtp2Async_Should_ReturnOkResponse_When_OtpVerifiedSuccessfully()
        {
            // Arrange
            var model = new VerifyOtpDtoWebCheckout { PhoneNumber = "**********", OtpCode = "1234", RequestId = "requestId", AppId = Guid.NewGuid() };
            var otp = new Otp { OtpCode = "1234", Msisdn = model.PhoneNumber, RequestId = model.RequestId, CreatedAt = DateTime.UtcNow };
            var clientApp = new GetBackOfficeAuthConfigStoreResponse { AccountLookUpUrl = "http://example.com" };
            var accountDetails = new PhoneNumberLookupResponse { Code = "200", Data = new PhoneNumberLookupData() };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);
            _accountLookupService.ClientAppDetails(model.AppId).Returns(clientApp);
            _accountLookupService.LookUpCustomerInfoByPhoneNumber(clientApp.AccountLookUpUrl, model.PhoneNumber).Returns(accountDetails);
            _jwtService.GenerateAppToken(Arg.Any<TokenGenerationModel>()).Returns(AppResult.Success(data: new VerifyOtpResponse()));

            // Act
            var result = await _sut.VerifyOtp2Async(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("200");
                result.Data.Should().NotBeNull();
            }
        }

        [Fact]
        public async Task VerifyOtp2Async_Should_ReturnFailedDependencyError_When_ExceptionThrown()
        {
            // Arrange
            var model = new VerifyOtpDtoWebCheckout { PhoneNumber = "**********", OtpCode = "1234", RequestId = "requestId" };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(new Otp()) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Throws(new Exception("Test exception"));

            // Act
            var result = await _sut.VerifyOtp2Async(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("424");
                result.Message.Should().Be("An error occured, try again later");
            }
        }
        [Fact]
        public async Task VerifyWhatsAppOtpAsync_Should_ReturnBadRequest_When_OtpExpired()
        {
            // Arrange
            var model = new VerifyOtpDto { PhoneNumber = "**********", OtpCode = "1234", Prefix = "ABCD", RequestId = "requestId" };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(new Otp()) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);

            // Act
            var result = await _sut.VerifyWhatsAppOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
                result.Message.Should().Be(AppResponses.InvalidRequestId);
            }
        }

        [Fact]
        public async Task VerifyWhatsAppOtpAsync_Should_ReturnForbidden_When_OtpAlreadyVerified()
        {
            // Arrange
            var model = new VerifyOtpDto { PhoneNumber = "**********", OtpCode = "1234", Prefix = "ABCD", RequestId = "requestId" };
            var otp = new Otp { IsVerified = true, CreatedAt = DateTime.UtcNow };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);

            // Act
            var result = await _sut.VerifyWhatsAppOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("403");
                result.Message.Should().Be("OTP already verified");
            }
        }

        [Fact]
        public async Task VerifyWhatsAppOtpAsync_Should_ReturnBadRequest_When_InvalidRequestId()
        {
            // Arrange
            var model = new VerifyOtpDto { PhoneNumber = "**********", OtpCode = "1234", Prefix = "ABCD", RequestId = "requestId" };
            var otp = new Otp { RequestId = "invalidRequestId", CreatedAt = DateTime.UtcNow };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);

            // Act
            var result = await _sut.VerifyWhatsAppOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
                result.Message.Should().Be(AppResponses.InvalidRequestId);
            }
        }

        [Fact]
        public async Task VerifyWhatsAppOtpAsync_Should_ReturnForbidden_When_OtpAttemptsExceed()
        {
            // Arrange
            var model = new VerifyOtpDto { PhoneNumber = "**********", OtpCode = "1234", Prefix = "ABCD", RequestId = "requestId" };
            var otp = new Otp { OTPUseCount = 3, CreatedAt = DateTime.UtcNow, RequestId = "requestId" };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);

            // Act
            var result = await _sut.VerifyWhatsAppOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("403");
                result.Message.Should().Be(AppResponses.OtpAttemptsExceed);
            }
        }

        [Fact]
        public async Task VerifyWhatsAppOtpAsync_Should_ReturnBadRequest_When_InvalidMobileNumber()
        {
            // Arrange
            var model = new VerifyOtpDto { PhoneNumber = "**********", OtpCode = "1234", Prefix = "ABCD", RequestId = "requestId" };
            var otp = new Otp { Msisdn = "0987654321", CreatedAt = DateTime.UtcNow, RequestId = "requestId" };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);

            // Act
            var result = await _sut.VerifyWhatsAppOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
                result.Message.Should().Be(AppResponses.InvalidMobileNumber);
            }
        }

        [Fact]
        public async Task VerifyWhatsAppOtpAsync_Should_ReturnBadRequest_When_InvalidOtp()
        {
            // Arrange
            var model = new VerifyOtpDto { PhoneNumber = "**********", OtpCode = "1234", Prefix = "ABCD", RequestId = "requestId" };
            var otp = new Otp { OtpCode = "5678", Msisdn = model.PhoneNumber, RequestId = model.RequestId, CreatedAt = DateTime.UtcNow };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);

            // Act
            var result = await _sut.VerifyWhatsAppOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
                result.Message.Should().Be(AppResponses.InvalidOtp);
            }
        }

        [Fact]
        public async Task VerifyWhatsAppOtpAsync_Should_ReturnOkResponse_When_OtpVerifiedSuccessfully()
        {
            // Arrange
            var model = new VerifyOtpDto { PhoneNumber = "**********", OtpCode = "1234", Prefix = "ABCD", RequestId = "requestId", AppId = Guid.NewGuid() };
            var otp = new Otp { OtpCode = "ABCD-1234", Msisdn = model.PhoneNumber, RequestId = model.RequestId, CreatedAt = DateTime.UtcNow };
            var clientApp = new GetBackOfficeAuthConfigStoreResponse { AccountLookUpUrl = "http://example.com" };
            var accountDetails = new PhoneNumberLookupResponse { Code = "200", Data = new PhoneNumberLookupData() };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);
            _accountLookupService.ClientAppDetails(model.AppId).Returns(clientApp);
            _accountLookupService.LookUpCustomerInfoByPhoneNumber(clientApp.AccountLookUpUrl, model.PhoneNumber).Returns(accountDetails);
            _jwtService.GenerateAppToken(Arg.Any<TokenGenerationModel>()).Returns(AppResult.Success(data: new VerifyOtpResponse()));

            // Act
            var result = await _sut.VerifyWhatsAppOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("200");
                result.Data.Should().NotBeNull();
            }
        }

        [Fact]
        public async Task VerifyWhatsAppOtpAsync_Should_ReturnBadRequest_When_ClientAppIsNulll()
        {
            // Arrange
            var model = new VerifyOtpDto { PhoneNumber = "**********", OtpCode = "1234", Prefix = "ABCD", RequestId = "requestId" };
            var otp = new Otp { OtpCode = "ABCD-1234", Msisdn = model.PhoneNumber, RequestId = model.RequestId, CreatedAt = DateTime.UtcNow, Prefix = "ABCD" };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);
            _accountLookupService.ClientAppDetails(model.AppId).Returns((GetBackOfficeAuthConfigStoreResponse)null!);
            // Act
            var result = await _sut.VerifyWhatsAppOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
            }
        }

        [Fact]
        public async Task VerifyWhatsAppOtpAsync_Should_ReturnBadRequest_When_AccountLookUpUrlForClientAppIsNulll()
        {
            // Arrange
            var model = new VerifyOtpDto { PhoneNumber = "**********", OtpCode = "1234", Prefix = "ABCD", RequestId = "requestId" };
            var otp = new Otp { OtpCode = "ABCD-1234", Msisdn = model.PhoneNumber, RequestId = model.RequestId, CreatedAt = DateTime.UtcNow,Prefix="ABCD" };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);
            _accountLookupService.ClientAppDetails(model.AppId).Returns(new GetBackOfficeAuthConfigStoreResponse()
            {
                AccountLookUpUrl = null
            });
            // Act
            var result = await _sut.VerifyWhatsAppOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
            }
        }
        [Fact]
        public async Task VerifyWhatsAppOtpAsync_Should_ReturnBadRequest_When_AccountDetailsIsNull()
        {
            // Arrange
            var model = new VerifyOtpDto { PhoneNumber = "**********", OtpCode = "1234", Prefix = "ABCD", RequestId = "requestId", AppId = Guid.NewGuid() };
            var otp = new Otp { OtpCode = "ABCD-1234", Msisdn = model.PhoneNumber, RequestId = model.RequestId, CreatedAt = DateTime.UtcNow };
            var clientApp = new GetBackOfficeAuthConfigStoreResponse { AccountLookUpUrl = "http://example.com" };
            PhoneNumberLookupResponse accountDetails = null!;
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);
            _accountLookupService.ClientAppDetails(model.AppId).Returns(clientApp);
            _accountLookupService.LookUpCustomerInfoByPhoneNumber(clientApp.AccountLookUpUrl, model.PhoneNumber).Returns(accountDetails);
            // Act
            var result = await _sut.VerifyWhatsAppOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("400");
            }
        }
        [Fact]
        public async Task VerifyWhatsAppOtpAsync_Should_ReturnForbidden_When_LookUpCustomerInfoByPhoneNumberReturns403()
        {
            // Arrange
            var model = new VerifyOtpDto { PhoneNumber = "**********", OtpCode = "1234", Prefix = "ABCD", RequestId = "requestId", AppId = Guid.NewGuid() };
            var otp = new Otp { OtpCode = "ABCD-1234", Msisdn = model.PhoneNumber, RequestId = model.RequestId, CreatedAt = DateTime.UtcNow };
            var clientApp = new GetBackOfficeAuthConfigStoreResponse { AccountLookUpUrl = "http://example.com" };
            var accountDetails = new PhoneNumberLookupResponse { Code = "403", Data = new PhoneNumberLookupData() };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);
            _accountLookupService.ClientAppDetails(model.AppId).Returns(clientApp);
            _accountLookupService.LookUpCustomerInfoByPhoneNumber(clientApp.AccountLookUpUrl, model.PhoneNumber).Returns(accountDetails);
            // Act
            var result = await _sut.VerifyWhatsAppOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("403");
            }
        }
        [Fact]
        public async Task VerifyWhatsAppOtpAsync_Should_ReturnInternalServerError_When_LookUpCustomerInfoByPhoneNumberReturns500()
        {
            // Arrange
            var model = new VerifyOtpDto { PhoneNumber = "**********", OtpCode = "1234", Prefix = "ABCD", RequestId = "requestId", AppId = Guid.NewGuid() };
            var otp = new Otp { OtpCode = "ABCD-1234", Msisdn = model.PhoneNumber, RequestId = model.RequestId, CreatedAt = DateTime.UtcNow };
            var clientApp = new GetBackOfficeAuthConfigStoreResponse { AccountLookUpUrl = "http://example.com" };
            var accountDetails = new PhoneNumberLookupResponse { Code = "500", Data = new PhoneNumberLookupData() };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);
            _accountLookupService.ClientAppDetails(model.AppId).Returns(clientApp);
            _accountLookupService.LookUpCustomerInfoByPhoneNumber(clientApp.AccountLookUpUrl, model.PhoneNumber).Returns(accountDetails);
            // Act
            var result = await _sut.VerifyWhatsAppOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("500");
            }
        }
        [Fact]
        public async Task VerifyWhatsAppOtpAsync_Should_ReturnFailedDependencyError_When_TokenCannotBeGenerated()
        {
            // Arrange
            var model = new VerifyOtpDto { PhoneNumber = "**********", OtpCode = "1234", Prefix = "ABCD", RequestId = "requestId", AppId = Guid.NewGuid() };
            var otp = new Otp { OtpCode = "ABCD-1234", Msisdn = model.PhoneNumber, RequestId = model.RequestId, CreatedAt = DateTime.UtcNow };
            var clientApp = new GetBackOfficeAuthConfigStoreResponse { AccountLookUpUrl = "http://example.com" };
            var accountDetails = new PhoneNumberLookupResponse { Code = "200", Data = new PhoneNumberLookupData() };
            var redisValues = new RedisValue[] { JsonConvert.SerializeObject(otp) };
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);
            _accountLookupService.ClientAppDetails(model.AppId).Returns(clientApp);
            _accountLookupService.LookUpCustomerInfoByPhoneNumber(clientApp.AccountLookUpUrl, model.PhoneNumber).Returns(accountDetails);
            _jwtService.GenerateAppToken(Arg.Any<TokenGenerationModel>()).Returns(AppResult.Fail(data: new VerifyOtpResponse()));
            // Act
            var result = await _sut.VerifyWhatsAppOtpAsync(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Code.Should().Be("424");
            }
        }

    }
}
