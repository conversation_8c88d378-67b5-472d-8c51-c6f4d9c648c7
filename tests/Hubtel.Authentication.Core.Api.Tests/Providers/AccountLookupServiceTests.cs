using Autofac.Core;
using FluentAssertions;
using FluentAssertions.Execution;
using Flurl.Http.Testing;
using Hubtel.Authentication.Commons.Extensions;
using Hubtel.Authentication.Core.Api.Dtos;
using Hubtel.Authentication.Core.Api.Models.Responses;
using Hubtel.Authentication.Core.Api.Services;
using Hubtel.Authentication.Core.Api.Services.Provider;
using Hubtel.Redis.Sdk.Services;
using Microsoft.Extensions.Logging;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using StackExchange.Redis;
using System.Net;
using System.Text.Json;
using NSubstitute.ReturnsExtensions;
using Xunit;

namespace Hubtel.Authentication.Core.Api.Tests.Providers
{
    public class AccountLookupServiceTests : IDisposable
    {
        private readonly AccountLookupService _accountLookupService;
        private readonly ILogger<AccountLookupService> _logger;
        private readonly HttpTest _httpTest;
        private readonly string _testurl = "http://test.com";
        private readonly IDatabase _redisDatabase;
        private readonly IAuthenticationConfigStoreService _authenticationConfigStore;

        public AccountLookupServiceTests()
        {
            _httpTest = new HttpTest();
            _logger = Substitute.For<ILogger<AccountLookupService>>();
            _authenticationConfigStore = Substitute.For<IAuthenticationConfigStoreService>();
            var redisCacheRepository = Substitute.For<IMultiRedisHostCacheRepository>();
            _redisDatabase = Substitute.For<IDatabase>();
            redisCacheRepository
                .GetDb(Arg.Any<string>(), Arg.Any<string>())
                .Returns(_redisDatabase);

            _accountLookupService = new AccountLookupService(
                _authenticationConfigStore,
                redisCacheRepository,
                _logger
            );
        }

        [Fact]
        public async Task RetrieveCachedQrInfo_Should_ReturnQrInfo_When_CacheHit()
        {
            // Arrange
            var applicationId = Guid.NewGuid();
            var qrData = "testQrData";
            var phoneNumber = "*********";
            var cacheKey = $"{nameof(qrData)}:{applicationId}:{phoneNumber}:{qrData}";
            var expectedQrInfo = "cachedQrInfo";

            _redisDatabase.StringGetAsync(cacheKey).Returns(expectedQrInfo);

            // Act
            var result = await _accountLookupService.RetrieveCachedQrInfo(applicationId, qrData, phoneNumber);

            // Assert
            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Should().Be(expectedQrInfo);
                await _redisDatabase.Received(1).StringGetAsync(cacheKey);
                _logger.Received(2).Log<object>(LogLevel.Information, 0, Arg.Any<object>(), Arg.Any<Exception>(), Arg.Any<Func<object, Exception, string>>()!);
            }
        }

        [Fact]
        public async Task RetrieveCachedQrInfo_Should_ReturnNull_When_CacheMiss()
        {
            // Arrange
            var applicationId = Guid.NewGuid();
            var qrData = "testQrData";
            var phoneNumber = "*********";
            var cacheKey = $"{nameof(qrData)}:{applicationId}:{phoneNumber}:{qrData}";

            _redisDatabase.StringGetAsync(cacheKey).Returns(RedisValue.Null);

            // Act
            var result = await _accountLookupService.RetrieveCachedQrInfo(applicationId, qrData, phoneNumber);

            // Assert
            using (new AssertionScope())
            {
                result.Should().BeEmpty();
                await _redisDatabase.Received(1).StringGetAsync(cacheKey);
                _logger.Received(1).Log<object>(LogLevel.Information, 0, Arg.Any<object>(), Arg.Any<Exception>(), Arg.Any<Func<object, Exception, string>>()!);
            }
        }
        [Fact]
        public async Task SaveQrInfoToCache_Should_ReturnTrue_When_Successful()
        {
            // Arrange
            var applicationId = Guid.NewGuid();
            var qrData = "testQrData";
            var phoneNumber = "*********";
            var cacheKey = $"{nameof(qrData)}:{applicationId}:{phoneNumber}:{qrData}";
            var expiry = 10;

            _redisDatabase.StringSetAsync(cacheKey, qrData, TimeSpan.FromMinutes(expiry)).Returns(true);

            // Act
            var result = await _accountLookupService.SaveQrInfoToCache(applicationId, qrData, phoneNumber, expiry);

            // Assert
            using (new AssertionScope())
            {
                result.Should().BeTrue();
                await _redisDatabase.Received(1).StringSetAsync(cacheKey, qrData, TimeSpan.FromMinutes(expiry));
            }
        }

        [Fact]
        public async Task SaveQrInfoToCache_Should_ReturnFalse_When_Unsuccessful()
        {
            // Arrange
            var applicationId = Guid.NewGuid();
            var qrData = "testQrData";
            var phoneNumber = "*********";
            var cacheKey = $"{nameof(qrData)}:{applicationId}:{phoneNumber}:{qrData}";
            var expiry = 10;

            _redisDatabase.StringSetAsync(cacheKey, qrData, TimeSpan.FromMinutes(expiry)).Returns(false);

            // Act
            var result = await _accountLookupService.SaveQrInfoToCache(applicationId, qrData, phoneNumber, expiry);

            // Assert
            using (new AssertionScope())
            {
                result.Should().BeFalse();
                await _redisDatabase.Received(1).StringSetAsync(cacheKey, qrData, TimeSpan.FromMinutes(expiry));
            }
        }

        [Fact]
        public async Task SaveQrInfoToCache_Should_LogError_When_ExceptionOccurs()
        {
            // Arrange
            var applicationId = Guid.NewGuid();
            var qrData = "testQrData";
            var phoneNumber = "*********";
            var cacheKey = $"{nameof(qrData)}:{applicationId}:{phoneNumber}:{qrData}";
            var expiry = 10;
            var exception = new Exception("Redis error");

            _redisDatabase.StringSetAsync(cacheKey, qrData, TimeSpan.FromMinutes(expiry)).Throws(exception);

            // Act
            Func<Task> act = async () => await _accountLookupService.SaveQrInfoToCache(applicationId, qrData, phoneNumber, expiry);

            // Assert
            using (new AssertionScope())
            {
                await act.Should().ThrowAsync<Exception>().WithMessage("Redis error");
                await _redisDatabase.Received(1).StringSetAsync(cacheKey, qrData, TimeSpan.FromMinutes(expiry));
            }
        }
        
        [Fact]
        public async Task RetrieveCachedQrInfo_Should_LogError_And_ReturnNull_When_ExceptionOccurs()
        {
            // Arrange
            var applicationId = Guid.NewGuid();
            var qrData = "testQrData";
            var phoneNumber = "*********";
            var cacheKey = $"{nameof(qrData)}:{applicationId}:{phoneNumber}:{qrData}";
            var exception = new Exception("Redis error");

            _redisDatabase.StringGetAsync(cacheKey).Throws(exception);

            // Act
            var result = await _accountLookupService.RetrieveCachedQrInfo(applicationId, qrData, phoneNumber);

            // Assert
            using (new AssertionScope())
            {
                result.Should().BeNull(); 

                await _redisDatabase.Received(1).StringGetAsync(cacheKey);
               
                _logger.Received(1).Log<object>(
                    LogLevel.Error,
                    0,
                    Arg.Is<object>(o => o.ToString()!.Contains("Error occurred while retrieving account information")),
                    exception,
                    Arg.Any<Func<object, Exception, string>>()!); 
            }
        }


        [Fact]
        public async Task LookupCustomerInformation_Should_ReturnNull_When_LookUpUrlIsEmpty()
        {
            // Act
            var result = await _accountLookupService.LookUpCustomerInfoByPhoneNumber(string.Empty, "*********");

            // Assert
            result.Should().BeNull();
            _logger.Received().LogWarning(
                "lookUpUrl is null, empty, or consists only of white-space characters. Returning null");
        }

        [Fact]
        public async Task LookupCustomerInformation_Should_ReturnAccountLookupResponse_When_ApiResponseIsSuccessful()
        {
            // Arrange
            var phoneNumber = "*********";
            var email = "<EMAIL>";
            var expected = new PhoneNumberLookupResponse()
            {
                Code = "200",
                Data = new PhoneNumberLookupData
                {
                    MobileNumber = phoneNumber,
                    Email = email,
                    TokenData = new Dictionary<string, string>
                    {
                        { "email", email },
                        { "phone", phoneNumber },
                    }
                },
                Message = "success"
            };

            _httpTest.RespondWithJson(expected);

            // Act
            var result = await _accountLookupService.LookUpCustomerInfoByPhoneNumber(_testurl, phoneNumber);

            // Assert
            using (new AssertionScope())
            {
                _httpTest.ShouldHaveCalled($"{_testurl}/{phoneNumber}");
                result.Should().NotBeNull();
                result.Should().BeEquivalentTo(expected);
            }
        }

        [Fact]
        public async Task LookupCustomerInformation_Should_ReturnNull_When_ApiResponseIsBadRequest()
        {
            // Arrange
            _httpTest.RespondWith("Bad Request", 400);
            var phoneNumber = "*********";
            // Act
            var result = await _accountLookupService.LookUpCustomerInfoByPhoneNumber(_testurl, phoneNumber);

            // Assert
            using (new AssertionScope())
            {
                _httpTest.ShouldHaveCalled($"{_testurl}/{phoneNumber}");
                result.Should().BeNull();
            }
        }
        [Fact]
        public async Task LookupCustomerInformation_Should_ReturnAccountLookupResponse_When_ApiResponseIsFailedDependency()
        {
            var expectedResponse = new PhoneNumberLookupResponse
            {
                Code = "424",
                Message = "Sorry, we encountered an unexpected error while processing your request. Please contact support if the problem persists."
            };
            // Arrange
            _httpTest.RespondWithJson(expectedResponse, 424);
            var phoneNumber = "*********";
            // Act
            var result = await _accountLookupService.LookUpCustomerInfoByPhoneNumber(_testurl, phoneNumber);

            // Assert
            using (new AssertionScope())
            {
                _httpTest.ShouldHaveCalled($"{_testurl}/{phoneNumber}");
                result.Should().NotBeNull();
                result.Code.Should().Be(expectedResponse.Code);
                result.Message.Should().Contain(expectedResponse.Message);
            }
        }
        [Fact]
        public async Task LookupCustomerInformation_Should_ReturnAccountLookupResponse_When_ApiResponseIsInternalServerError()
        {
            var expectedResponse = new PhoneNumberLookupResponse
            {
                Code = "500",
                Message = "Sorry, we encountered an unexpected error while processing your request. Please contact support if the problem persists."
            };
            // Arrange
            _httpTest.RespondWithJson(expectedResponse, 500);
            var phoneNumber = "*********";
            // Act
            var result = await _accountLookupService.LookUpCustomerInfoByPhoneNumber(_testurl, phoneNumber);

            // Assert
            using (new AssertionScope())
            {
                _httpTest.ShouldHaveCalled($"{_testurl}/{phoneNumber}");
                result.Should().NotBeNull();
                result.Code.Should().Be(expectedResponse.Code);
                result.Message.Should().Contain(expectedResponse.Message);
            }
        }

        [Fact]
        public async Task LookupCustomerInformation_Should_ReturnNull_When_ApiResponseIsNotFound()
        {
            // Arrange
            _httpTest.RespondWith("Resource not found", 404);

            // Act
            var result = await _accountLookupService.LookUpCustomerInfoByPhoneNumber(_testurl, "*********");

            // Assert
            _httpTest.ShouldHaveCalled($"{_testurl}/*");
            result.Code.Should().Be("404");
        }

        [Fact]
        public async Task LookupCustomerInformation_Should_ReturnNull_When_ApiResponseIsNotFound_AndCustomMessageReturnsFalse()
        {
            // Arrange
            _httpTest.RespondWith(string.Empty, 404);

            // Act
            var result = await _accountLookupService.LookUpCustomerInfoByPhoneNumber(_testurl, "*********");

            // Assert
            _httpTest.ShouldHaveCalled($"{_testurl}/*");
            result.Code.Should().Be("404");
        }

        [Fact]
        public async Task LookupCustomerInformation_Should_ReturnNull_When_ApiResponseIsNotFound_AndCustomMessageReturnsTrue()
        {
            var jsonContent = """
                                {
                  "code": "404",
                  "message": "Custom Message To Show",
                  "data": {
                    
                  }
                }
                """;
            // Arrange
            _httpTest.RespondWith(jsonContent, 404);

            // Act
            var result = await _accountLookupService.LookUpCustomerInfoByPhoneNumber(_testurl, "*********");

            // Assert
            _httpTest.ShouldHaveCalled($"{_testurl}/*");
            result.Code.Should().Be("404");
            result.Message.Should().Contain("Custom Message To Show");
        }
        [Fact]
        public async Task LookUpCustomerInfoByPhoneNumber_Should_ReturnAccountLookupResponse_When_ApiResponseIsSuccessful_WithLinkedEmail()
        {
            // Arrange
            var phoneNumber = "*********";
            var linkedEmail = "<EMAIL>";
            var email = "<EMAIL>";
            var expected = new PhoneNumberLookupResponse()
            {
                Code = "200",
                Data = new PhoneNumberLookupData
                {
                    MobileNumber = phoneNumber,
                    Email = email,
                    TokenData = new Dictionary<string, string>
                    {
                        { "email", email },
                        { "phone", phoneNumber },
                    }
                },
                Message = "success"
            };

            _httpTest.RespondWithJson(expected);

            // Act
            var result = await _accountLookupService.LookUpCustomerInfoByPhoneNumber(_testurl, phoneNumber, linkedEmail);
            var urlencoded = Uri.EscapeDataString(linkedEmail);

            // Assert
            using (new AssertionScope())
            {
                _httpTest.ShouldHaveCalled($"{_testurl}/{phoneNumber}?linkedEmail={urlencoded}");
                result.Should().NotBeNull();
                result.Should().BeEquivalentTo(expected);
            }
        }

        [Fact]
        public async Task LookupCustomerInformation_Should_ReturnAccountLookupResponse_When_ApiResponseIsForbidden()
        {
            // Arrange
            var expectedResponse = new PhoneNumberLookupResponse
            {
                Code = "403",
                Message = "Unable to log in at this time. Please contact customer support for assistance."
            };
            _httpTest.RespondWithJson(expectedResponse, 403);

            // Act
            var result = await _accountLookupService.LookUpCustomerInfoByPhoneNumber(_testurl, "*********");

            // Assert
            using (new AssertionScope())
            {
                _httpTest.ShouldHaveCalled($"{_testurl}/*");
                result.Should().NotBeNull();
                result.Code.Should().Be(expectedResponse.Code);
                result.Message.Should().Contain(expectedResponse.Message);
            }
        }

        [Fact]
        public async Task LookupCustomerInformation_Should_ReturnNull_When_UnexpectedStatusCodeIsReceived()
        {
            // Arrange
            _httpTest.RespondWith("could not get data", 401);

            // Act
            var result = await _accountLookupService.LookUpCustomerInfoByPhoneNumber(_testurl, "*********");

            // Assert
            result.Should().BeNull();
            _logger.Received().Log<object>(LogLevel.Error, 0, Arg.Is<object>(arg => arg.ToString()!.Contains($"failed with status code {HttpStatusCode.Unauthorized}")), Arg.Any<Exception>(), Arg.Any<Func<object, Exception, string>>()!);

        }
        [Fact]
        public async Task LookUpCustomerInfoByEmail_Should_ReturnNull_When_LookUpUrlIsEmpty()
        {
            // Act
            var result = await _accountLookupService.LookUpCustomerInfoByEmail(string.Empty, "<EMAIL>");

            // Assert
            result.Should().BeNull();
            _logger.Received().LogWarning(
                "lookUpUrl is null, empty, or consists only of white-space characters. Returning null");
        }

        [Fact]
        public async Task LookUpCustomerInfoByEmail_Should_ReturnEmailLookUpResponse_When_ApiResponseIsSuccessful()
        {
            // Arrange
            var email = "<EMAIL>";
            var expected = new EmailLookUpResponse
            {
                Code = "200",
                Data = new EmailLookUpData
                {
                    PhoneNumbers = [new PhoneNumber() { CountryCode = "233", Number = "************" }],
                    TokenData = new Dictionary<string, string>
                    {
                        { "email", email }
                    }
                },
                Message = "success"
            };

            _httpTest.RespondWithJson(expected);

            // Act
            var result = await _accountLookupService.LookUpCustomerInfoByEmail(_testurl, email);

            // Assert
            using (new AssertionScope())
            {
                _httpTest.ShouldHaveCalled($"{_testurl}/{email}");
                result.Should().NotBeNull();
                result.Should().BeEquivalentTo(expected);
            }
        }

        [Fact]
        public async Task LookUpCustomerInfoByEmail_Should_ReturnNull_When_ApiResponseIsBadRequest()
        {
            // Arrange
            _httpTest.RespondWith("Bad Request", 400);
            var email = "<EMAIL>";

            // Act
            var result = await _accountLookupService.LookUpCustomerInfoByEmail(_testurl, email);

            // Assert
            using (new AssertionScope())
            {
                _httpTest.ShouldHaveCalled($"{_testurl}/{email}");
                result.Should().BeNull();
            }
        }

        [Fact]
        public async Task LookUpCustomerInfoByEmail_Should_ReturnNull_When_ApiResponseIsNotFound()
        {
            // Arrange
            _httpTest.RespondWith("Resource not found", 404);
            var email = "<EMAIL>";

            // Act
            var result = await _accountLookupService.LookUpCustomerInfoByEmail(_testurl, email);

            // Assert
            using (new AssertionScope())
            {
                _httpTest.ShouldHaveCalled($"{_testurl}/{email}");
                result.Should().BeNull();
            }
        }

        [Fact]
        public async Task LookUpCustomerInfoByEmail_Should_ReturnEmailLookUpResponse_When_ApiResponseIsForbidden()
        {
            // Arrange
            var email = "<EMAIL>";
            var expectedResponse = new EmailLookUpResponse
            {
                Code = "403",
                Message = "Sorry, You do not have permission to access this resource."
            };
            _httpTest.RespondWithJson(expectedResponse, 403);

            // Act
            var result = await _accountLookupService.LookUpCustomerInfoByEmail(_testurl, email);

            // Assert
            using (new AssertionScope())
            {
                _httpTest.ShouldHaveCalled($"{_testurl}/{email}");
                result.Should().NotBeNull();
                result.Code.Should().Be(expectedResponse.Code);
                result.Message.Should().Contain(expectedResponse.Message);
            }
        }

        [Fact]
        public async Task LookUpCustomerInfoByEmail_Should_ReturnEmailLookUpResponse_When_ApiResponseIsForbiddenAndCustomIsNotMessageProvided()
        {
            // Arrange
            var email = "<EMAIL>";
            var expectedResponse = new EmailLookUpResponse
            {
                Code = "403"
            };
            _httpTest.RespondWithJson(expectedResponse, 403);

            // Act
            var result = await _accountLookupService.LookUpCustomerInfoByEmail(_testurl, email);

            // Assert
            using (new AssertionScope())
            {
                _httpTest.ShouldHaveCalled($"{_testurl}/{email}");
                result.Should().NotBeNull();
                result.Code.Should().Be(expectedResponse.Code);
                result.Message.Should().Contain("Sorry, You do not have permission to access this resource.");
            }
        }

        [Fact]
        public async Task LookUpCustomerInfoByEmail_Should_ReturnEmailLookUpResponse_When_ApiResponseIsInternalServerError()
        {
            var expectedResponse = new PhoneNumberLookupResponse
            {
                Code = "500",
                Message = "Unable to log in at this time. Please contact customer support for assistance."
            };
            // Arrange
            _httpTest.RespondWithJson(expectedResponse, 500);
            var email = "<EMAIL>";

            // Act
            var result = await _accountLookupService.LookUpCustomerInfoByEmail(_testurl, email);

            // Assert
            using (new AssertionScope())
            {
                _httpTest.ShouldHaveCalled($"{_testurl}/{email}");
                result.Should().NotBeNull();
                result.Code.Should().Be(expectedResponse.Code);
                result.Message.Should().Contain(expectedResponse.Message);
            }
        }

        [Fact]
        public async Task LookUpCustomerInfoByEmail_Should_ReturnEmailLookUpResponse_When_ApiResponseIsFailedDependency()
        {
            var expectedResponse = new PhoneNumberLookupResponse
            {
                Code = "424",
                Message = "Unable to log in at this time. Please contact customer support for assistance."
            };
            // Arrange
            _httpTest.RespondWithJson(expectedResponse, 424);
            var email = "<EMAIL>";

            // Act
            var result = await _accountLookupService.LookUpCustomerInfoByEmail(_testurl, email);

            // Assert
            using (new AssertionScope())
            {
                _httpTest.ShouldHaveCalled($"{_testurl}/{email}");
                _logger.Received().Log<object>(LogLevel.Error, 0, Arg.Any<object>(), Arg.Any<Exception>(), Arg.Any<Func<object, Exception, string>>()!);
                result.Should().NotBeNull();
                result.Code.Should().Be(expectedResponse.Code);
                result.Message.Should().Contain(expectedResponse.Message);
            }
        }

        [Fact]
        public async Task LookUpCustomerInfoByEmail_Should_LogError_When_UnexpectedStatusCodeIsReceived()
        {
            // Arrange
            var email = "<EMAIL>";

            _httpTest.RespondWith("some unexpectd status code", 401);

            // Act
            var result = await _accountLookupService.LookUpCustomerInfoByEmail(_testurl, email);

            // Assert
            using (new AssertionScope())
            {
                result.Should().BeNull();
                _logger.Received().Log<object>(LogLevel.Error, 0, Arg.Any<object>(), Arg.Any<Exception>(), Arg.Any<Func<object, Exception, string>>()!);
            }
        }

        [Fact]
        public async Task ClientAppDetails_Should_ReturnAppConfig_When_AppIdIsValid()
        {
            // Arrange
            var appId = Guid.NewGuid();
            var expectedResponse = new GetBackOfficeAuthConfigStoreResponse
            {
                Id = appId,
                AppName = "TestApp",
                ProductEmail = "<EMAIL>"
            };

            _authenticationConfigStore.GetAsync<GetBackOfficeAuthConfigStoreResponse>(appId)
                .Returns(expectedResponse);

            // Act
            var result = await _accountLookupService.ClientAppDetails(appId);

            // Assert
            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Should().BeEquivalentTo(expectedResponse);
                await _authenticationConfigStore.Received(1).GetAsync<GetBackOfficeAuthConfigStoreResponse>(appId);
            }
        }

        [Fact]
        public async Task ClientAppDetails_Should_ReturnNull_When_AppIdIsInvalid()
        {
            // Arrange
            var appId = Guid.NewGuid();

            _authenticationConfigStore.GetAsync<GetBackOfficeAuthConfigStoreResponse>(appId)
                .Returns((GetBackOfficeAuthConfigStoreResponse)null!);

            // Act
            var result = await _accountLookupService.ClientAppDetails(appId);

            // Assert
            using (new AssertionScope())
            {
                result.Should().BeNull();
                await _authenticationConfigStore.Received(1).GetAsync<GetBackOfficeAuthConfigStoreResponse>(appId);
            }
        }
        [Fact]
        public async Task SaveAccountInfoToCache_Should_ReturnTrue_When_Successful()
        {
            // Arrange
            var applicationId = Guid.NewGuid();
            var accountInfo = new PhoneNumberLookupData
            {
                MobileNumber = "*********",
                Email = "<EMAIL>",
                TokenData = new Dictionary<string, string> { { "key", "value" } }
            };
            var cacheKey = $"{nameof(PhoneNumberLookupData)}:{applicationId}:{accountInfo.MobileNumber}";
            var expiry = 10;

            _redisDatabase.StringSetAsync(cacheKey, Arg.Any<RedisValue>(), TimeSpan.FromMinutes(expiry)).Returns(true);

            // Act
            var result = await _accountLookupService.SaveAccountInfoToCache(applicationId, accountInfo, expiry);

            // Assert
            using (new AssertionScope())
            {
                result.Should().BeTrue();
                await _redisDatabase.Received(1).StringSetAsync(cacheKey, Arg.Any<RedisValue>(), TimeSpan.FromMinutes(expiry));
            }
        }

        [Fact]
        public async Task SaveAccountInfoToCache_Should_ReturnFalse_When_Unsuccessful()
        {
            // Arrange
            var applicationId = Guid.NewGuid();
            var accountInfo = new PhoneNumberLookupData
            {
                MobileNumber = "*********",
                Email = "<EMAIL>",
                TokenData = new Dictionary<string, string> { { "key", "value" } }
            };
            var cacheKey = $"{nameof(PhoneNumberLookupData)}:{applicationId}:{accountInfo.MobileNumber}";
            var expiry = 10;

            _redisDatabase.StringSetAsync(cacheKey, Arg.Any<RedisValue>(), TimeSpan.FromMinutes(expiry)).Returns(false);

            // Act
            var result = await _accountLookupService.SaveAccountInfoToCache(applicationId, accountInfo, expiry);

            // Assert
            using (new AssertionScope())
            {
                result.Should().BeFalse();
                await _redisDatabase.Received(1).StringSetAsync(cacheKey, Arg.Any<RedisValue>(), TimeSpan.FromMinutes(expiry));
            }
        }
        [Fact]
        public async Task RetrieveCachedAccountInfo_Should_ReturnAccountInfo_When_CacheHit()
        {
            // Arrange
            var applicationId = Guid.NewGuid();
            var mobileNumber = "*********";
            var cacheKey = $"{nameof(PhoneNumberLookupData)}:{applicationId}:{mobileNumber}";
            var expectedAccountInfo = new PhoneNumberLookupData
            {
                MobileNumber = mobileNumber,
                Email = "<EMAIL>",
                TokenData = new Dictionary<string, string> { { "key", "value" } }
            };

            _redisDatabase.StringGetAsync(cacheKey).Returns((RedisValue)JsonSerializer.Serialize(expectedAccountInfo));

            // Act
            var result = await _accountLookupService.RetrieveCachedAccountInfo(applicationId, mobileNumber);

            // Assert
            using (new AssertionScope())
            {
                result.Should().NotBeNull();
                result.Should().BeEquivalentTo(expectedAccountInfo);
                await _redisDatabase.Received(1).GetKey<PhoneNumberLookupData>(cacheKey);
                _logger.Received(2).Log<object>(LogLevel.Information, 0, Arg.Any<object>(), Arg.Any<Exception>(), Arg.Any<Func<object, Exception, string>>()!);
            }
        }

        [Fact]
        public async Task RetrieveCachedAccountInfo_Should_ReturnNull_When_CacheMiss()
        {
            // Arrange
            var applicationId = Guid.NewGuid();
            var mobileNumber = "*********";
            var cacheKey = $"{nameof(PhoneNumberLookupData)}:{applicationId}:{mobileNumber}";

            _redisDatabase.StringGetAsync(cacheKey).Returns(RedisValue.Null);

            // Act
            var result = await _accountLookupService.RetrieveCachedAccountInfo(applicationId, mobileNumber);

            // Assert
            using (new AssertionScope())
            {
                result.Should().BeNull();
                await _redisDatabase.Received(1).GetKey<PhoneNumberLookupData>(cacheKey);
                _logger.Received(1).Log<object>(LogLevel.Warning, 0, Arg.Any<object>(), Arg.Any<Exception>(), Arg.Any<Func<object, Exception, string>>()!);
            }
        }

        [Fact]
        public async Task RetrieveCachedAccountInfo_Should_LogError_When_ExceptionOccurs()
        {
            // Arrange
            var applicationId = Guid.NewGuid();
            var mobileNumber = "*********";
            var cacheKey = $"{nameof(PhoneNumberLookupData)}:{applicationId}:{mobileNumber}";
            var exception = new Exception("Redis error");

            _redisDatabase.GetKey<PhoneNumberLookupData>(cacheKey).Throws(exception);

            // Act
            Func<Task> act = async () => await _accountLookupService.RetrieveCachedAccountInfo(applicationId, mobileNumber);

            // Assert
            using (new AssertionScope())
            {
                await act.Should().ThrowAsync<Exception>().WithMessage("Redis error");
                await _redisDatabase.Received(1).GetKey<PhoneNumberLookupData>(cacheKey);
                _logger.Received(1).Log<object>(LogLevel.Information, 0, Arg.Any<object>(), Arg.Any<Exception>(), Arg.Any<Func<object, Exception, string>>()!);
                _logger.Received(1).Log<object>(LogLevel.Error, 0, Arg.Any<object>(), Arg.Any<Exception>(), Arg.Any<Func<object, Exception, string>>()!);
            }
        }
        [Fact]
        public async Task SaveAccountInfoToCache_Should_LogError_When_ExceptionOccurs()
        {
            // Arrange
            var applicationId = Guid.NewGuid();
            var accountInfo = new PhoneNumberLookupData
            {
                MobileNumber = "*********",
                Email = "<EMAIL>",
                TokenData = new Dictionary<string, string> { { "key", "value" } }
            };
            var cacheKey = $"{nameof(PhoneNumberLookupData)}:{applicationId}:{accountInfo.MobileNumber}";
            var expiry = 10;
            var exception = new Exception("Redis error");

            _redisDatabase.StringSetAsync(cacheKey, Arg.Any<RedisValue>(), TimeSpan.FromMinutes(expiry)).Throws(exception);

            // Act
            Func<Task> act = async () => await _accountLookupService.SaveAccountInfoToCache(applicationId, accountInfo, expiry);

            // Assert
            using (new AssertionScope())
            {
                await act.Should().ThrowAsync<Exception>().WithMessage("Redis error");
                await _redisDatabase.Received(1).StringSetAsync(cacheKey, Arg.Any<RedisValue>(), TimeSpan.FromMinutes(expiry));
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            _httpTest.Dispose();
        }


    }
}