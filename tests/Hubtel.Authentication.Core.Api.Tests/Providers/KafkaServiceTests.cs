using Confluent.Kafka;
using FluentAssertions.Execution;
using Hubtel.Authentication.Core.Api.Models.AppModels;
using Hubtel.Authentication.Core.Api.Options;
using Hubtel.Authentication.Core.Api.Services.Provider;
using Hubtel.Kafka.Host.Core;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using NSubstitute;

namespace Hubtel.Authentication.Core.Api.Tests.Services.Provider
{
    public class KafkaServiceTests
    {
        private readonly IKafkaRawProducer _producer;
        private readonly IOptions<KafkaExtra> _config;
        private readonly ILogger<KafkaService> _logger;
        private readonly KafkaService _kafkaService;

        public KafkaServiceTests()
        {
            _producer = Substitute.For<IKafkaRawProducer>();
            _config = Substitute.For<IOptions<KafkaExtra>>();
            _logger = Substitute.For<ILogger<KafkaService>>();
            _config.Value.Returns(new KafkaExtra { TopicPrefix = "prefix_", NotificationTopic = "notification_topic" });
            _kafkaService = new KafkaService(_producer, _config, _logger);
        }

        [Fact]
        public async Task ProduceMessageAsync_Should_LogDebug_When_MessageProducedSuccessfully()
        {
            // Arrange
            var model = new { Name = "Test" };
            var topic = "test_topic";
            var json = JsonConvert.SerializeObject(model);

            // Act
            await _kafkaService.ProduceMessageAsync(model, topic);

            // Assert
            using (new AssertionScope())
            {
                await _producer.Received(1).Produce(topic, json, Arg.Any<Action<DeliveryReport<Null, string>>> ());
            }
        }

        [Fact]
        public async Task NotifyOptionsAsync_Should_ProduceMessageForEachOption_When_Called()
        {
            // Arrange
            var notification = new NotificationData { Destination = "<EMAIL>" };
            var options = new[] { "option1", "option2" };

            // Act
            await _kafkaService.NotifyOptionsAsync(notification, options);

            // Assert
            using (new AssertionScope())
            {
                await _producer.Received(2).Produce(_config.Value.NotificationTopic, Arg.Any<string>(), Arg.Any<Action<DeliveryReport<Null, string>>>());
            }
        }

        [Fact]
        public async Task NotifyOptionsAsync_Should_Not_LogError_When_ExceptionThrown()
        {
            // Arrange
            var notification = new NotificationData { Destination = "<EMAIL>" };
            var options = new[] { "option1", "option2" };
            var exception = new Exception("Test exception");
            _producer.When(x => x.Produce(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<Action<DeliveryReport<Null, string>>>()))
                     .Do(x => throw exception);

            // Act
            await _kafkaService.NotifyOptionsAsync(notification, options);

            // Assert
            using (new AssertionScope())
            {
                _logger.DidNotReceive().LogError(exception, "Error while sending OTP Notification error -> {Message}",exception.Message);
            }
        }

        [Fact]
        public async Task ProduceMessageAsync_Should_ProduceMessageWithCorrectTopic_When_CalledWithNotificationVM()
        {
            // Arrange
            var model = new NotificationVM { To = "<EMAIL>", Content = "Test content" };
            var type = NotificationTypes.email;
            var expectedTopic = $"{_config.Value.TopicPrefix}{type}";

            // Act
            await _kafkaService.ProduceMessageAsync(model, type);

            // Assert
            using (new AssertionScope())
            {
                await _producer.Received(1).Produce(expectedTopic, Arg.Any<string>(), Arg.Any<Action<DeliveryReport<Null, string>>>());
            }
        }
    }
}
