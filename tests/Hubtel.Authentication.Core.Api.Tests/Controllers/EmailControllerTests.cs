using FluentAssertions;
using Hubtel.Authentication.Core.Api.Controllers;
using Hubtel.Authentication.Core.Api.Data.Core;
using Hubtel.Authentication.Core.Api.Data.Entities;
using Hubtel.Authentication.Core.Api.Dtos;
using Hubtel.Authentication.Core.Api.Models;
using Hubtel.Authentication.Core.Api.Models.AppModels;
using Hubtel.Authentication.Core.Api.Models.Requests;
using Hubtel.Authentication.Core.Api.Models.Responses;
using Hubtel.Authentication.Core.Api.Services.Interface;
using Hubtel.Authentication.Core.Api.Services.Provider;
using Hubtel.PhoneNumbers.Extensions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using NSubstitute;
using System.Globalization;

namespace Hubtel.Authentication.Core.Api.Tests.Controllers
{
    public class EmailControllerTests
    {
        private readonly IEmailService _otpService;
        private readonly IPhoneNumberParser _phoneNumberParser;
        private readonly ILogger<EmailController> _logger;
        private readonly IAccountLookupService _accountLookupService;
        private readonly IJwtService _jwtService;
        private readonly EmailController _controller;
        private readonly HttpResponse _mockHttpResponse;

        public EmailControllerTests()
        {
            var mockHttpContext = Substitute.For<HttpContext>();
            _mockHttpResponse = Substitute.For<HttpResponse>();
            _otpService = Substitute.For<IEmailService>();
            _phoneNumberParser = Substitute.For<IPhoneNumberParser>();
            _logger = Substitute.For<ILogger<EmailController>>();
            _accountLookupService = Substitute.For<IAccountLookupService>();
            _jwtService = Substitute.For<IJwtService>();
            _controller = new EmailController(_otpService, _phoneNumberParser, _logger, _accountLookupService, _jwtService) {
                ControllerContext = new ControllerContext
                {
                    HttpContext = mockHttpContext
                }
            };
            mockHttpContext.Response.Returns(_mockHttpResponse);
        }

        [Fact]
        public async Task SendOtp_Should_Return400BadRequest_When_PhoneNumberIsInvalid()
        {
            // Arrange
            var model = new GenerateEmailOtpDto { PhoneNumber = "invalid", CountryCode = "GH" };
            _phoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, model.CountryCode, out Arg.Any<string>()).Returns(false);

            // Act
            var result = await _controller.SendOtp(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Should().BeOfType<ObjectResult>();
                var objectResult = result as ObjectResult;
                objectResult?.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
                var apiResponse = objectResult?.Value as ApiResponse<EmptyDto>;
                apiResponse?.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
                apiResponse?.Message.Should().Be(AppResponses.InvalidMobileNumber);
            }
        }

        [Fact]
        public async Task SendOtp_Should_Return400BadRequest_When_AccountDetailsNotFound()
        {
            // Arrange
            var model = new GenerateEmailOtpDto { PhoneNumber = "**********", CountryCode = "GH", AppId = Guid.NewGuid() };
            _phoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, model.CountryCode, out Arg.Any<string>()).Returns(true);
            _accountLookupService.RetrieveCachedAccountInfo(model.AppId, model.PhoneNumber).Returns((PhoneNumberLookupData)null!);

            // Act
            var result = await _controller.SendOtp(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Should().BeOfType<ObjectResult>();
                var objectResult = result as ObjectResult;
                objectResult?.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
                var apiResponse = objectResult?.Value as ApiResponse<EmptyDto>;
                apiResponse?.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
                apiResponse?.Message.Should().Be(AppResponses.OtpExpired);
            }
        }

        [Fact]
        public async Task SendOtp_Should_Return400BadRequest_When_AppConfigNotFound()
        {
            // Arrange
            var model = new GenerateEmailOtpDto { PhoneNumber = "**********", CountryCode = "GH", AppId = Guid.NewGuid() };
            _phoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, model.CountryCode, out Arg.Any<string>()).Returns(true);
            _accountLookupService.RetrieveCachedAccountInfo(model.AppId, model.PhoneNumber).Returns(new PhoneNumberLookupData());
            _accountLookupService.ClientAppDetails(model.AppId).Returns((GetBackOfficeAuthConfigStoreResponse)null!);

            // Act
            var result = await _controller.SendOtp(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Should().BeOfType<ObjectResult>();
                var objectResult = result as ObjectResult;
                objectResult?.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
                var apiResponse = objectResult?.Value as ApiResponse<EmptyDto>;
                apiResponse?.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
                apiResponse?.Message.Should().Be(AppResponses.AppMayNotBeConfigured);
            }
        }

        [Fact]
        public async Task SendOtp_Should_Return500InternalServerError_When_AccountLookupFails()
        {
            // Arrange
            var model = new GenerateEmailOtpDto { PhoneNumber = "**********", CountryCode = "GH", AppId = Guid.NewGuid() };
            var appConfig = new GetBackOfficeAuthConfigStoreResponse { AccountLookUpUrl = "http://example.com" };
            _phoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, model.CountryCode, out Arg.Any<string>()).Returns(true);
            _accountLookupService.RetrieveCachedAccountInfo(model.AppId, model.PhoneNumber).Returns(new PhoneNumberLookupData());
            _accountLookupService.ClientAppDetails(model.AppId).Returns(appConfig);
            _accountLookupService.LookUpCustomerInfoByPhoneNumber(appConfig.AccountLookUpUrl, model.PhoneNumber).Returns((PhoneNumberLookupResponse)null!);

            // Act
            var result = await _controller.SendOtp(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Should().BeOfType<ObjectResult>();
                var objectResult = result as ObjectResult;
                objectResult?.StatusCode.Should().Be(StatusCodes.Status500InternalServerError);
                var apiResponse = objectResult?.Value as ApiResponse<EmptyDto>;
                apiResponse?.Code.Should().Be($"{StatusCodes.Status500InternalServerError}");
                apiResponse?.Message.Should().Be(AppResponses.HttpRequestFailed);
            }
        }

        [Fact]
        public async Task SendOtp_Should_Return403Forbidden_When_AccountLookupReturns403()
        {
            // Arrange
            var model = new GenerateEmailOtpDto { PhoneNumber = "**********", CountryCode = "GH", AppId = Guid.NewGuid() };
            var appConfig = new GetBackOfficeAuthConfigStoreResponse { AccountLookUpUrl = "http://example.com" };
            var accountLookupResponse = new PhoneNumberLookupResponse { Code = $"{StatusCodes.Status403Forbidden}", Message = "Forbidden" };
            _phoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, model.CountryCode, out Arg.Any<string>()).Returns(true);
            _accountLookupService.RetrieveCachedAccountInfo(model.AppId, model.PhoneNumber).Returns(new PhoneNumberLookupData());
            _accountLookupService.ClientAppDetails(model.AppId).Returns(appConfig);
            _accountLookupService.LookUpCustomerInfoByPhoneNumber(appConfig.AccountLookUpUrl, model.PhoneNumber).Returns(accountLookupResponse);

            // Act
            var result = await _controller.SendOtp(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Should().BeOfType<ObjectResult>();
                var objectResult = result as ObjectResult;
                objectResult?.StatusCode.Should().Be(StatusCodes.Status403Forbidden);
                var apiResponse = objectResult?.Value as ApiResponse<PhoneNumberLookupData>;
                apiResponse?.Code.Should().Be($"{StatusCodes.Status403Forbidden}");
                apiResponse?.Message.Should().Be("Forbidden");
                apiResponse?.Data.Should().Be(accountLookupResponse.Data);
            }
        }

        [Fact]
        public async Task SendOtp_Should_Return404NotFound_When_AccountLookupReturns404()
        {
            // Arrange
            var model = new GenerateEmailOtpDto { PhoneNumber = "**********", CountryCode = "GH", AppId = Guid.NewGuid() };
            var appConfig = new GetBackOfficeAuthConfigStoreResponse { AccountLookUpUrl = "http://example.com" };
            var accountLookupResponse = new PhoneNumberLookupResponse { Code = $"{StatusCodes.Status404NotFound}", Message = "Not Found" };
            _phoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, model.CountryCode, out Arg.Any<string>()).Returns(true);
            _accountLookupService.RetrieveCachedAccountInfo(model.AppId, model.PhoneNumber).Returns(new PhoneNumberLookupData());
            _accountLookupService.ClientAppDetails(model.AppId).Returns(appConfig);
            _accountLookupService.LookUpCustomerInfoByPhoneNumber(appConfig.AccountLookUpUrl, model.PhoneNumber).Returns(accountLookupResponse);

            // Act
            var result = await _controller.SendOtp(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Should().BeOfType<NotFoundObjectResult>();
                var objectResult = result as NotFoundObjectResult;
                var apiResponse = objectResult?.Value as ApiResponse<PhoneNumberLookupData>;
                apiResponse?.Code.Should().Be($"{StatusCodes.Status404NotFound}");
                apiResponse?.Message.Should().Be("Not Found");
                apiResponse?.Data.Should().Be(accountLookupResponse.Data);
            }
        }

        [Fact]
        public async Task SendOtp_Should_Return500InternalServerError_When_AccountLookupReturns500()
        {
            // Arrange
            var model = new GenerateEmailOtpDto { PhoneNumber = "**********", CountryCode = "GH", AppId = Guid.NewGuid() };
            var appConfig = new GetBackOfficeAuthConfigStoreResponse { AccountLookUpUrl = "http://example.com" };
            var accountLookupResponse = new PhoneNumberLookupResponse { Code = $"{StatusCodes.Status500InternalServerError}", Message = "Internal Server Error" };
            _phoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, model.CountryCode, out Arg.Any<string>()).Returns(true);
            _accountLookupService.RetrieveCachedAccountInfo(model.AppId, model.PhoneNumber).Returns(new PhoneNumberLookupData());
            _accountLookupService.ClientAppDetails(model.AppId).Returns(appConfig);
            _accountLookupService.LookUpCustomerInfoByPhoneNumber(appConfig.AccountLookUpUrl, model.PhoneNumber).Returns(accountLookupResponse);

            // Act
            var result = await _controller.SendOtp(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Should().BeOfType<ObjectResult>();
                var objectResult = result as ObjectResult;
                objectResult?.StatusCode.Should().Be(StatusCodes.Status500InternalServerError);
                var apiResponse = objectResult?.Value as ApiResponse<PhoneNumberLookupData>;
                apiResponse?.Code.Should().Be($"{StatusCodes.Status500InternalServerError}");
                apiResponse?.Message.Should().Be("Internal Server Error");
                apiResponse?.Data.Should().Be(accountLookupResponse.Data);
            }
        }

        [Fact]
        public async Task SendOtp_Should_Return200Ok_When_Successful()
        {
            // Arrange
            var model = new GenerateEmailOtpDto { PhoneNumber = "**********", CountryCode = "GH", AppId = Guid.NewGuid() };
            var appConfig = new GetBackOfficeAuthConfigStoreResponse { AccountLookUpUrl = "http://example.com" };
            var accountLookupResponse = new PhoneNumberLookupResponse { Code = $"{StatusCodes.Status200OK}", Message = "Success", Data = new PhoneNumberLookupData { MobileNumber = "**********", Email = "<EMAIL>" } };
            var otpResponse = new ApiResponse<GenerateOtpResponse>("Success", new GenerateOtpResponse { Account = new { MobileNumber = "**********", Email = "<EMAIL>" } })
            {
                Code = $"{StatusCodes.Status200OK}",
            };
            _phoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, model.CountryCode, out Arg.Any<string>()).Returns(true);
            _accountLookupService.RetrieveCachedAccountInfo(model.AppId, model.PhoneNumber).Returns(new PhoneNumberLookupData { Email = "<EMAIL>" });
            _accountLookupService.ClientAppDetails(model.AppId).Returns(appConfig);
            _accountLookupService.LookUpCustomerInfoByPhoneNumber(appConfig.AccountLookUpUrl, model.PhoneNumber).Returns(accountLookupResponse);
            _otpService.SendOtpAsync(model, appConfig).Returns(otpResponse);

            // Act
            var result = await _controller.SendOtp(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Should().BeOfType<ObjectResult>();
                var objectResult = result as ObjectResult;
                objectResult?.StatusCode.Should().Be(StatusCodes.Status200OK);
                var apiResponse = objectResult?.Value as ApiResponse<GenerateOtpResponse>;
                apiResponse?.Code.Should().Be($"{StatusCodes.Status200OK}");
                apiResponse?.Message.Should().Be("Success");
                apiResponse?.Data.Should().NotBeNull();
            }
        }

        [Fact]
        public async Task VerifyOtp_Should_Return400BadRequest_When_PhoneNumberIsInvalid()
        {
            // Arrange
            var model = new VerifyEmailOtpDto { PhoneNumber = "invalid", AppId = Guid.NewGuid() };
            _phoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, "GH", out Arg.Any<string>()).Returns(false);

            // Act
            var result = await _controller.VerifyOtp(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Should().BeOfType<ObjectResult>();
                var objectResult = result as ObjectResult;
                objectResult?.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
                var apiResponse = objectResult?.Value as ApiResponse<EmptyDto>;
                apiResponse?.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
                apiResponse?.Message.Should().Be(AppResponses.InvalidMobileNumber);
            }
        }

        [Fact]
        public async Task VerifyOtp_Should_Return400BadRequest_When_AccountDetailsNotFound()
        {
            // Arrange
            var model = new VerifyEmailOtpDto { PhoneNumber = "**********", AppId = Guid.NewGuid() };
            _phoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, "GH", out Arg.Any<string>()).Returns(true);
            _accountLookupService.RetrieveCachedAccountInfo(model.AppId, model.PhoneNumber).Returns((PhoneNumberLookupData)null!);

            // Act
            var result = await _controller.VerifyOtp(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Should().BeOfType<ObjectResult>();
                var objectResult = result as ObjectResult;
                objectResult?.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
                var apiResponse = objectResult?.Value as ApiResponse<EmptyDto>;
                apiResponse?.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
                apiResponse?.Message.Should().Be(AppResponses.AppMayNotBeConfigured);
            }
        }

        [Fact]
        public async Task VerifyOtp_Should_Return200Ok_When_Successful()
        {
            // Arrange
            var model = new VerifyEmailOtpDto { PhoneNumber = "**********", AppId = Guid.NewGuid() };
            var verifyOtpResponse = new ApiResponse<VerifyOtpResponse>("Success", new VerifyOtpResponse { Token = "token", Expiry = DateTime.UtcNow.AddMinutes(5).ToString(CultureInfo.CurrentCulture) })
            {
                Code = $"{StatusCodes.Status200OK}"
            };
            _phoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, "GH", out Arg.Any<string>()).Returns(true);
            _accountLookupService.RetrieveCachedAccountInfo(model.AppId, model.PhoneNumber).Returns(new PhoneNumberLookupData());
            _otpService.VerifyOtpAsync(model).Returns(verifyOtpResponse);

            // Act
            var result = await _controller.VerifyOtp(model);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Should().BeOfType<ObjectResult>();
                var objectResult = result as ObjectResult;
                objectResult?.StatusCode.Should().Be(StatusCodes.Status200OK);
                var apiResponse = objectResult?.Value as ApiResponse<VerifyOtpResponse>;
                apiResponse?.Code.Should().Be($"{StatusCodes.Status200OK}");
                _mockHttpResponse.Cookies.Received(1).Append(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<CookieOptions>());
                apiResponse?.Message.Should().Be("Success");
                apiResponse?.Data.Token.Should().Be("token");
            }
        }

        [Fact]
        public async Task CustomerInfoLookUpByEmail_Should_Return400BadRequest_When_EmailIsInvalid()
        {
            // Arrange
            var appId = Guid.NewGuid();
            var email = "invalid-email";
            var authenticationType = ExternalAuthenticationType.Google;

            // Act
            var result = await _controller.CustomerInfoLookUpByEmail(appId, email, authenticationType);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Should().BeOfType<ObjectResult>();
                var objectResult = result as ObjectResult;
                objectResult?.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
                var apiResponse = objectResult?.Value as ApiResponse<EmptyDto>;
                apiResponse?.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
                apiResponse?.Message.Should().Be(AppResponses.EmailIsInvalid);
            }
        }

        [Fact]
        public async Task CustomerInfoLookUpByEmail_Should_Return400BadRequest_When_AppConfigNotFound()
        {
            // Arrange
            var appId = Guid.NewGuid();
            var email = "<EMAIL>";
            var authenticationType = ExternalAuthenticationType.Google;
            _accountLookupService.ClientAppDetails(appId).Returns((GetBackOfficeAuthConfigStoreResponse)null!);

            // Act
            var result = await _controller.CustomerInfoLookUpByEmail(appId, email, authenticationType);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Should().BeOfType<ObjectResult>();
                var objectResult = result as ObjectResult;
                objectResult?.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
                var apiResponse = objectResult?.Value as ApiResponse<EmptyDto>;
                apiResponse?.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
                apiResponse?.Message.Should().Be(AppResponses.AppMayNotBeConfigured);
            }
        }

        [Fact]
        public async Task CustomerInfoLookUpByEmail_Should_Return404NotFound_When_CustomerInformationNotFound()
        {
            // Arrange
            var appId = Guid.NewGuid();
            var email = "<EMAIL>";
            var authenticationType = ExternalAuthenticationType.Google;
            var appConfig = new GetBackOfficeAuthConfigStoreResponse { EmailLookUpUrl = "http://example.com" };
            _accountLookupService.ClientAppDetails(appId).Returns(appConfig);
            _accountLookupService.LookUpCustomerInfoByEmail(appConfig.EmailLookUpUrl, email).Returns((EmailLookUpResponse)null!);

            // Act
            var result = await _controller.CustomerInfoLookUpByEmail(appId, email, authenticationType);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Should().BeOfType<ObjectResult>();
                var objectResult = result as ObjectResult;
                objectResult?.StatusCode.Should().Be(StatusCodes.Status404NotFound);
                var apiResponse = objectResult?.Value as ApiResponse<EmptyDto>;
                apiResponse?.Code.Should().Be($"{StatusCodes.Status404NotFound}");
                apiResponse?.Message.Should().Be(AppResponses.EmailMayNotExist);
            }
        }

        [Fact]
        public async Task CustomerInfoLookUpByEmail_Should_Return403Forbidden_When_CustomerInformationReturns403()
        {
            // Arrange
            var appId = Guid.NewGuid();
            var email = "<EMAIL>";
            var authenticationType = ExternalAuthenticationType.Google;
            var appConfig = new GetBackOfficeAuthConfigStoreResponse { EmailLookUpUrl = "http://example.com" };
            var customerInformation = new EmailLookUpResponse { Code = $"{StatusCodes.Status403Forbidden}", Message = "Forbidden" };
            _accountLookupService.ClientAppDetails(appId).Returns(appConfig);
            _accountLookupService.LookUpCustomerInfoByEmail(appConfig.EmailLookUpUrl, email).Returns(customerInformation);

            // Act
            var result = await _controller.CustomerInfoLookUpByEmail(appId, email, authenticationType);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Should().BeOfType<ObjectResult>();
                var objectResult = result as ObjectResult;
                objectResult?.StatusCode.Should().Be(StatusCodes.Status403Forbidden);
                var apiResponse = objectResult?.Value as ApiResponse<EmailLookUpData>;
                apiResponse?.Code.Should().Be($"{StatusCodes.Status403Forbidden}");
                apiResponse?.Message.Should().Be("Forbidden");
                apiResponse?.Data.Should().Be(customerInformation.Data);
            }
        }

        [Fact]
        public async Task CustomerInfoLookUpByEmail_Should_Return200Ok_When_PhoneLookupIsDisabled()
        {
            // Arrange
            var appId = Guid.NewGuid();
            var email = "<EMAIL>";
            var authenticationType = ExternalAuthenticationType.Google;
            var appConfig = new GetBackOfficeAuthConfigStoreResponse
            {
                EmailLookUpUrl = "http://example.com",
                ExternalChannels = new List<ExternalLoginConfiguration>
                {
                    new ExternalLoginConfiguration { Type = authenticationType, DisablePhoneLookup = true }
                }
            };
            var customerInformation = new EmailLookUpResponse
            {
                Code = $"{StatusCodes.Status200OK}",
                Message = "Success",
                Data = new EmailLookUpData { TokenData = new Dictionary<string, string>() }
            };
            var authToken = new AppResult { IsSuccessful = true, Status = AppStatus.Success, Data = new { Token = "token" }, Message = "Token generated" };
            _accountLookupService.ClientAppDetails(appId).Returns(appConfig);
            _accountLookupService.LookUpCustomerInfoByEmail(appConfig.EmailLookUpUrl, email).Returns(customerInformation);
            _jwtService.GenerateAppToken(Arg.Any<TokenGenerationModel>()).Returns(authToken);

            // Act
            var result = await _controller.CustomerInfoLookUpByEmail(appId, email, authenticationType);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Should().BeOfType<OkObjectResult>();
                var objectResult = result as OkObjectResult;
                var apiResponse = objectResult?.Value as ApiResponse<object>;
                apiResponse?.Code.Should().Be($"{StatusCodes.Status200OK}");
                apiResponse?.Message.Should().Be("Token generated");
                apiResponse?.Data.Should().BeEquivalentTo(new { Token = "token" });
            }
        }

        [Fact]
        public async Task CustomerInfoLookUpByEmail_Should_Return500InternalServerError_When_TokenGenerationFails()
        {
            // Arrange
            var appId = Guid.NewGuid();
            var email = "<EMAIL>";
            var authenticationType = ExternalAuthenticationType.Google;
            var appConfig = new GetBackOfficeAuthConfigStoreResponse
            {
                EmailLookUpUrl = "http://example.com",
                ExternalChannels = new List<ExternalLoginConfiguration>
                {
                    new ExternalLoginConfiguration { Type = authenticationType, DisablePhoneLookup = true }
                }
            };
            var customerInformation = new EmailLookUpResponse
            {
                Code = $"{StatusCodes.Status200OK}",
                Message = "Success",
                Data = new EmailLookUpData { TokenData = new Dictionary<string, string>() }
            };
            var authToken = new AppResult { IsSuccessful = false, Status = AppStatus.Error, Message = "Token generation failed" };
            _accountLookupService.ClientAppDetails(appId).Returns(appConfig);
            _accountLookupService.LookUpCustomerInfoByEmail(appConfig.EmailLookUpUrl, email).Returns(customerInformation);
            _jwtService.GenerateAppToken(Arg.Any<TokenGenerationModel>()).Returns(authToken);

            // Act
            var result = await _controller.CustomerInfoLookUpByEmail(appId, email, authenticationType);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Should().BeOfType<ObjectResult>();
                var objectResult = result as ObjectResult;
                objectResult?.StatusCode.Should().Be(StatusCodes.Status500InternalServerError);
                var apiResponse = objectResult?.Value as ApiResponse<EmailLookUpData>;
                apiResponse?.Code.Should().Be($"{StatusCodes.Status500InternalServerError}");
                apiResponse?.Message.Should().Be("Token generation failed");
                apiResponse?.Data.Should().BeNull();
            }
        }

        [Fact]
        public async Task CustomerInfoLookUpByEmail_Should_Return200Ok_When_Successful()
        {
            // Arrange
            var appId = Guid.NewGuid();
            var email = "<EMAIL>";
            var authenticationType = ExternalAuthenticationType.Google;
            var appConfig = new GetBackOfficeAuthConfigStoreResponse { EmailLookUpUrl = "http://example.com" };
            var customerInformation = new EmailLookUpResponse
            {
                Code = $"{StatusCodes.Status200OK}",
                Message = "Success",
                Data = new EmailLookUpData { PhoneNumbers= [new PhoneNumber() { Number="************",CountryCode="233"}] }
            };
            _accountLookupService.ClientAppDetails(appId).Returns(appConfig);
            _accountLookupService.LookUpCustomerInfoByEmail(appConfig.EmailLookUpUrl, email).Returns(customerInformation);

            // Act
            var result = await _controller.CustomerInfoLookUpByEmail(appId, email, authenticationType);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Should().BeOfType<ObjectResult>();
                var objectResult = result as ObjectResult;
                objectResult?.StatusCode.Should().Be(StatusCodes.Status200OK);
                var apiResponse = objectResult?.Value as ApiResponse<EmailLookUpData>;
                apiResponse?.Code.Should().Be($"{StatusCodes.Status200OK}");
                apiResponse?.Message.Should().Be("Success");
                apiResponse?.Data.Should().Be(customerInformation.Data);
            }
        }
    }
}
