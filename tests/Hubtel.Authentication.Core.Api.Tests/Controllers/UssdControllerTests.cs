using Autofac.Core;
using FluentAssertions;
using FluentAssertions.Execution;
using Flurl.Http.Testing;
using Hubtel.Authentication.Core.Api.Controllers;
using Hubtel.Authentication.Core.Api.Dtos;
using Hubtel.Authentication.Core.Api.Models;
using Hubtel.Authentication.Core.Api.Models.AppModels;
using Hubtel.Authentication.Core.Api.Models.Requests;
using Hubtel.Authentication.Core.Api.Models.Responses;
using Hubtel.Authentication.Core.Api.Options;
using Hubtel.Authentication.Core.Api.Services.Interface;
using Hubtel.Authentication.Core.Api.Services.Provider;
using Hubtel.PhoneNumbers.Extensions;
using Hubtel.Redis.Sdk.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute;
using StackExchange.Redis;

namespace Hubtel.Authentication.Core.Api.Tests.Controllers;

public class UssdControllerTests
{
    private  IPhoneNumberParser _phoneNumberParser;
    private  IAccountLookupService _accountLookupService;
    private  UssdController _ussdController;
    private  IDatabase _otpCacheRepository;
    private  IDatabase _authConfigRedisStore;
    private  IJwtService _jwtService;
    private readonly HttpTest _httpTest;
    private readonly IOptions<SocketOptions> _socketOption = Microsoft.Extensions.Options.Options.Create(
        new SocketOptions()
        {
            Failure = "http://test.com",
            Success = "http://test.com"
        });

    public UssdControllerTests()
    {
        _accountLookupService = Substitute.For<IAccountLookupService>();
        var multiRedisHostCacheRepository = Substitute.For<IMultiRedisHostCacheRepository>();
        var otpService = Substitute.For<IUssdService>();
        _phoneNumberParser = Substitute.For<IPhoneNumberParser>();
        ILogger<UssdController> logger = Substitute.For<ILogger<UssdController>>();
        _authConfigRedisStore = Substitute.For<IDatabase>();
        _otpCacheRepository = _authConfigRedisStore;
        multiRedisHostCacheRepository.GetDb(Arg.Any<string>(), Arg.Any<string>()).Returns(_authConfigRedisStore);
        _jwtService = Substitute.For<IJwtService>();
        _httpTest = new HttpTest();

        _ussdController = new UssdController(otpService, _phoneNumberParser, logger, _jwtService,
            _accountLookupService, multiRedisHostCacheRepository, _socketOption);
    }

    [Fact]
    public async Task Generate_Should_ReturnBadRequest_When_PhoneNumberIsInvalid()
    {
        // Arrange
        var generateUssdOtpDto = new GenerateUssdOtpDto { PhoneNumber = "invalid_phone_number" };

        _phoneNumberParser.IsPhoneNumberValid(Arg.Any<string>(),
            Arg.Any<string>(), out Arg.Any<string>()).Returns(x =>
        {
            x[2] = null;
            return false;
        });

        // Act
        var result = await _ussdController.Generate(generateUssdOtpDto);

        // Assert
        using (new AssertionScope())
        {
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult?.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
            badRequestResult?.Value.Should().BeOfType<string>().Which.Should().Be("Invalid phone number format");
        }
    }


    [Fact]
    public async Task Generate_Should_ReturnBadRequest_When_ClientAppDetailsIsNull()
    {
        // Arrange
        var generateUssdOtpDto = new GenerateUssdOtpDto { PhoneNumber = "valid_phone_number", AppId = Guid.NewGuid() };

        _phoneNumberParser.IsPhoneNumberValid(Arg.Any<string>(),
            Arg.Any<string>(), out Arg.Any<string>()).Returns(x =>
        {
            x[2] = "correct_mobile_number";
            return true;
        });

        _accountLookupService.ClientAppDetails(Arg.Any<Guid>())
            .Returns(Task.FromResult<GetBackOfficeAuthConfigStoreResponse>(null));

        // Act
        var result = await _ussdController.Generate(generateUssdOtpDto);

        // Assert
        using (new AssertionScope())
        {
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult?.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
            badRequestResult?.Value.Should().BeOfType<string>().Which.Should().Be(AppResponses.InvalidMobileNumber);
        }
    }

    [Fact]
    public async Task Generate_Should_ReturnBadRequest_When_CustomerInfoIsNull()
    {
        // Arrange
        var generateUssdOtpDto = new GenerateUssdOtpDto { PhoneNumber = "valid_phone_number", AppId = Guid.NewGuid() };
        var clientAppDetails = new GetBackOfficeAuthConfigStoreResponse()
        {
            AccountLookUpUrl = "valid url",
        };

        _phoneNumberParser.IsPhoneNumberValid(Arg.Any<string>(),
            Arg.Any<string>(), out Arg.Any<string>()).Returns(x =>
        {
            x[2] = "correct_mobile_number";
            return true;
        });

        _accountLookupService.ClientAppDetails(Arg.Any<Guid>())
            .Returns(Task.FromResult<GetBackOfficeAuthConfigStoreResponse>(clientAppDetails));
        _accountLookupService.LookUpCustomerInfoByPhoneNumber(Arg.Any<string>(), Arg.Any<string>())
            .Returns(Task.FromResult<PhoneNumberLookupResponse>(null));

        // Act
        var result = await _ussdController.Generate(generateUssdOtpDto);

        // Assert
        using (new AssertionScope())
        {
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult?.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
            badRequestResult?.Value.Should().BeOfType<string>().Which.Should().Be(AppResponses.PhoneNumberMayNotExist);
        }
    }

    [Fact]
    public async Task Generate_Should_ReturnInternalServerError_When_ExceptionIsThrown()
    {
        // Arrange
        var generateUssdOtpDto = new GenerateUssdOtpDto { PhoneNumber = "valid_phone_number", AppId = Guid.NewGuid() };

        _phoneNumberParser.WhenForAnyArgs(x => x.IsPhoneNumberValid(null, null, out _))
            .Do(x => throw new DependencyResolutionException("error"));

        // Act
        var result = await _ussdController.Generate(generateUssdOtpDto);

        // Assert
        using (new AssertionScope())
        {
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult?.StatusCode.Should().Be(StatusCodes.Status500InternalServerError);
        }
    }
    [Fact]
    public async Task OnSuccess_ReturnsBadRequest_When_InvalidUssdRequest()
    {
        // Arrange
        var model = new UssdNotifyClientStatusDto { PhoneNumber = "**********", RequestId = "1" };
        _otpCacheRepository.StringGetAsync(Arg.Any<RedisKey>()).Returns(new RedisValue());

        // Act
        var result = (ObjectResult)await _ussdController.OnSuccess(model);

        // Assert
        using (new AssertionScope())
        {
            _httpTest.ShouldHaveCalled($"{_socketOption.Value.Failure}/*");
            result.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
        }
    }
    [Fact]
    public async Task OnSuccess_Should_ReturnBadRequest_When_OtpCacheRetrievalFails()
    {
        // Arrange
        var model = new UssdNotifyClientStatusDto { PhoneNumber = "**********", RequestId = "1" };
        _otpCacheRepository.StringGetAsync(Arg.Any<RedisKey>()).Returns(new RedisValue());

        // Act
        var result = (ObjectResult)await _ussdController.OnSuccess(model);

        // Assert
        using (new AssertionScope())
        {
            _httpTest.ShouldHaveCalled($"{_socketOption.Value.Failure}/*");
            (result).StatusCode.Should().Be(StatusCodes.Status400BadRequest);
        }
    }


    [Fact]
    public async Task OnSuccess_Should_ReturnBadRequest_When_AccountDetailsRetrievalFails()
    {
        // Arrange
        var model = new UssdNotifyClientStatusDto { PhoneNumber = "**********", RequestId = "1" };
        _otpCacheRepository.StringGetAsync(Arg.Any<RedisKey>()).Returns("123456");
        _accountLookupService.RetrieveCachedAccountInfo(Arg.Any<Guid>(), Arg.Any<string>()).Returns(Task.FromResult<PhoneNumberLookupData?>(null));

        // Act
        var result = await _ussdController.OnSuccess(model);

        // Assert
        using (new AssertionScope())
        {
            _httpTest.ShouldHaveCalled($"{_socketOption.Value.Failure}/*");
            result.Should().BeOfType<ObjectResult>();
            ((ObjectResult)result).StatusCode.Should().Be(StatusCodes.Status400BadRequest);
        }
    }

    [Fact]
    public async Task OnSuccess_Should_ReturnBadRequest_When_ClientAppDetailsRetrievalFails()
    {
        // Arrange
        var model = new UssdNotifyClientStatusDto { PhoneNumber = "**********", RequestId = "1" };
        _otpCacheRepository.StringGetAsync(Arg.Any<RedisKey>()).Returns("123456");
        _accountLookupService.ClientAppDetails(Arg.Any<Guid>()).Returns(new GetBackOfficeAuthConfigStoreResponse());
        _authConfigRedisStore.StringGetAsync(Arg.Any<RedisKey>()).Returns(new RedisValue());

        // Act
        var result = await _ussdController.OnSuccess(model);

        // Assert
        using (new AssertionScope())
        {
            _httpTest.ShouldHaveCalled($"{_socketOption.Value.Failure}/*");
            result.Should().BeOfType<ObjectResult>();
            ((ObjectResult)result).StatusCode.Should().Be(StatusCodes.Status400BadRequest);
        }
    }

    [Fact]
    public async Task OnSuccess_Should_ReturnBadRequest_When_JwtTokenGenerationFails()
    {
        // Arrange
        var model = new UssdNotifyClientStatusDto { PhoneNumber = "**********", RequestId = "1" };
        _otpCacheRepository.StringGetAsync(Arg.Any<RedisKey>()).Returns("123456");
        _accountLookupService.RetrieveCachedAccountInfo(Arg.Any<Guid>(), Arg.Any<string>()).Returns(new PhoneNumberLookupData());
        _authConfigRedisStore.StringGetAsync(Arg.Any<RedisKey>()).Returns("clientAppDetails");
        var tokenModel = new TokenGenerationModel
        {
            AccountLookupData = new PhoneNumberLookupData(),
            AuthenticationConfiguration = new GetBackOfficeAuthConfigStoreResponse()
        };
        _jwtService.GenerateAppToken(tokenModel).Returns(new AppResult() { IsSuccessful = false });

        // Act
        var result = await _ussdController.OnSuccess(model);

        // Assert
        using (new AssertionScope())
        {
            _httpTest.ShouldHaveCalled($"{_socketOption.Value.Failure}/*");
            result.Should().BeOfType<ObjectResult>();
            ((ObjectResult)result).StatusCode.Should().Be(StatusCodes.Status400BadRequest);
        }
    }

    [Fact]
    public async Task OnSuccess_Should_ReturnOk_When_AllServicesReturnSuccessfully()
    {
        // Arrange
        var jwtPayload = new AppResult() { IsSuccessful = true, Message = "SomeMessage" };
       
        var model = new UssdNotifyClientStatusDto { PhoneNumber = "**********", RequestId = "1" };
        _accountLookupService.RetrieveCachedAccountInfo(Arg.Any<Guid>(), Arg.Any<string>()).Returns(new PhoneNumberLookupData());
        _authConfigRedisStore.StringGetAsync(Arg.Any<RedisKey>()).Returns(model.PhoneNumber);
        _accountLookupService.ClientAppDetails(Arg.Any<Guid>()).Returns(new GetBackOfficeAuthConfigStoreResponse { AccountLookUpUrl="SomeUrl"});
        _jwtService.GenerateAppToken(Arg.Any<TokenGenerationModel>()).Returns(jwtPayload);

        // Act
        var result = await _ussdController.OnSuccess(model);

        // Assert
        using (new AssertionScope())
        {
            _httpTest.ShouldHaveCalled($"{_socketOption.Value.Success}/*");
            result.Should().BeOfType<OkResult>();
            ((OkResult)result).StatusCode.Should().Be(StatusCodes.Status200OK);
        }


    }

}