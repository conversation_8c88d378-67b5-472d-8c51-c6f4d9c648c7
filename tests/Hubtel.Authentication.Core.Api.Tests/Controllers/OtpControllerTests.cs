using FluentAssertions;
using FluentAssertions.Execution;
using Hubtel.Authentication.Core.Api.Controllers;
using Hubtel.Authentication.Core.Api.Dtos;
using Hubtel.Authentication.Core.Api.Models;
using Hubtel.Authentication.Core.Api.Models.AppModels;
using Hubtel.Authentication.Core.Api.Models.Requests;
using Hubtel.Authentication.Core.Api.Models.Responses;
using Hubtel.Authentication.Core.Api.Services.Interface;
using Hubtel.PhoneNumbers.Extensions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using NSubstitute;

namespace Hubtel.Authentication.Core.Api.Tests.Controllers
{
    public class OtpControllerTests
    {
        private readonly IOtpService _mockOtpService;
        private readonly IPhoneNumberParser _mockPhoneNumberParser;
        private readonly ILogger<OtpController> _mockLogger;
        private readonly IAccountLookupService _mockAccountLookupService;
        private readonly OtpController _controller;
        private readonly HttpResponse _mockHttpResponse;

        public OtpControllerTests()
        {
            var mockHttpContext = Substitute.For<HttpContext>();
            _mockHttpResponse = Substitute.For<HttpResponse>();
            _mockOtpService = Substitute.For<IOtpService>();
            _mockPhoneNumberParser = Substitute.For<IPhoneNumberParser>();
            _mockLogger = Substitute.For<ILogger<OtpController>>();
            _mockAccountLookupService = Substitute.For<IAccountLookupService>();
            _controller = new OtpController(_mockOtpService, _mockPhoneNumberParser, _mockLogger, _mockAccountLookupService)
            {
                ControllerContext = new ControllerContext
                {
                    HttpContext = mockHttpContext
                }
            };
            mockHttpContext.Response.Returns(_mockHttpResponse);
        }

        [Fact]
        public async Task SendOtp_Should_ReturnBadRequest_When_PhoneNumberIsInvalid()
        {
            // Arrange
            var model = new GenerateOtpDto { PhoneNumber = "invalid", CountryCode = "GH", AppId = Guid.NewGuid() };
            _mockPhoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, model.CountryCode, out Arg.Any<string>()).Returns(false);

            //Act
            var result = await _controller.SendOtp(model);

            // Assert
            using (new AssertionScope())
            {
                var badRequestResult = result.Should().BeOfType<ObjectResult>().Which;
                badRequestResult.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
                var apiResponse = badRequestResult.Value.Should().BeOfType<ApiResponse<EmptyDto>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
                apiResponse.Message.Should().Be(AppResponses.InvalidMobileNumber);
            }
        }

        [Fact]
        public async Task SendOtp_Should_ReturnInternalServerError_When_ClientAppDetailsIsNull()
        {
            // Arrange
            var model = new GenerateOtpDto { PhoneNumber = "**********", CountryCode = "GH", AppId = Guid.NewGuid() };
            _mockPhoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, model.CountryCode, out Arg.Any<string>()).Returns(true);
            _mockAccountLookupService.ClientAppDetails(model.AppId).Returns(Task.FromResult<GetBackOfficeAuthConfigStoreResponse>(null!));

            // Act
            var result = await _controller.SendOtp(model);

            // Assert
            using (new AssertionScope())
            {
                var internalServerErrorResult = result.Should().BeOfType<BadRequestObjectResult>().Which;
                internalServerErrorResult.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
                var apiResponse = internalServerErrorResult.Value.Should().BeOfType<ApiResponse<EmptyDto>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
                apiResponse.Message.Should().Be(AppResponses.HttpRequestFailed);
            }
        }

        [Fact]
        public async Task SendOtp_Should_ReturnBadRequest_When_AccountLookUpUrlIsEmpty()
        {
            // Arrange
            var model = new GenerateOtpDto { PhoneNumber = "**********", CountryCode = "GH", AppId = Guid.NewGuid() };
            var accountDetails = new GetBackOfficeAuthConfigStoreResponse { AccountLookUpUrl = "" };
            _mockPhoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, model.CountryCode, out Arg.Any<string>()).Returns(true);
            _mockAccountLookupService.ClientAppDetails(model.AppId).Returns(Task.FromResult(accountDetails));

            // Act
            var result = await _controller.SendOtp(model);

            // Assert
            using (new AssertionScope())
            {
                var badRequestResult = result.Should().BeOfType<ObjectResult>().Which;
                badRequestResult.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
                var apiResponse = badRequestResult.Value.Should().BeOfType<ApiResponse<EmptyDto>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
                apiResponse.Message.Should().Be(AppResponses.AppMayNotBeConfigured);
            }
        }

        [Fact]
        public async Task SendOtp_Should_ReturnBadRequest_When_AccountLookupResponseIsNull()
        {
            // Arrange
            var model = new GenerateOtpDto { PhoneNumber = "**********", CountryCode = "GH", AppId = Guid.NewGuid() };
            var accountDetails = new GetBackOfficeAuthConfigStoreResponse { AccountLookUpUrl = "http://example.com" };
            _mockPhoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, model.CountryCode, out Arg.Any<string>()).Returns(true);
            _mockAccountLookupService.ClientAppDetails(model.AppId).Returns(Task.FromResult(accountDetails));
            _mockAccountLookupService.LookUpCustomerInfoByPhoneNumber(accountDetails.AccountLookUpUrl, model.PhoneNumber, model.LinkedEmail).Returns(Task.FromResult<PhoneNumberLookupResponse>(null!));

            // Act
            var result = await _controller.SendOtp(model);

            // Assert
            using (new AssertionScope())
            {
                var badRequestResult = result.Should().BeOfType<BadRequestObjectResult>().Which;
                var apiResponse = badRequestResult.Value.Should().BeOfType<ApiResponse<EmptyDto>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
                apiResponse.Message.Should().Be(AppResponses.HttpRequestFailed);
            }
        }

        [Fact]
        public async Task SendOtp_Should_ReturnForbidden_When_AccountLookupResponseCodeIs403()
        {
            // Arrange
            var model = new GenerateOtpDto { PhoneNumber = "**********", CountryCode = "GH", AppId = Guid.NewGuid() };
            var accountDetails = new GetBackOfficeAuthConfigStoreResponse { AccountLookUpUrl = "http://example.com" };
            var accountLookupResponse = new PhoneNumberLookupResponse { Code = $"{StatusCodes.Status403Forbidden}", Message = "Forbidden", Data = new PhoneNumberLookupData() };
            _mockPhoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, model.CountryCode, out Arg.Any<string>()).Returns(true);
            _mockAccountLookupService.ClientAppDetails(model.AppId).Returns(Task.FromResult(accountDetails));
            _mockAccountLookupService.LookUpCustomerInfoByPhoneNumber(accountDetails.AccountLookUpUrl, model.PhoneNumber, model.LinkedEmail).Returns(Task.FromResult(accountLookupResponse));

            // Act
            var result = await _controller.SendOtp(model);

            // Assert
            using (new AssertionScope())
            {
                var forbiddenResult = result.Should().BeOfType<ObjectResult>().Which;
                forbiddenResult.StatusCode.Should().Be(StatusCodes.Status403Forbidden);
                var apiResponse = forbiddenResult.Value.Should().BeOfType<ApiResponse<PhoneNumberLookupData>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status403Forbidden}");
                apiResponse.Message.Should().Be("Forbidden");
                apiResponse.Data.Should().BeEquivalentTo(accountLookupResponse.Data);
            }
        }

        [Fact]
        public async Task SendOtp_Should_ReturnNotFound_When_AccountLookupResponseCodeIs404()
        {
            // Arrange
            var model = new GenerateOtpDto { PhoneNumber = "**********", CountryCode = "GH", AppId = Guid.NewGuid() };
            var accountDetails = new GetBackOfficeAuthConfigStoreResponse { AccountLookUpUrl = "http://example.com" };
            var accountLookupResponse = new PhoneNumberLookupResponse { Code = $"{StatusCodes.Status404NotFound}", Message = "Not Found", Data = new PhoneNumberLookupData() };
            _mockPhoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, model.CountryCode, out Arg.Any<string>()).Returns(true);
            _mockAccountLookupService.ClientAppDetails(model.AppId).Returns(Task.FromResult(accountDetails));
            _mockAccountLookupService.LookUpCustomerInfoByPhoneNumber(accountDetails.AccountLookUpUrl, model.PhoneNumber, model.LinkedEmail).Returns(Task.FromResult(accountLookupResponse));

            // Act
            var result = await _controller.SendOtp(model);

            // Assert
            using (new AssertionScope())
            {
                var notFoundResult = result.Should().BeOfType<NotFoundObjectResult>().Which;
                var apiResponse = notFoundResult.Value.Should().BeOfType<ApiResponse<PhoneNumberLookupData>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status404NotFound}");
                apiResponse.Message.Should().Be("Not Found");
                apiResponse.Data.Should().BeEquivalentTo(accountLookupResponse.Data);
            }
        }

        [Fact]
        public async Task SendOtp_Should_ReturnInternalServerError_When_AccountLookupResponseCodeIs500()
        {
            // Arrange
            var model = new GenerateOtpDto { PhoneNumber = "**********", CountryCode = "GH", AppId = Guid.NewGuid() };
            var accountDetails = new GetBackOfficeAuthConfigStoreResponse { AccountLookUpUrl = "http://example.com" };
            var accountLookupResponse = new PhoneNumberLookupResponse { Code = $"{StatusCodes.Status500InternalServerError}", Message = "Internal Server Error", Data = new PhoneNumberLookupData() };
            _mockPhoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, model.CountryCode, out Arg.Any<string>()).Returns(true);
            _mockAccountLookupService.ClientAppDetails(model.AppId).Returns(Task.FromResult(accountDetails));
            _mockAccountLookupService.LookUpCustomerInfoByPhoneNumber(accountDetails.AccountLookUpUrl, model.PhoneNumber, model.LinkedEmail).Returns(Task.FromResult(accountLookupResponse));

            // Act
            var result = await _controller.SendOtp(model);

            // Assert
            using (new AssertionScope())
            {
                var internalServerErrorResult = result.Should().BeOfType<ObjectResult>().Which;
                internalServerErrorResult.StatusCode.Should().Be(StatusCodes.Status500InternalServerError);
                var apiResponse = internalServerErrorResult.Value.Should().BeOfType<ApiResponse<PhoneNumberLookupData>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status500InternalServerError}");
                apiResponse.Message.Should().Be("Internal Server Error");
                apiResponse.Data.Should().BeEquivalentTo(accountLookupResponse.Data);
            }
        }

        [Fact]
        public async Task SendOtp_Should_ReturnOk_When_OtpIsSentSuccessfully()
        {
            // Arrange
            var model = new GenerateOtpDto { PhoneNumber = "**********", CountryCode = "GH", AppId = Guid.NewGuid() };
            var accountDetails = new GetBackOfficeAuthConfigStoreResponse { AccountLookUpUrl = "http://example.com" };
            var accountLookupResponse = new PhoneNumberLookupResponse { Code = $"{StatusCodes.Status200OK}", Message = "Success", Data = new PhoneNumberLookupData { MobileNumber = "**********", Email = "<EMAIL>" } };
            var otpResponse = new ApiResponse<GenerateOtpResponse> { Code = $"{StatusCodes.Status200OK}", Message = "OTP Sent", Data = new GenerateOtpResponse() };
            _mockPhoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, model.CountryCode, out Arg.Any<string>()).Returns(true);
            _mockAccountLookupService.ClientAppDetails(model.AppId).Returns(Task.FromResult(accountDetails));
            _mockAccountLookupService.LookUpCustomerInfoByPhoneNumber(accountDetails.AccountLookUpUrl, model.PhoneNumber, model.LinkedEmail).Returns(Task.FromResult(accountLookupResponse));
            _mockOtpService.SendOtpAsync(Arg.Any<CreateOtpRequest>()).Returns(Task.FromResult(otpResponse));

            // Act
            var result = await _controller.SendOtp(model);

            // Assert
            using (new AssertionScope())
            {
                var okResult = result.Should().BeOfType<OkObjectResult>().Which;
                var apiResponse = okResult.Value.Should().BeOfType<ApiResponse<GenerateOtpResponse>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status200OK}");
                apiResponse.Message.Should().Be("OTP Sent");
                apiResponse.Data.Should().NotBeNull();
            }
        }
        [Fact]
        public async Task VerifyOtp_Should_ReturnBadRequest_When_PhoneNumberIsInvalid()
        {
            // Arrange
            var model = new VerifyOtpDto { PhoneNumber = "invalid", RequestId = "request-id", Prefix = "prefix", OtpCode = "otp-code", AppId = Guid.NewGuid() };
            _mockPhoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, "GH", out Arg.Any<string>()).Returns(false);

            // Act
            var result = await _controller.VerifyOtp(model);

            // Assert
            using (new AssertionScope())
            {
                var badRequestResult = result.Should().BeOfType<ObjectResult>().Which;
                badRequestResult.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
                var apiResponse = badRequestResult.Value.Should().BeOfType<ApiResponse<EmptyDto>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
                apiResponse.Message.Should().Be(AppResponses.InvalidMobileNumber);
            }
        }

        [Fact]
        public async Task VerifyOtp_Should_ReturnOk_When_OtpIsVerifiedSuccessfully()
        {
            // Arrange
            var model = new VerifyOtpDto { PhoneNumber = "**********", RequestId = "request-id", Prefix = "prefix", OtpCode = "otp-code", AppId = Guid.NewGuid() };
            var verifyOtpResponse = new ApiResponse<VerifyOtpResponse> { Code = $"{StatusCodes.Status200OK}", Message = "OTP Verified", Data = new VerifyOtpResponse { Token = "token", Expiry = DateTime.UtcNow.AddMinutes(5).ToString() } };
            _mockPhoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, "GH", out Arg.Any<string>()).Returns(true);
            _mockOtpService.VerifyOtpAsync(model).Returns(Task.FromResult(verifyOtpResponse));

            // Act
            var result = await _controller.VerifyOtp(model);

            // Assert
            using (new AssertionScope())
            {
                var okResult = result.Should().BeOfType<ObjectResult>().Which;
                var apiResponse = okResult.Value.Should().BeOfType<ApiResponse<VerifyOtpResponse>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status200OK}");
                apiResponse.Message.Should().Be("OTP Verified");
                apiResponse.Data.Token.Should().Be("token");
                apiResponse.Data.Expiry.Should().Be(verifyOtpResponse.Data.Expiry);
            }
        }
        [Fact]
        public async Task ResendOtp_Should_ReturnBadRequest_When_PhoneNumberIsInvalid()
        {
            // Arrange
            var model = new ResendOtpDto { PhoneNumber = "invalid", RequestId = "request-id" };
            _mockPhoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, "GH", out Arg.Any<string>()).Returns(false);

            // Act
            var result = await _controller.ResendOtp(model);

            // Assert
            using (new AssertionScope())
            {
                var badRequestResult = result.Should().BeOfType<ObjectResult>().Which;
                badRequestResult.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
                var apiResponse = badRequestResult.Value.Should().BeOfType<ApiResponse<EmptyDto>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
                apiResponse.Message.Should().Be(AppResponses.InvalidMobileNumber);
            }
        }

        [Fact]
        public async Task ResendOtp_Should_ReturnOk_When_OtpIsResentSuccessfully()
        {
            // Arrange
            var model = new ResendOtpDto { PhoneNumber = "**********", RequestId = "request-id" };
            var resendOtpResponse = new ApiResponse<GenerateOtpResponse> { Code = $"{StatusCodes.Status200OK}", Message = "OTP Resent", Data = new GenerateOtpResponse() };
            _mockPhoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, "GH", out Arg.Any<string>()).Returns(true);
            _mockOtpService.ResendOtpAsync(model).Returns(Task.FromResult(resendOtpResponse));

            // Act
            var result = await _controller.ResendOtp(model);

            // Assert
            using (new AssertionScope())
            {
                var okResult = result.Should().BeOfType<ObjectResult>().Which;
                var apiResponse = okResult.Value.Should().BeOfType<ApiResponse<GenerateOtpResponse>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status200OK}");
                apiResponse.Message.Should().Be("OTP Resent");
            }
        }

        [Fact]
        public async Task ResendOtp_Should_ReturnBadRequest_When_OtpServiceReturnsBadRequest()
        {
            // Arrange
            var model = new ResendOtpDto { PhoneNumber = "**********", RequestId = "request-id" };
            var resendOtpResponse = new ApiResponse<GenerateOtpResponse> { Code = $"{StatusCodes.Status400BadRequest}", Message = "Bad Request" };
            _mockPhoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, "GH", out Arg.Any<string>()).Returns(true);
            _mockOtpService.ResendOtpAsync(model).Returns(Task.FromResult(resendOtpResponse));

            // Act
            var result = await _controller.ResendOtp(model);

            // Assert
            using (new AssertionScope())
            {
                var badRequestResult = result.Should().BeOfType<ObjectResult>().Which;
                badRequestResult.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
                var apiResponse = badRequestResult.Value.Should().BeOfType<ApiResponse<GenerateOtpResponse>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
                apiResponse.Message.Should().Be("Bad Request");
            }
        }

        [Fact]
        public async Task ResendOtp_Should_ReturnInternalServerError_When_OtpServiceReturnsInternalServerError()
        {
            // Arrange
            var model = new ResendOtpDto { PhoneNumber = "**********", RequestId = "request-id" };
            var resendOtpResponse = new ApiResponse<GenerateOtpResponse> { Code = $"{StatusCodes.Status500InternalServerError}", Message = "Internal Server Error" };
            _mockPhoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, "GH", out Arg.Any<string>()).Returns(true);
            _mockOtpService.ResendOtpAsync(model).Returns(Task.FromResult(resendOtpResponse));

            // Act
            var result = await _controller.ResendOtp(model);

            // Assert
            using (new AssertionScope())
            {
                var internalServerErrorResult = result.Should().BeOfType<ObjectResult>().Which;
                internalServerErrorResult.StatusCode.Should().Be(StatusCodes.Status500InternalServerError);
                var apiResponse = internalServerErrorResult.Value.Should().BeOfType<ApiResponse<GenerateOtpResponse>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status500InternalServerError}");
                apiResponse.Message.Should().Be("Internal Server Error");
            }
        }
        [Fact]
        public async Task VerifyOtpCheckout_Should_ReturnBadRequest_When_PhoneNumberIsInvalid()
        {
            // Arrange
            var model = new VerifyOtpDtoWebCheckout { PhoneNumber = "invalid", RequestId = "request-id", OtpCode = "otp-code", AppId = Guid.NewGuid() };
            _mockPhoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, "GH", out Arg.Any<string>()).Returns(false);

            // Act
            var result = await _controller.VerifyOtpCheckout(model);

            // Assert
            using (new AssertionScope())
            {
                var badRequestResult = result.Should().BeOfType<ObjectResult>().Which;
                badRequestResult.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
                var apiResponse = badRequestResult.Value.Should().BeOfType<ApiResponse<EmptyDto>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
                apiResponse.Message.Should().Be(AppResponses.InvalidMobileNumber);
            }
        }

        [Fact]
        public async Task VerifyOtpCheckout_Should_ReturnOk_When_OtpIsVerifiedSuccessfully()
        {
            // Arrange
            var model = new VerifyOtpDtoWebCheckout { PhoneNumber = "**********", RequestId = "request-id", OtpCode = "otp-code", AppId = Guid.NewGuid() };
            var verifyOtpResponse = new ApiResponse<VerifyOtpResponse> { Code = $"{StatusCodes.Status200OK}", Message = "OTP Verified", Data = new VerifyOtpResponse() };
            _mockPhoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, "GH", out Arg.Any<string>()).Returns(true);
            _mockOtpService.VerifyOtp2Async(model).Returns(Task.FromResult(verifyOtpResponse));

            // Act
            var result = await _controller.VerifyOtpCheckout(model);

            // Assert
            using (new AssertionScope())
            {
                var okResult = result.Should().BeOfType<ObjectResult>().Which;
                var apiResponse = okResult.Value.Should().BeOfType<ApiResponse<VerifyOtpResponse>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status200OK}");
                apiResponse.Message.Should().Be("OTP Verified");
            }
        }

        [Fact]
        public async Task VerifyOtpCheckout_Should_ReturnBadRequest_When_OtpServiceReturnsBadRequest()
        {
            // Arrange
            var model = new VerifyOtpDtoWebCheckout { PhoneNumber = "**********", RequestId = "request-id", OtpCode = "otp-code", AppId = Guid.NewGuid() };
            var verifyOtpResponse = new ApiResponse<VerifyOtpResponse> { Code = $"{StatusCodes.Status400BadRequest}", Message = "Bad Request" };
            _mockPhoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, "GH", out Arg.Any<string>()).Returns(true);
            _mockOtpService.VerifyOtp2Async(model).Returns(Task.FromResult(verifyOtpResponse));

            // Act
            var result = await _controller.VerifyOtpCheckout(model);

            // Assert
            using (new AssertionScope())
            {
                var badRequestResult = result.Should().BeOfType<ObjectResult>().Which;
                badRequestResult.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
                var apiResponse = badRequestResult.Value.Should().BeOfType<ApiResponse<VerifyOtpResponse>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
                apiResponse.Message.Should().Be("Bad Request");
            }
        }

        [Fact]
        public async Task VerifyOtpCheckout_Should_ReturnInternalServerError_When_OtpServiceReturnsInternalServerError()
        {
            // Arrange
            var model = new VerifyOtpDtoWebCheckout { PhoneNumber = "**********", RequestId = "request-id", OtpCode = "otp-code", AppId = Guid.NewGuid() };
            var verifyOtpResponse = new ApiResponse<VerifyOtpResponse> { Code = $"{StatusCodes.Status500InternalServerError}", Message = "Internal Server Error" };
            _mockPhoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, "GH", out Arg.Any<string>()).Returns(true);
            _mockOtpService.VerifyOtp2Async(model).Returns(Task.FromResult(verifyOtpResponse));

            // Act
            var result = await _controller.VerifyOtpCheckout(model);

            // Assert
            using (new AssertionScope())
            {
                var internalServerErrorResult = result.Should().BeOfType<ObjectResult>().Which;
                internalServerErrorResult.StatusCode.Should().Be(StatusCodes.Status500InternalServerError);
                var apiResponse = internalServerErrorResult.Value.Should().BeOfType<ApiResponse<VerifyOtpResponse>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status500InternalServerError}");
                apiResponse.Message.Should().Be("Internal Server Error");
            }
        }


    }
}
