using FluentAssertions;
using FluentAssertions.Execution;
using Flurl.Http.Testing;
using Hubtel.Authentication.Core.Api.Controllers;
using Hubtel.Authentication.Core.Api.Dtos;
using Hubtel.Authentication.Core.Api.Models;
using Hubtel.Authentication.Core.Api.Models.AppModels;
using Hubtel.Authentication.Core.Api.Models.Requests;
using Hubtel.Authentication.Core.Api.Models.Responses;
using Hubtel.Authentication.Core.Api.Options;
using Hubtel.Authentication.Core.Api.Services.Interface;
using Hubtel.PhoneNumbers.Extensions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute;

namespace Hubtel.Authentication.Core.Api.Tests.Controllers
{
    public class QrControllerTests
    {
        private readonly IQrService _mockQrService;
        private readonly IPhoneNumberParser _mockPhoneNumberParser;
        private readonly ILogger<QrController> _mockLogger;
        private readonly IAccountLookupService _mockAccountLookupService;
        private readonly QrOptions _qrOptions;
        private readonly QrController _controller;
        private readonly HttpTest _httpTest;

        public QrControllerTests()
        {
            _mockQrService = Substitute.For<IQrService>();
            _mockPhoneNumberParser = Substitute.For<IPhoneNumberParser>();
            _mockLogger = Substitute.For<ILogger<QrController>>();
            _mockAccountLookupService = Substitute.For<IAccountLookupService>();
            _qrOptions = new QrOptions { Failure = "http://failure.url", Success = "http://success.url" };
            var options = Substitute.For<IOptions<QrOptions>>();
            options.Value.Returns(_qrOptions);
            _controller = new QrController(_mockQrService, _mockPhoneNumberParser, _mockLogger, _mockAccountLookupService, options);
            _httpTest = new HttpTest();
        }

        [Fact]
        public async Task ConfirmQr_Should_ReturnBadRequest_When_PhoneNumberIsInvalid()
        {
            // Arrange
            var model = new QrDataRequest { PhoneNumber = "invalid", QrData = "qr-data", AppId = Guid.NewGuid() };
            _mockPhoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, "GH", out Arg.Any<string>()).Returns(false);

            // Act
            var result = await _controller.ConfirmQr(model);

            // Assert
            using (new AssertionScope())
            {
                var badRequestResult = result.Should().BeOfType<ObjectResult>().Which;
                badRequestResult.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
                var apiResponse = badRequestResult.Value.Should().BeOfType<ApiResponse<EmptyDto>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
                apiResponse.Message.Should().Be(AppResponses.InvalidMobileNumber);
            }
        }

        [Fact]
        public async Task ConfirmQr_Should_ReturnBadRequest_When_CachedQrInfoIsNull()
        {
            // Arrange
            var model = new QrDataRequest { PhoneNumber = "**********", QrData = "qr-data", AppId = Guid.NewGuid() };
            _mockPhoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, "GH", out Arg.Any<string>()).Returns(true);
            _mockAccountLookupService.RetrieveCachedQrInfo(model.AppId, model.QrData, model.PhoneNumber).Returns(Task.FromResult<string>(null!));

            // Act
            var result = await _controller.ConfirmQr(model);

            // Assert
            using (new AssertionScope())
            {
                _httpTest.ShouldHaveCalled($"{_qrOptions.Failure}/*");
                var badRequestResult = result.Should().BeOfType<ObjectResult>().Which;
                badRequestResult.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
                var apiResponse = badRequestResult.Value.Should().BeOfType<ApiResponse<dynamic>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
                apiResponse.Message.Should().Be("Login failed");
            }
        }

        [Fact]
        public async Task ConfirmQr_Should_ReturnOk_When_QrIsVerifiedSuccessfully()
        {
            // Arrange
            var model = new QrDataRequest { PhoneNumber = "**********", QrData = "qr-data", AppId = Guid.NewGuid() };
            var verifyQrResponse = new ApiResponse<VerifyQrResponse> { Code = $"{StatusCodes.Status200OK}", Message = "QR Verified", Data = new VerifyQrResponse() };
            _mockPhoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, "GH", out Arg.Any<string>()).Returns(true);
            _mockAccountLookupService.RetrieveCachedQrInfo(model.AppId, model.QrData, model.PhoneNumber).Returns(Task.FromResult("cached-qr-info"));
            _mockQrService.VerifyQrTokenAsync(model).Returns(Task.FromResult(verifyQrResponse));

            // Act
            var result = await _controller.ConfirmQr(model);

            // Assert
            using (new AssertionScope())
            {
                _httpTest.ShouldHaveCalled($"{_qrOptions.Success}/*");
                var okResult = result.Should().BeOfType<ObjectResult>().Which;
                var apiResponse = okResult.Value.Should().BeOfType<ApiResponse<VerifyQrResponse>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status200OK}");
                apiResponse.Message.Should().Be("QR Verified");
            }
        }

        [Fact]
        public async Task ConfirmQr_Should_ReturnBadRequest_When_QrServiceReturnsBadRequest()
        {
            // Arrange
            var model = new QrDataRequest { PhoneNumber = "**********", QrData = "qr-data", AppId = Guid.NewGuid() };
            var verifyQrResponse = new ApiResponse<VerifyQrResponse> { Code = $"{StatusCodes.Status400BadRequest}", Message = "Bad Request" };
            _mockPhoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, "GH", out Arg.Any<string>()).Returns(true);
            _mockAccountLookupService.RetrieveCachedQrInfo(model.AppId, model.QrData, model.PhoneNumber).Returns(Task.FromResult("cached-qr-info"));
            _mockQrService.VerifyQrTokenAsync(model).Returns(Task.FromResult(verifyQrResponse));

            // Act
            var result = await _controller.ConfirmQr(model);

            // Assert
            using (new AssertionScope())
            {
                var badRequestResult = result.Should().BeOfType<ObjectResult>().Which;
                badRequestResult.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
                var apiResponse = badRequestResult.Value.Should().BeOfType<ApiResponse<VerifyQrResponse>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
                apiResponse.Message.Should().Be("Bad Request");
            }
        }

        [Fact]
        public async Task ConfirmQr_Should_ReturnInternalServerError_When_QrServiceReturnsInternalServerError()
        {
            // Arrange
            var model = new QrDataRequest { PhoneNumber = "**********", QrData = "qr-data", AppId = Guid.NewGuid() };
            var verifyQrResponse = new ApiResponse<VerifyQrResponse> { Code = $"{StatusCodes.Status500InternalServerError}", Message = "Internal Server Error" };
            _mockPhoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, "GH", out Arg.Any<string>()).Returns(true);
            _mockAccountLookupService.RetrieveCachedQrInfo(model.AppId, model.QrData, model.PhoneNumber).Returns(Task.FromResult("cached-qr-info"));
            _mockQrService.VerifyQrTokenAsync(model).Returns(Task.FromResult(verifyQrResponse));

            // Act
            var result = await _controller.ConfirmQr(model);

            // Assert
            using (new AssertionScope())
            {
                var internalServerErrorResult = result.Should().BeOfType<ObjectResult>().Which;
                internalServerErrorResult.StatusCode.Should().Be(StatusCodes.Status500InternalServerError);
                var apiResponse = internalServerErrorResult.Value.Should().BeOfType<ApiResponse<VerifyQrResponse>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status500InternalServerError}");
                apiResponse.Message.Should().Be("Internal Server Error");
            }
        }
        [Fact]
        public async Task GenerateQr_Should_ReturnBadRequest_When_PhoneNumberIsInvalid()
        {
            // Arrange
            var model = new QrChallengeRequest { PhoneNumber = "invalid", CountryCode = "GH", AppId = Guid.NewGuid() };
            _mockPhoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, model.CountryCode, out Arg.Any<string>()).Returns(false);

            // Act
            var result = await _controller.GenerateQr(model);

            // Assert
            using (new AssertionScope())
            {
                var badRequestResult = result.Should().BeOfType<ObjectResult>().Which;
                badRequestResult.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
                var apiResponse = badRequestResult.Value.Should().BeOfType<ApiResponse<EmptyDto>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
                apiResponse.Message.Should().Be(AppResponses.InvalidMobileNumber);
            }
        }

        [Fact]
        public async Task GenerateQr_Should_ReturnBadRequest_When_ClientAppDetailsIsNull()
        {
            // Arrange
            var model = new QrChallengeRequest { PhoneNumber = "**********", CountryCode = "GH", AppId = Guid.NewGuid() };
            _mockPhoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, model.CountryCode, out Arg.Any<string>()).Returns(true);
            _mockAccountLookupService.ClientAppDetails(model.AppId).Returns(Task.FromResult<GetBackOfficeAuthConfigStoreResponse>(null!));

            // Act
            var result = await _controller.GenerateQr(model);

            // Assert
            using (new AssertionScope())
            {

                var badRequestResult = result.Should().BeOfType<ObjectResult>().Which;
                badRequestResult.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
                var apiResponse = badRequestResult.Value.Should().BeOfType<ApiResponse<EmptyDto>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
                apiResponse.Message.Should().Be(AppResponses.AppMayNotBeConfigured);
            }
        }

        [Fact]
        public async Task GenerateQr_Should_ReturnBadRequest_When_AccountLookUpUrlIsEmpty()
        {
            // Arrange
            var model = new QrChallengeRequest { PhoneNumber = "**********", CountryCode = "GH", AppId = Guid.NewGuid() };
            var accountDetails = new GetBackOfficeAuthConfigStoreResponse { AccountLookUpUrl = "" };
            _mockPhoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, model.CountryCode, out Arg.Any<string>()).Returns(true);
            _mockAccountLookupService.ClientAppDetails(model.AppId).Returns(Task.FromResult(accountDetails));

            // Act
            var result = await _controller.GenerateQr(model);

            // Assert
            using (new AssertionScope())
            {
                var badRequestResult = result.Should().BeOfType<ObjectResult>().Which;
                badRequestResult.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
                var apiResponse = badRequestResult.Value.Should().BeOfType<ApiResponse<EmptyDto>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
                apiResponse.Message.Should().Be(AppResponses.AppMayNotBeConfigured);
            }
        }

        [Fact]
        public async Task GenerateQr_Should_ReturnInternalServerError_When_AccountLookupResponseIsNull()
        {
            // Arrange
            var model = new QrChallengeRequest { PhoneNumber = "**********", CountryCode = "GH", AppId = Guid.NewGuid() };
            var accountDetails = new GetBackOfficeAuthConfigStoreResponse { AccountLookUpUrl = "http://example.com" };
            _mockPhoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, model.CountryCode, out Arg.Any<string>()).Returns(true);
            _mockAccountLookupService.ClientAppDetails(model.AppId).Returns(Task.FromResult(accountDetails));
            _mockAccountLookupService.LookUpCustomerInfoByPhoneNumber(accountDetails.AccountLookUpUrl, model.PhoneNumber).Returns(Task.FromResult<PhoneNumberLookupResponse>(null!));

            // Act
            var result = await _controller.GenerateQr(model);

            // Assert
            using (new AssertionScope())
            {
                var internalServerErrorResult = result.Should().BeOfType<ObjectResult>().Which;
                internalServerErrorResult.StatusCode.Should().Be(StatusCodes.Status500InternalServerError);
                var apiResponse = internalServerErrorResult.Value.Should().BeOfType<ApiResponse<EmptyDto>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status500InternalServerError}");
                apiResponse.Message.Should().Be(AppResponses.HttpRequestFailed);
            }
        }

        [Fact]
        public async Task GenerateQr_Should_ReturnForbidden_When_AccountLookupResponseCodeIs403()
        {
            // Arrange
            var model = new QrChallengeRequest { PhoneNumber = "**********", CountryCode = "GH", AppId = Guid.NewGuid() };
            var accountDetails = new GetBackOfficeAuthConfigStoreResponse { AccountLookUpUrl = "http://example.com" };
            var accountLookupResponse = new PhoneNumberLookupResponse { Code = $"{StatusCodes.Status403Forbidden}", Message = "Forbidden", Data = new PhoneNumberLookupData() };
            _mockPhoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, model.CountryCode, out Arg.Any<string>()).Returns(true);
            _mockAccountLookupService.ClientAppDetails(model.AppId).Returns(Task.FromResult(accountDetails));
            _mockAccountLookupService.LookUpCustomerInfoByPhoneNumber(accountDetails.AccountLookUpUrl, model.PhoneNumber).Returns(Task.FromResult(accountLookupResponse));

            // Act
            var result = await _controller.GenerateQr(model);

            // Assert
            using (new AssertionScope())
            {
                var forbiddenResult = result.Should().BeOfType<ObjectResult>().Which;
                forbiddenResult.StatusCode.Should().Be(StatusCodes.Status403Forbidden);
                var apiResponse = forbiddenResult.Value.Should().BeOfType<ApiResponse<PhoneNumberLookupData>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status403Forbidden}");
                apiResponse.Message.Should().Be("Forbidden");
                apiResponse.Data.Should().BeEquivalentTo(accountLookupResponse.Data);
            }
        }

        [Fact]
        public async Task GenerateQr_Should_ReturnInternalServerError_When_AccountLookupResponseCodeIs500()
        {
            // Arrange
            var model = new QrChallengeRequest { PhoneNumber = "**********", CountryCode = "GH", AppId = Guid.NewGuid() };
            var accountDetails = new GetBackOfficeAuthConfigStoreResponse { AccountLookUpUrl = "http://example.com" };
            var accountLookupResponse = new PhoneNumberLookupResponse { Code = $"{StatusCodes.Status500InternalServerError}", Message = "Internal Server Error", Data = new PhoneNumberLookupData() };
            _mockPhoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, model.CountryCode, out Arg.Any<string>()).Returns(true);
            _mockAccountLookupService.ClientAppDetails(model.AppId).Returns(Task.FromResult(accountDetails));
            _mockAccountLookupService.LookUpCustomerInfoByPhoneNumber(accountDetails.AccountLookUpUrl, model.PhoneNumber).Returns(Task.FromResult(accountLookupResponse));

            // Act
            var result = await _controller.GenerateQr(model);

            // Assert
            using (new AssertionScope())
            {
                var internalServerErrorResult = result.Should().BeOfType<ObjectResult>().Which;
                internalServerErrorResult.StatusCode.Should().Be(StatusCodes.Status500InternalServerError);
                var apiResponse = internalServerErrorResult.Value.Should().BeOfType<ApiResponse<PhoneNumberLookupData>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status500InternalServerError}");
                apiResponse.Message.Should().Be("Internal Server Error");
                apiResponse.Data.Should().BeEquivalentTo(accountLookupResponse.Data);
            }
        }

        [Fact]
        public async Task GenerateQr_Should_ReturnOk_When_QrIsGeneratedSuccessfully()
        {
            // Arrange
            var model = new QrChallengeRequest { PhoneNumber = "**********", CountryCode = "GH", AppId = Guid.NewGuid() };
            var accountDetails = new GetBackOfficeAuthConfigStoreResponse { AccountLookUpUrl = "http://example.com" };
            var accountLookupResponse = new PhoneNumberLookupResponse { Code = $"{StatusCodes.Status200OK}", Message = "Success", Data = new PhoneNumberLookupData { MobileNumber = "**********", Email = "<EMAIL>" } };
            var qrResponse = new ApiResponse<QrChallengeResponse> { Code = $"{StatusCodes.Status200OK}", Message = "QR Generated", Data = new QrChallengeResponse() };
            _mockPhoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, model.CountryCode, out Arg.Any<string>()).Returns(true);
            _mockAccountLookupService.ClientAppDetails(model.AppId).Returns(Task.FromResult(accountDetails));
            _mockAccountLookupService.LookUpCustomerInfoByPhoneNumber(accountDetails.AccountLookUpUrl, model.PhoneNumber).Returns(Task.FromResult(accountLookupResponse));
            _mockQrService.GenerateQrTokenAsync(Arg.Any<QrData>()).Returns(Task.FromResult(qrResponse));

            // Act
            var result = await _controller.GenerateQr(model);

            // Assert
            using (new AssertionScope())
            {
                var okResult = result.Should().BeOfType<ObjectResult>().Which;
                var apiResponse = okResult.Value.Should().BeOfType<ApiResponse<QrChallengeResponse>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status200OK}");
                apiResponse.Message.Should().Be("QR Generated");
                apiResponse.Data.Should().NotBeNull();
            }
        }
    }


}
