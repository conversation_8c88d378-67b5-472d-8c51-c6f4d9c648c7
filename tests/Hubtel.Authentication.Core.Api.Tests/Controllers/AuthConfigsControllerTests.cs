using FluentAssertions;
using Hubtel.Authentication.Core.Api.Controllers;
using Hubtel.Authentication.Core.Api.Dtos;
using Hubtel.Authentication.Core.Api.Models;
using Hubtel.Authentication.Core.Api.Services;
using Microsoft.AspNetCore.Http;
using NSubstitute;

namespace Hubtel.Authentication.Core.Api.Tests.Controllers;
public class AuthConfigsControllerTests
{
    private readonly IAuthenticationConfigStoreService _configStoreService;
    private readonly AuthConfigsController _controller;

    public AuthConfigsControllerTests()
    {
        _configStoreService = Substitute.For<IAuthenticationConfigStoreService>();
        _controller = new AuthConfigsController(_configStoreService);
    }

    [Fact]
    public async Task GetAppConfigs_Should_Return200Ok_When_ConfigsExist()
    {
        // Arrange
        var filter = new BaseFilter();
        var response = new ApiResponse<PagedResult<GetAuthConfigStoreResponse>>("Success", new PagedResult<GetAuthConfigStoreResponse>())
        {
            Code = $"{StatusCodes.Status200OK}"
        };
        _configStoreService.GetAsync<GetAuthConfigStoreResponse>(filter).Returns(response);

        // Act
        var result = await _controller.GetAppConfigs(filter);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status200OK}");
            result.Message.Should().Be("Success");
            result.Data.Should().Be(response.Data);
        }
    }

    [Fact]
    public async Task GetAppConfigs_Should_Return400BadRequest_When_ServiceFails()
    {
        // Arrange
        var filter = new BaseFilter();
        var response = new ApiResponse<PagedResult<GetAuthConfigStoreResponse>>("Failure", new PagedResult<GetAuthConfigStoreResponse>())
        {
            Code = $"{StatusCodes.Status400BadRequest}",
            Data = null!
        };
        _configStoreService.GetAsync<GetAuthConfigStoreResponse>(filter).Returns(response);

        // Act
        var result = await _controller.GetAppConfigs(filter);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
            result.Message.Should().Be("Failure");
            result.Data.Should().BeNull();
        }
    }

    [Fact]
    public async Task GetAppConfig_Should_Return200Ok_When_ConfigExists()
    {
        // Arrange
        var id = Guid.NewGuid();
        var response = new GetAuthConfigStoreResponse();
        _configStoreService.GetCachedClientAsync<GetAuthConfigStoreResponse>(id).Returns(response);

        // Act
        var result = await _controller.GetAppConfig(id);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status200OK}");
            result.Message.Should().Be("application details retrieved");
            result.Data.Should().Be(response);
        }
    }

    [Fact]
    public async Task GetAppConfig_Should_Return400BadRequest_When_ConfigDoesNotExist()
    {
        // Arrange
        var id = Guid.NewGuid();
        _configStoreService.GetCachedClientAsync<GetAuthConfigStoreResponse>(id).Returns((GetAuthConfigStoreResponse)null!);

        // Act
        var result = await _controller.GetAppConfig(id);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
            result.Message.Should().Be("App may not have been configured");
            result.Data.Should().BeNull();
        }
    }
}
