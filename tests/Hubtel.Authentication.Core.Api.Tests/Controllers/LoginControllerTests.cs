using FluentAssertions.Execution;
using Hubtel.Authentication.Core.Api.Controllers;
using Hubtel.Authentication.Core.Api.Dtos;
using Hubtel.Authentication.Core.Api.Models.AppModels;
using Hubtel.Authentication.Core.Api.Models.Responses;
using Hubtel.Authentication.Core.Api.Services.Interface;
using Hubtel.Authentication.Core.Api.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using NSubstitute;
using Hubtel.Authentication.Core.Api.Models;
using FluentAssertions;

namespace Hubtel.Authentication.Core.Api.Tests.Controllers
{
    public class LoginControllerTests
    {
        private readonly IAccountLookupService _mockAccountLookupService;
        private readonly IAuthenticationConfigStoreService _mockConfigStoreService;
        private readonly LoginController _controller;

        public LoginControllerTests()
        {
            _mockAccountLookupService = Substitute.For<IAccountLookupService>();
            _mockConfigStoreService = Substitute.For<IAuthenticationConfigStoreService>();
            _controller = new LoginController(_mockAccountLookupService, _mockConfigStoreService);
        }

        [Fact]
        public async Task AccountDetails_Should_ReturnBadRequest_When_AppConfigIsNull()
        {
            // Arrange
            var appId = Guid.NewGuid();
            var phoneNumber = "**********";
            _mockConfigStoreService.GetAsync<GetAuthConfigStoreResponse>(appId).Returns(Task.FromResult<GetAuthConfigStoreResponse>(null!));

            // Act
            var result = await _controller.AccountDetails(appId, phoneNumber);

            // Assert
            using (new AssertionScope())
            {
                var badRequestResult = result.Should().BeOfType<BadRequestObjectResult>().Which;
                var apiResponse = badRequestResult.Value.Should().BeOfType<ApiResponse<EmptyDto>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
                apiResponse.Message.Should().Be(AppResponses.AppMayNotBeConfigured);
            }
        }

        [Fact]
        public async Task AccountDetails_Should_ReturnBadRequest_When_AccountLookUpUrlIsEmpty()
        {
            // Arrange
            var appId = Guid.NewGuid();
            var phoneNumber = "**********";
            var appConfig = new GetAuthConfigStoreResponse { AccountLookUpUrl = "" };
            _mockConfigStoreService.GetAsync<GetAuthConfigStoreResponse>(appId).Returns(Task.FromResult(appConfig));

            // Act
            var result = await _controller.AccountDetails(appId, phoneNumber);

            // Assert
            using (new AssertionScope())
            {
                var badRequestResult = result.Should().BeOfType<BadRequestObjectResult>().Which;
                var apiResponse = badRequestResult.Value.Should().BeOfType<ApiResponse<EmptyDto>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
                apiResponse.Message.Should().Be(AppResponses.AppMayNotBeConfigured);
            }
        }

        [Fact]
        public async Task AccountDetails_Should_ReturnBadRequest_When_LookupResponseIsNull()
        {
            // Arrange
            var appId = Guid.NewGuid();
            var phoneNumber = "**********";
            var appConfig = new GetAuthConfigStoreResponse { AccountLookUpUrl = "http://example.com" };
            _mockConfigStoreService.GetAsync<GetAuthConfigStoreResponse>(appId).Returns(Task.FromResult(appConfig));
            _mockAccountLookupService.LookUpCustomerInfoByPhoneNumber(appConfig.AccountLookUpUrl, phoneNumber).Returns(Task.FromResult<PhoneNumberLookupResponse>(null!));

            // Act
            var result = await _controller.AccountDetails(appId, phoneNumber);

            // Assert
            using (new AssertionScope())
            {
                var badRequestResult = result.Should().BeOfType<BadRequestObjectResult>().Which;
                var apiResponse = badRequestResult.Value.Should().BeOfType<ApiResponse<EmptyDto>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status500InternalServerError}");
                apiResponse.Message.Should().Be(AppResponses.HttpRequestFailed);
            }
        }

        [Fact]
        public async Task AccountDetails_Should_ReturnNotFound_When_LookupResponseCodeIs404()
        {
            // Arrange
            var appId = Guid.NewGuid();
            var phoneNumber = "**********";
            var appConfig = new GetAuthConfigStoreResponse { AccountLookUpUrl = "http://example.com" };
            var lookupResponse = new PhoneNumberLookupResponse { Code = $"{StatusCodes.Status404NotFound}", Data = new PhoneNumberLookupData() };
            _mockConfigStoreService.GetAsync<GetAuthConfigStoreResponse>(appId).Returns(Task.FromResult(appConfig));
            _mockAccountLookupService.LookUpCustomerInfoByPhoneNumber(appConfig.AccountLookUpUrl, phoneNumber).Returns(Task.FromResult(lookupResponse));

            // Act
            var result = await _controller.AccountDetails(appId, phoneNumber);

            // Assert
            using (new AssertionScope())
            {
                var notFoundResult = result.Should().BeOfType<NotFoundObjectResult>().Which;
                var apiResponse = notFoundResult.Value.Should().BeOfType<ApiResponse<PhoneNumberLookupData>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status404NotFound}");
                apiResponse.Message.Should().Be(AppResponses.PhoneNumberNotAssociatedWithAccount);
                apiResponse.Data.Should().BeEquivalentTo(lookupResponse.Data);
            }
        }

        [Fact]
        public async Task AccountDetails_Should_ReturnInternalServerError_When_LookupResponseCodeIs500()
        {
            // Arrange
            var appId = Guid.NewGuid();
            var phoneNumber = "**********";
            var appConfig = new GetAuthConfigStoreResponse { AccountLookUpUrl = "http://example.com" };
            var lookupResponse = new PhoneNumberLookupResponse { Code = $"{StatusCodes.Status500InternalServerError}", Message = "Internal Server Error", Data = new PhoneNumberLookupData() };
            _mockConfigStoreService.GetAsync<GetAuthConfigStoreResponse>(appId).Returns(Task.FromResult(appConfig));
            _mockAccountLookupService.LookUpCustomerInfoByPhoneNumber(appConfig.AccountLookUpUrl, phoneNumber).Returns(Task.FromResult(lookupResponse));

            // Act
            var result = await _controller.AccountDetails(appId, phoneNumber);

            // Assert
            using (new AssertionScope())
            {
                var internalServerErrorResult = result.Should().BeOfType<ObjectResult>().Which;
                internalServerErrorResult.StatusCode.Should().Be(StatusCodes.Status500InternalServerError);
                var apiResponse = internalServerErrorResult.Value.Should().BeOfType<ApiResponse<PhoneNumberLookupData>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status500InternalServerError}");
                apiResponse.Message.Should().Be("Internal Server Error");
                apiResponse.Data.Should().BeEquivalentTo(lookupResponse.Data);
            }
        }

        [Fact]
        public async Task AccountDetails_Should_ReturnOk_When_LookupResponseIsValid()
        {
            // Arrange
            var appId = Guid.NewGuid();
            var phoneNumber = "**********";
            var appConfig = new GetAuthConfigStoreResponse { AccountLookUpUrl = "http://example.com" };
            var lookupResponse = new PhoneNumberLookupResponse { Code = $"{StatusCodes.Status200OK}", Message = "Success", Data = new PhoneNumberLookupData() };
            _mockConfigStoreService.GetAsync<GetAuthConfigStoreResponse>(appId).Returns(Task.FromResult(appConfig));
            _mockAccountLookupService.LookUpCustomerInfoByPhoneNumber(appConfig.AccountLookUpUrl, phoneNumber).Returns(Task.FromResult(lookupResponse));

            // Act
            var result = await _controller.AccountDetails(appId, phoneNumber);

            // Assert
            using (new AssertionScope())
            {
                var okResult = result.Should().BeOfType<OkObjectResult>().Which;
                var apiResponse = okResult.Value.Should().BeOfType<ApiResponse<PhoneNumberLookupData>>().Which;
                apiResponse.Code.Should().Be($"{StatusCodes.Status200OK}");
                apiResponse.Message.Should().Be("Success");
                apiResponse.Data.Should().BeEquivalentTo(lookupResponse.Data);
            }
        }
    }
}
