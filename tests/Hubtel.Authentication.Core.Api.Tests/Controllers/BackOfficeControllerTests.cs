using FluentAssertions;
using Hubtel.Authentication.Commons.Models;
using Hubtel.Authentication.Core.Api.Controllers;
using Hubtel.Authentication.Core.Api.Data.Entities;
using Hubtel.Authentication.Core.Api.Dtos;
using Hubtel.Authentication.Core.Api.Models;
using Hubtel.Authentication.Core.Api.Models.AppModels;
using Hubtel.Authentication.Core.Api.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using NSubstitute;

namespace Hubtel.Authentication.Core.Api.Tests.Controllers;
public class BackOfficeControllerTests
{
    private readonly IAuthenticationConfigStoreService _configStoreService;
    private readonly BackOfficeController _controller;

    public BackOfficeControllerTests()
    {
        _configStoreService = Substitute.For<IAuthenticationConfigStoreService>();
        var logger = Substitute.For<ILogger<BackOfficeController>>();
        _controller = new BackOfficeController(_configStoreService, logger);
    }

    [Fact]
    public async Task GetAppConfigs_Should_Return200Ok_When_ConfigsExist()
    {
        // Arrange
        var filter = new BaseFilter();
        var response = new ApiResponse<PagedResult<GetBackOfficeAuthConfigStoreResponse>>("Success", new PagedResult<GetBackOfficeAuthConfigStoreResponse>())
        {
            Code = $"{StatusCodes.Status200OK}"
        };
        _configStoreService.GetAsync<GetBackOfficeAuthConfigStoreResponse>(filter).Returns(response);

        // Act
        var result = await _controller.GetAppConfigs(filter);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status200OK}");
            result.Message.Should().Be("Success");
            result.Data.Should().Be(response.Data);
        }
    }

    [Fact]
    public async Task GetAppConfigs_Should_Return400BadRequest_When_ServiceFails()
    {
        // Arrange
        var filter = new BaseFilter();
        var response = new ApiResponse<PagedResult<GetBackOfficeAuthConfigStoreResponse>>("Failure", null!)
        {
            Code = $"{StatusCodes.Status400BadRequest}"
        };
        _configStoreService.GetAsync<GetBackOfficeAuthConfigStoreResponse>(filter).Returns(response);

        // Act
        var result = await _controller.GetAppConfigs(filter);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
            result.Message.Should().Be("Failure");
            result.Data.Should().BeNull();
        }
    }

    [Fact]
    public async Task GetAllPendingAppConfigs_Should_Return200Ok_When_PendingConfigsExist()
    {
        // Arrange
        var filter = new BaseFilter();
        var response = new ApiResponse<PagedResult<GetBackOfficeAuthConfigStoreResponse>>("Success", new PagedResult<GetBackOfficeAuthConfigStoreResponse>())
        {
            Code = $"{StatusCodes.Status200OK}"
        };
        _configStoreService.GetAllPendingAsync(filter, Arg.Any<CancellationToken>()).Returns(response);

        // Act
        CancellationToken cancellationToken = default;
        var result = await _controller.GetAllPendingAppConfigs(filter,cancellationToken);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status200OK}");
            result.Message.Should().Be("Success");
            result.Data.Should().Be(response.Data);
        }
    }

    [Fact]
    public async Task GetAllPendingAppConfigs_Should_Return400BadRequest_When_ServiceFails()
    {
        // Arrange
        var filter = new BaseFilter();
        var response = new ApiResponse<PagedResult<GetBackOfficeAuthConfigStoreResponse>>("Failure", null!)
        {
            Code = $"{StatusCodes.Status400BadRequest}"
        };
        _configStoreService.GetAllPendingAsync(filter, Arg.Any<CancellationToken>()).Returns(response);

        // Act
        CancellationToken cancellationToken = default;
        var result = await _controller.GetAllPendingAppConfigs(filter,cancellationToken);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
            result.Message.Should().Be("Failure");
            result.Data.Should().BeNull();
        }
    }

    [Fact]
    public async Task GetAllChannels_Should_Return200Ok_When_ChannelsExist()
    {
        // Act
        var result = await _controller.GetAllChannels();

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status200OK}");
            result.Message.Should().Be("channels retrieved");
            result.Data.Should().NotBeNull();
        }
    }

    [Fact]
    public async Task GetAllExternalLoginConfigurationTypes_Should_Return200Ok_When_ExternalLoginTypesExist()
    {
        // Act
        var result = await _controller.GetAllExternalLoginConfigurationTypes();

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status200OK}");
            result.Message.Should().Be("external login types retrieved");
            result.Data.Should().NotBeNull();
        }
    }

    [Fact]
    public async Task GetAppConfig_Should_Return200Ok_When_ConfigExists()
    {
        // Arrange
        var id = Guid.NewGuid();
        var response = new GetBackOfficeAuthConfigStoreResponse();
        _configStoreService.GetAsync<GetBackOfficeAuthConfigStoreResponse>(id).Returns(response);

        // Act
        var result =(ObjectResult)await _controller.GetAppConfig(id);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.StatusCode.Should().Be(StatusCodes.Status200OK);
        }
    }

    [Fact]
    public async Task GetAppConfig_Should_Return404NotFound_When_ConfigDoesNotExist()
    {
        // Arrange
        var id = Guid.NewGuid();
        _configStoreService.GetAsync<GetBackOfficeAuthConfigStoreResponse>(id).Returns((GetBackOfficeAuthConfigStoreResponse)null!);

        // Act
        var result = (ObjectResult)await _controller.GetAppConfig(id);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.StatusCode.Should().Be(StatusCodes.Status404NotFound);
        }
    }

    [Fact]
    public async Task GetAuthenticationChannels_Should_Return200Ok_When_ChannelsExist()
    {
        // Arrange
        var id = Guid.NewGuid();
        var response = new ApiResponse<List<AuthenticationChannel>>("Success", new List<AuthenticationChannel>());
        _configStoreService.GetAllAuthenticationChannelsAsync(id, Arg.Any<CancellationToken>()).Returns(response);

        // Act
        CancellationToken cancellationToken = default;
        var result = await _controller.GetAuthenticationChannels(id,cancellationToken);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status200OK}");
            result.Message.Should().Be("application channels retrieved");
            result.Data.Should().BeEquivalentTo(response.Data);
        }
    }

    [Fact]
    public async Task GetAuthenticationChannels_Should_Return400BadRequest_When_ChannelsDoNotExist()
    {
        // Arrange
        var id = Guid.NewGuid();
        _configStoreService.GetAllAuthenticationChannelsAsync(id, Arg.Any<CancellationToken>()).Returns((ApiResponse<List<AuthenticationChannel>>)null!);

        // Act
        CancellationToken cancellationToken = default;
        var result = await _controller.GetAuthenticationChannels(id,cancellationToken);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
            result.Message.Should().Be(AppResponses.ChannelsMayNotBeConfigured);
            result.Data.Should().BeNull();
        }
    }

    [Fact]
    public async Task GetExternalLoginConfigs_Should_Return200Ok_When_ExternalLoginsExist()
    {
        // Arrange
        var id = Guid.NewGuid();
        var response = new ApiResponse<List<ExternalLoginConfiguration>>("Success", new List<ExternalLoginConfiguration>());
        _configStoreService.GetExternalLoginConfigurationAsync(id, Arg.Any<CancellationToken>()).Returns(response);

        // Act
        CancellationToken cancellationToken = default;
        var result = await _controller.GetExternalLoginConfigs(id,cancellationToken);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status200OK}");
            result.Message.Should().Be("application channels retrieved");
            result.Data.Should().BeEquivalentTo(response.Data);
        }
    }

    [Fact]
    public async Task GetExternalLoginConfigs_Should_Return400BadRequest_When_ExternalLoginsDoNotExist()
    {
        // Arrange
        var id = Guid.NewGuid();
        _configStoreService.GetExternalLoginConfigurationAsync(id, Arg.Any<CancellationToken>()).Returns((ApiResponse<List<ExternalLoginConfiguration>>)null!);

        // Act
        CancellationToken cancellationToken = default;
        var result = await _controller.GetExternalLoginConfigs(id,cancellationToken);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
            result.Message.Should().Be(AppResponses.ChannelsMayNotBeConfigured);
            result.Data.Should().BeNull();
        }
    }

    [Fact]
    public async Task GetAuthenticationWhitelists_Should_Return200Ok_When_WhitelistsExist()
    {
        // Arrange
        var id = Guid.NewGuid();
        var response = new ApiResponse<List<WhiteListedNumber>>("Success", new List<WhiteListedNumber>());
        _configStoreService.GetAllAuthenticationWhitelistsAsync(id, Arg.Any<CancellationToken>()).Returns(response);

        // Act
        CancellationToken cancellationToken = default;
        var result = await _controller.GetAuthenticationWhitelists(id,cancellationToken);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status200OK}");
            result.Message.Should().Be("application whitelists retrieved");
            result.Data.Should().BeEquivalentTo(response.Data);
        }
    }

    [Fact]
    public async Task GetAuthenticationWhitelists_Should_Return400BadRequest_When_WhitelistsDoNotExist()
    {
        // Arrange
        var id = Guid.NewGuid();
        _configStoreService.GetAllAuthenticationWhitelistsAsync(id, Arg.Any<CancellationToken>()).Returns((ApiResponse<List<WhiteListedNumber>>)null!);

        // Act
        CancellationToken cancellationToken = default;
        var result = await _controller.GetAuthenticationWhitelists(id,cancellationToken);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
            result.Message.Should().Be(AppResponses.CouldNotRetrieveWhiteList);
            result.Data.Should().BeNull();
        }
    }

    [Fact]
    public async Task UpdateAppConfig_Should_Return200Ok_When_UpdateIsSuccessful()
    {
        // Arrange
        var id = Guid.NewGuid();
        var appConfig = new UpdateAuthConfigStoreRequest();
        var response = (true, new GetBackOfficeAuthConfigStoreResponse());
        _configStoreService.UpdateAsync<GetBackOfficeAuthConfigStoreResponse>(id, appConfig).Returns(response);

        // Act
        var result = await _controller.UpdateAppConfig(id, appConfig) as OkObjectResult;

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            var apiResponse = result?.Value as ApiResponse<GetBackOfficeAuthConfigStoreResponse>;
            apiResponse?.Should().NotBeNull();
            apiResponse?.Code.Should().Be($"{StatusCodes.Status200OK}");
            apiResponse?.Message.Should().Be("Successfully updated app configuration");
            apiResponse?.Data.Should().Be(response.Item2);
        }
    }

    [Fact]
    public async Task UpdateAppConfig_Should_Return400BadRequest_When_UpdateFails()
    {
        // Arrange
        var id = Guid.NewGuid();
        var appConfig = new UpdateAuthConfigStoreRequest();
        var response = (false, (GetBackOfficeAuthConfigStoreResponse)null!);
        _configStoreService.UpdateAsync<GetBackOfficeAuthConfigStoreResponse>(id, appConfig).Returns(response);

        // Act
        var result = await _controller.UpdateAppConfig(id, appConfig) as BadRequestObjectResult;

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            var apiResponse = result?.Value as ApiResponse<GetBackOfficeAuthConfigStoreResponse>;
            apiResponse.Should().NotBeNull();
            apiResponse?.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
            apiResponse?.Message.Should().Be("could not update aplication configuration. Please contact the administrator");
            apiResponse?.Data.Should().BeNull();
        }
    }

    [Fact]
    public async Task ApproveApp_Should_Return200Ok_When_ApprovalIsSuccessful()
    {
        // Arrange
        var id = Guid.NewGuid();
        var response = new ApiResponse<bool>("Success", true);
        _configStoreService.ApproveAuthConfiguration(id).Returns(response);

        // Act
        var result = await _controller.ApproveApp(id);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status200OK}");
            result.Message.Should().Be("Successfully approved app configuration");
            result.Data.Should().BeTrue();
        }
    }

    [Fact]
    public async Task ApproveApp_Should_Return400BadRequest_When_ApprovalFails()
    {
        // Arrange
        var id = Guid.NewGuid();
        var response = new ApiResponse<bool>("Failure", false)
        {
            Code = $"{StatusCodes.Status400BadRequest}"
        };
        _configStoreService.ApproveAuthConfiguration(id).Returns(response);

        // Act
        var result = await _controller.ApproveApp(id);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
            result.Message.Should().Be("Could not update auth config with the specified Id");
            result.Data.Should().BeFalse();
        }
    }

    [Fact]
    public async Task AddChannel_Should_Return200Ok_When_AdditionIsSuccessful()
    {
        // Arrange
        var id = Guid.NewGuid();
        var channels = new List<AuthenticationChannel>();
        var response = new ApiResponse<List<AuthenticationChannel>>("Success", channels);
        _configStoreService.AddAuthenticationChannelsAsync(id, channels, Arg.Any<CancellationToken>()).Returns(response);

        // Act
        CancellationToken cancellationToken = default;
        var result = await _controller.AddChannel(id, channels,cancellationToken);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status200OK}");
            result.Message.Should().Be(AppResponses.SuccessfullyAddedChannels);
            result.Data.Should().BeEquivalentTo(channels);
        }
    }

    [Fact]
    public async Task AddChannel_Should_Return400BadRequest_When_AdditionFails()
    {
        // Arrange
        var id = Guid.NewGuid();
        var channels = new List<AuthenticationChannel>();
        _configStoreService.AddAuthenticationChannelsAsync(id, channels, Arg.Any<CancellationToken>()).Returns((ApiResponse<List<AuthenticationChannel>>)null!);

        // Act
        CancellationToken cancellationToken = default;
        var result = await _controller.AddChannel(id, channels,cancellationToken);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
            result.Message.Should().Be("Could not update auth config with the specified Id");
            result.Data.Should().BeEmpty();
        }
    }

    [Fact]
    public async Task AddExternalLogin_Should_Return200Ok_When_AdditionIsSuccessful()
    {
        // Arrange
        var id = Guid.NewGuid();
        var externalLogins = new List<ExternalLoginConfigurationRequest>();
        var externalLoginsConfig = new List<ExternalLoginConfiguration>();
        var response = new ApiResponse<List<ExternalLoginConfiguration>>("Success", externalLoginsConfig);
        _configStoreService.AddExternalLoginConfiguration(id, Arg.Any<List<ExternalLoginConfiguration>>(), Arg.Any<CancellationToken>()).Returns(response);

        // Act
        CancellationToken cancellationToken = default;
        var result = await _controller.AddExternalLogin(id, externalLogins,cancellationToken);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status200OK}");
            result.Message.Should().Be(AppResponses.SuccessfullyAddedExternalLogins);
            result.Data.Should().BeEquivalentTo(externalLoginsConfig);
        }
    }

    [Fact]
    public async Task AddExternalLogin_Should_Return400BadRequest_When_AdditionFails()
    {
        // Arrange
        var id = Guid.NewGuid();
        var externalLogins = new List<ExternalLoginConfigurationRequest>();
        var externalLoginsConfig = new List<ExternalLoginConfiguration>();
        _configStoreService.AddExternalLoginConfiguration(id, externalLoginsConfig, Arg.Any<CancellationToken>()).Returns((ApiResponse<List<ExternalLoginConfiguration>>)null!);

        // Act
        CancellationToken cancellationToken = default;
        var result = await _controller.AddExternalLogin(id, externalLogins,cancellationToken);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
            result.Message.Should().Be("Could not update auth config with the specified Id");
            result.Data.Should().BeEmpty();
        }
    }

    [Fact]
    public async Task RemoveChannels_Should_Return200Ok_When_RemovalIsSuccessful()
    {
        // Arrange
        var id = Guid.NewGuid();
        var channels = new List<AuthenticationChannel>();
        var response = new ApiResponse<bool>("Success", true);
        _configStoreService.RemoveAuthenticationChannelsAsync(id, channels, Arg.Any<CancellationToken>()).Returns(response);

        // Act
        CancellationToken cancellationToken = default;
        var result = await _controller.RemoveChannels(id, channels,cancellationToken);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status200OK}");
            result.Message.Should().Be(AppResponses.SuccessfullyRemovedChannels);
            result.Data.Should().BeTrue();
        }
    }

    [Fact]
    public async Task RemoveChannels_Should_Return400BadRequest_When_RemovalFails()
    {
        // Arrange
        var id = Guid.NewGuid();
        var channels = new List<AuthenticationChannel>();
        _configStoreService.RemoveAuthenticationChannelsAsync(id, channels, Arg.Any<CancellationToken>()).Returns((ApiResponse<bool>)null!);

        // Act
        CancellationToken cancellationToken = default;
        var result = await _controller.RemoveChannels(id, channels,cancellationToken);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
            result.Message.Should().Be(AppResponses.CouldNotUpdateAuthConfig);
            result.Data.Should().BeFalse();
        }
    }

    [Fact]
    public async Task RemoveExternalLogins_Should_Return200Ok_When_RemovalIsSuccessful()
    {
        // Arrange
        var id = Guid.NewGuid();
        var externalLogins = new List<ExternalLoginConfiguration>();
        var response = new ApiResponse<bool>("Success", true);
        _configStoreService.RemoveExternalLoginsAsync(id, externalLogins, Arg.Any<CancellationToken>()).Returns(response);

        // Act
        CancellationToken cancellationToken = default;
        var result = await _controller.RemoveExternalLogins(id, externalLogins,cancellationToken);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status200OK}");
            result.Message.Should().Be(AppResponses.SuccessfullyRemovedAuthenticationTypes);
            result.Data.Should().BeTrue();
        }
    }

    [Fact]
    public async Task RemoveExternalLogins_Should_Return400BadRequest_When_RemovalFails()
    {
        // Arrange
        var id = Guid.NewGuid();
        var externalLogins = new List<ExternalLoginConfiguration>();
        var response = new ApiResponse<bool>("Failure", false)
        {
            Code = $"{StatusCodes.Status400BadRequest}"
        };
        _configStoreService.RemoveExternalLoginsAsync(id, externalLogins, Arg.Any<CancellationToken>()).Returns(response);

        // Act
        CancellationToken cancellationToken = default;
        var result = await _controller.RemoveExternalLogins(id, externalLogins,cancellationToken);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
            result.Message.Should().Be(AppResponses.CouldNotUpdateAuthConfig);
            result.Data.Should().BeFalse();
        }
    }

    [Fact]
    public async Task RemoveExternalLogins_Should_Return400BadRequest_When_ResponseIsNull()
    {
        // Arrange
        var id = Guid.NewGuid();
        var externalLogins = new List<ExternalLoginConfiguration>();
        _configStoreService.RemoveExternalLoginsAsync(id, externalLogins, Arg.Any<CancellationToken>()).Returns((ApiResponse<bool>)null!);

        // Act
        CancellationToken cancellationToken = default;
        var result = await _controller.RemoveExternalLogins(id, externalLogins,cancellationToken);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
            result.Message.Should().Be(AppResponses.CouldNotUpdateAuthConfig);
            result.Data.Should().BeFalse();
        }
    }
    [Fact]
    public async Task RemoveWhiteListedNumbers_Should_Return200Ok_When_RemovalIsSuccessful()
    {
        // Arrange
        var id = Guid.NewGuid();
        var removals = new List<string> { "1234567890" };
        var response = new ApiResponse<bool>("Success", true);
        _configStoreService.RemoveWhiteListedNumbersFromAuthConfigAsync(id, removals, Arg.Any<CancellationToken>()).Returns(response);

        // Act
        CancellationToken cancellationToken = default;
        var result = await _controller.RemoveWhiteListedNumbers(id, removals,cancellationToken);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status200OK}");
            result.Message.Should().Be(AppResponses.SuccessfullyRemovedWhiteListedNumbers);
            result.Data.Should().BeTrue();
        }
    }

    [Fact]
    public async Task RemoveWhiteListedNumbers_Should_Return400BadRequest_When_RemovalFails()
    {
        // Arrange
        var id = Guid.NewGuid();
        var removals = new List<string> { "1234567890" };
        ApiResponse<bool>? response = null;
        _configStoreService.RemoveWhiteListedNumbersFromAuthConfigAsync(id, removals, Arg.Any<CancellationToken>()).Returns(response);

        // Act
        CancellationToken cancellationToken = default;
        var result = await _controller.RemoveWhiteListedNumbers(id, removals,cancellationToken);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
            result.Message.Should().Be(AppResponses.CouldNotUpdateWhiteList);
            result.Data.Should().BeFalse();
        }
    }

    [Fact]
    public async Task RemoveWhiteListedNumbers_Should_Return400BadRequest_When_ResponseIsNull()
    {
        // Arrange
        var id = Guid.NewGuid();
        var removals = new List<string> { "1234567890" };
        _configStoreService.RemoveWhiteListedNumbersFromAuthConfigAsync(id, removals, Arg.Any<CancellationToken>()).Returns((ApiResponse<bool>)null!);

        // Act
        CancellationToken cancellationToken = default;
        var result = await _controller.RemoveWhiteListedNumbers(id, removals,cancellationToken);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
            result.Message.Should().Be(AppResponses.CouldNotUpdateWhiteList);
            result.Data.Should().BeFalse();
        }
    }

    [Fact]
    public async Task AddWhiteListedNumbersToAuthConfig_Should_Return200Ok_When_AdditionIsSuccessful()
    {
        // Arrange
        var id = Guid.NewGuid();
        var whiteListedNumbers = new List<WhiteListedNumber>();
        var response = new ApiResponse<List<WhiteListedNumber>>("Success", whiteListedNumbers);
        _configStoreService.AddWhiteListedNumbersToAuthConfigAsync(id, whiteListedNumbers, Arg.Any<CancellationToken>()).Returns(response);

        // Act
        CancellationToken cancellationToken = default;
        var result = await _controller.AddWhiteListedNumbersToAuthConfig(id, whiteListedNumbers,cancellationToken);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status200OK}");
            result.Message.Should().Be(AppResponses.SuccessfullyAddedWhiteListedNumbers);
            result.Data.Should().BeEquivalentTo(whiteListedNumbers);
        }
    }

    [Fact]
    public async Task AddWhiteListedNumbersToAuthConfig_Should_Return400BadRequest_When_AdditionFails()
    {
        // Arrange
        var id = Guid.NewGuid();
        var whiteListedNumbers = new List<WhiteListedNumber>();
        _configStoreService.AddWhiteListedNumbersToAuthConfigAsync(id, whiteListedNumbers, Arg.Any<CancellationToken>()).Returns((ApiResponse<List<WhiteListedNumber>>)null!);

        // Act
        CancellationToken cancellationToken = default;
        var result = await _controller.AddWhiteListedNumbersToAuthConfig(id, whiteListedNumbers,cancellationToken);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
            result.Message.Should().Be(AppResponses.CouldNotUpdateWhiteList);
            result.Data.Should().BeEmpty();
        }
    }

    [Fact]
    public async Task CreateAppConfig_Should_Return200Ok_When_CreationIsSuccessful()
    {
        // Arrange
        var appConfig = new CreateAuthConfigStoreRequest();
        var response = new ApiResponse<GetBackOfficeAuthConfigStoreResponse>("Success", new GetBackOfficeAuthConfigStoreResponse())
        {
            Code = $"{StatusCodes.Status200OK}"
        };
        _configStoreService.AddAsync<GetBackOfficeAuthConfigStoreResponse>(appConfig).Returns(response);

        // Act
        var result = await _controller.CreateAppConfig(appConfig);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status200OK}");
            result.Message.Should().Be("Success");
            result.Data.Should().Be(response.Data);
        }
    }

    [Fact]
    public async Task CreateAppConfig_Should_Return400BadRequest_When_CreationFails()
    {
        // Arrange
        var appConfig = new CreateAuthConfigStoreRequest();
        var response = new ApiResponse<GetBackOfficeAuthConfigStoreResponse>("Failure", null!)
        {
            Code = $"{StatusCodes.Status400BadRequest}"
        };
        _configStoreService.AddAsync<GetBackOfficeAuthConfigStoreResponse>(appConfig).Returns(response);

        // Act
        var result = await _controller.CreateAppConfig(appConfig);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
            result.Message.Should().Be("Failure");
            result.Data.Should().BeNull();
        }
    }

    [Fact]
    public async Task DeleteAppConfig_Should_Return200Ok_When_DeletionIsSuccessful()
    {
        // Arrange
        var id = Guid.NewGuid();
        var response = new ApiResponse<bool>("Success", true)
        {
            Code= "200"
        };
        _configStoreService.DeleteAsync(id).Returns(response);

        // Act
        var result = await _controller.DeleteAppConfig(id);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status200OK}");
            result.Message.Should().Be("Success");
            result.Data.Should().BeTrue();
        }
    }

    [Fact]
    public async Task DeleteAppConfig_Should_Return400BadRequest_When_DeletionFails()
    {
        // Arrange
        var id = Guid.NewGuid();
        var response = new ApiResponse<bool>("Failure", false)
        {
            Code = $"{StatusCodes.Status400BadRequest}"
        };
        _configStoreService.DeleteAsync(id).Returns(response);

        // Act
        var result = await _controller.DeleteAppConfig(id);

        // Assert
        using (new FluentAssertions.Execution.AssertionScope())
        {
            result.Should().NotBeNull();
            result.Code.Should().Be($"{StatusCodes.Status400BadRequest}");
            result.Message.Should().Be("Failure");
            result.Data.Should().BeFalse();
        }
    }

}