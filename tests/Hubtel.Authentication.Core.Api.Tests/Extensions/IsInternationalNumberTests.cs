using FluentAssertions;
using Hubtel.Authentication.Core.Api.Extensions;

namespace Hubtel.Authentication.Core.Api.Tests.Extensions
{
    public class IsInternationalNumberTests
    {
        [Fact]
        public void IsInternationalNumber_Should_Return_False_When_Number_Starts_With_Plus233()
        {
            var number = "+2331234567890";

            var result = number.IsInternationalNumber();

            result.Should().BeFalse();
        }

        [Fact]
        public void IsInternationalNumber_Should_Return_False_When_Number_Starts_With_233()
        {
            var number = "2331234567890";

            var result = number.IsInternationalNumber();

            result.Should().BeFalse();
        }

        [Fact]
        public void IsInternationalNumber_Should_Return_True_When_Number_Does_Not_Start_With_Plus233_Or_233()
        {
            var number = "4561234567890";

            var result = number.IsInternationalNumber();

            result.Should().BeTrue();
        }
    }
}