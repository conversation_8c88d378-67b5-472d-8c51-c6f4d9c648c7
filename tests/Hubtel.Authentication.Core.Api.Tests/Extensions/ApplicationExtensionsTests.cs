using FluentAssertions;
using Hubtel.Authentication.Core.Api.Extensions;
using Hubtel.Authentication.Core.Api.Models.Responses;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hubtel.Authentication.Core.Api.Tests.Extensions
{
    public class ApplicationExtensionsTests
    {
        [Fact]
        public void GetEmailOnly_TokenData_ShouldReturnEmailOnly_WhenEmailExists_EmailLookupDataIsProvided()
        {
            // Arrange
            var emailLookupData = new EmailLookUpData
            {
                PhoneNumbers = new List<PhoneNumber>
                {
                    new PhoneNumber { CountryCode = "233", Number = "123456789" }
                },
                TokenData = new Dictionary<string, string>
                {
                    { "email", "<EMAIL>" },
                    { "name", "John Doe" },
                    { "music","Rock" }
                }
            };
            // Act
            var result = emailLookupData.FilterForEmailOnly();

            // Assert
            result.Should().BeOfType<EmailLookUpData>();
            result.TokenData.Should().ContainKey("email").WhoseValue.Should().Be("<EMAIL>");
            result.TokenData.Count.Should().Be(1);
        }



        [Fact]
        public void GetEmailOnly_TokenData_ShouldReturnEmpty_WhenEmailIsNotInTokenData_EmailLookupDataIsProvided()
        {
            // Arrange
            var emailLookupData = new EmailLookUpData
            {
                PhoneNumbers = new List<PhoneNumber>
                {
                    new PhoneNumber { CountryCode = "233", Number = "123456789" }
                },
                TokenData = new Dictionary<string, string>
                {
                    { "name", "John Doe" },
                    { "music","Rock" }
                }
            };
            // Act
            var result = emailLookupData.FilterForEmailOnly();
            // Assert
            result.Should().BeOfType<EmailLookUpData>();
            result.PhoneNumbers.Count().Should().Be(1);
            result.TokenData.Count.Should().Be(0);

        }
        [Fact]
        public void GetEmailOnly_TokenData_ShouldReturnEmailOnly_WhenEmailExists_PhoneNumberLookupDataIsProvided()
        {
            // Arrange
            var phoneNumberLookupData = new PhoneNumberLookupData
            {
                MobileNumber = "233123456789",
                Email = "<EMAIL>",
                TokenData = new Dictionary<string, string>
                {
                    { "email", "<EMAIL>" },
                    { "name", "John Doe" },
                    { "music","Rock" }
                }
            };
            // Act
            var result = phoneNumberLookupData.FilterForEmailOnly();

            // Assert
            result.Should().BeOfType<PhoneNumberLookupData>();
            result.TokenData.Should().ContainKey("email").WhoseValue.Should().Be("<EMAIL>");
            result.TokenData.Count.Should().Be(1);

        }
        [Fact]
        public void GetEmailOnly_TokenData_ShouldReturnEmpty_WhenEmailIsNotInTokenData_PhoneNumberLookupDataIsProvided()
        {
            // Arrange
            var baseLookupData = new PhoneNumberLookupData
            {
                MobileNumber = "233123456789",
                Email = "<EMAIL>",
                TokenData = new Dictionary<string, string>
                {
                    { "name", "John Doe" },
                    { "music","Rock" }
                }
            };
            // Act
            var result = baseLookupData.FilterForEmailOnly();
            // Assert
            result.Should().BeOfType<PhoneNumberLookupData>();
            result.MobileNumber.Should().Be("233123456789");
            result.TokenData.Count.Should().Be(0);
        }

        [Fact]
        public void GetEmailOnly_TokenData_ShouldReturnEmpty_WhenTokenDataIsEmpty()
        {
            // Arrange
            var baseLookupData = new PhoneNumberLookupData
            {
                MobileNumber = "233123456789",
                Email = "<EMAIL>",
            };
            // Act
            var result = baseLookupData.FilterForEmailOnly();
            // Assert
            result.Should().BeOfType<PhoneNumberLookupData>();
            result.MobileNumber.Should().Be("233123456789");
            result.TokenData.Count.Should().Be(0);
        }
        [Fact]
        public void GetEmailOnly_TokenData_ShouldReturnNull_WhenBaseLookupDataIsNull()
        {
            // Arrange
            PhoneNumberLookupData? baseLookupData = null;
            // Act
            var result = baseLookupData.FilterForEmailOnly();
            // Assert
            result.Should().BeNull();
        }
        [Fact]
        public void GetEmailOnly_TokenData_ShouldReturnNull_WhenBaseLookupDataIsNull_EmailLookupDataIsProvided()
        {
            // Arrange
            var baseLookupData = new PhoneNumberLookupData
            {
                MobileNumber = "233123456789",
                Email = "<EMAIL>",
                TokenData = null
            };
            // Act
            var result = baseLookupData.FilterForEmailOnly();
            // Assert
            result.Should().NotBeNull();
            result.Should().BeOfType<PhoneNumberLookupData>();
            result.MobileNumber.Should().Be("233123456789");
            result.Email.Should().Be("<EMAIL>");
            result.TokenData.Should().BeNull();
        }
    }
}
