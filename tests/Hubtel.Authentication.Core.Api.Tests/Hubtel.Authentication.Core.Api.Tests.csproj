<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>

        <IsPackable>false</IsPackable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="FluentAssertions" Version="6.12.2" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="8.0.14" />
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.12.0" />
        <PackageReference Include="MockQueryable.NSubstitute" Version="7.0.3" />
        <PackageReference Include="NSubstitute" Version="5.3.0" />
        <PackageReference Include="System.Net.Http" Version="4.3.4" />
        <PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
        <PackageReference Include="xunit" Version="2.9.2" />
		
        <PackageReference Include="xunit.runner.visualstudio" Version="2.8.2">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="coverlet.collector" Version="6.0.2">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\src\Hubtel.Authentication.Core.Api\Hubtel.Authentication.Core.Api.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Components\" />
    </ItemGroup>
	<ItemGroup>
		<None Update="wwwroot\**">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>

</Project>
