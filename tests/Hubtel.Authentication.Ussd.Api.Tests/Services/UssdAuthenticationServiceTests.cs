using FluentAssertions;
using Hubtel.Authentication.Commons.Extensions;
using Hubtel.Authentication.Commons.Models;
using Hubtel.Authentication.Commons.Services;
using Hubtel.Authentication.Ussd.Api.Options;
using Hubtel.Redis.Sdk.Services;
using Microsoft.Extensions.Logging;
using NSubstitute;
using StackExchange.Redis;
using Flurl.Http;
using Flurl.Http.Testing;
using Newtonsoft.Json;
using Hubtel.Authentication.Ussd.Api.Models;

namespace Hubtel.Authentication.Ussd.Api.Services.Tests
{
    public class UssdAuthenticationServiceTests : IDisposable
    {
        private readonly HttpTest _httpTest;
        private readonly ILogger<UssdAuthenticationService> _logger;
        private readonly INotificationService _notificationService;
        private readonly FraudulentEmailsConfig _fraudulentEmailsConfig;
        private readonly Endpoints _externalEndpoints;
        private readonly IDatabase _otpCacheRepository;
        private readonly UssdAuthenticationService _service;
        private bool disposedValue;

        public UssdAuthenticationServiceTests()
        {
            _httpTest = new HttpTest();
            _logger = Substitute.For<ILogger<UssdAuthenticationService>>();
            _notificationService = Substitute.For<INotificationService>();
            _fraudulentEmailsConfig = new FraudulentEmailsConfig
            {
                NotificationEmails = new List<string> { "<EMAIL>" },
                Topic = "test-topic"
            };
            _externalEndpoints = new Endpoints
            {
                AuthenticationAPIUrl = "https://api.example.com",
                AuthenticationAPIKey = "api-key",
                AuthenticationAPISecret = "api-secret"
            };
            var multiRedisHostCacheRepository = Substitute.For<IMultiRedisHostCacheRepository>();
            _otpCacheRepository = Substitute.For<IDatabase>();
            multiRedisHostCacheRepository.GetDb(RedisConstants.MainRedisHostName, RedisConstants.DefaultOtpCacheDb).Returns(_otpCacheRepository);

            _service = new UssdAuthenticationService(_logger, multiRedisHostCacheRepository,
                Microsoft.Extensions.Options.Options.Create(_externalEndpoints), _notificationService, Microsoft.Extensions.Options.Options.Create(_fraudulentEmailsConfig));
        }

        [Fact]
        public async Task FindLoginAttempt_Should_ReturnOtp_When_ValidPhoneNumber()
        {
            // Arrange
            var phoneNumber = "1234567890";
            var otp = new ExtendedOtp { CreatedAt = DateTime.UtcNow, OtpCode = "1234" };
            var listOfOtp = new List<ExtendedOtp>() { otp };
            var redisValues = listOfOtp.Select(otp => (RedisValue)JsonConvert.SerializeObject(otp)).ToArray();
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);

            // Act
            var result = await _service.FindLoginAttempt(phoneNumber);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Should().NotBeNull();
                result.OtpCode.Should().Be("1234");
            }
        }

        [Fact]
        public async Task FindLoginAttempt_Should_ReturnNull_When_OtpExpired()
        {
            // Arrange
            var phoneNumber = "1234567890";
            var otp = new ExtendedOtp { CreatedAt = DateTime.UtcNow.AddMinutes(-10), OtpCode = "1234" };
            var listOfOtp = new List<ExtendedOtp>() { otp };
            var redisValues = listOfOtp.Select(otp => (RedisValue)JsonConvert.SerializeObject(otp)).ToArray();
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);

            // Act
            var result = await _service.FindLoginAttempt(phoneNumber);

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public async Task ValidateUserOtp_Should_ReturnTrue_When_OtpIsValid()
        {
            // Arrange
            var phoneNumber = "1234567890";
            var otp = new ExtendedOtp { CreatedAt = DateTime.UtcNow, OtpCode = "OTP-1234", Prefix = "OTP", RequestId = "req-123" };
            var listOfOtp = new List<ExtendedOtp>() { otp };
            var redisValues = listOfOtp.Select(otp => (RedisValue)JsonConvert.SerializeObject(otp)).ToArray();
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);
            // Act
            var (isValid, requestId) = await _service.ValidateUserOtp(phoneNumber, "1234");

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                isValid.Should().BeTrue();
                requestId.Should().Be("req-123");
            }
        }

        [Fact]
        public async Task ValidateUserOtp_Should_ReturnFalse_When_OtpIsInvalid()
        {
            // Arrange
            var phoneNumber = "1234567890";
            var listOfOtp = new List<ExtendedOtp>() { };
            var redisValues = listOfOtp.Select(otp => (RedisValue)JsonConvert.SerializeObject(otp)).ToArray();
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);

            // Act
            var (isValid, requestId) = await _service.ValidateUserOtp(phoneNumber, "5678");

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                isValid.Should().BeFalse();
                requestId.Should().BeEmpty();
            }
        }

        [Fact]
        public async Task StoreSuccessfulLogin_Should_StoreLogin_When_ValidRequest()
        {
            // Arrange
            var phoneNumber = "1234567890";
            var requestId = "req-123";

            // Act
            await _service.StoreSuccessfulLogin(phoneNumber, requestId);

            // Assert
            await _otpCacheRepository.Received(1).StringSetAsync($"{CommonConstants.UssdVerified}:{phoneNumber}:{requestId}", phoneNumber, TimeSpan.FromMinutes(5));
        }

        [Fact]
        public async Task PostSuccessfulCallback_Should_LogDebug_When_Successful()
        {
            // Arrange
            var phoneNumber = "1234567890";
            var requestId = "req-123";
            var otp = new ExtendedOtp { CreatedAt = DateTime.UtcNow, OtpCode = "1234", AppId = Guid.NewGuid() };
            var listOfOtp = new List<ExtendedOtp>() { otp };
            var redisValues = listOfOtp.Select(otp => (RedisValue)JsonConvert.SerializeObject(otp)).ToArray();
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);

            // Act
            await _service.PostSuccessfulCallback(phoneNumber, requestId);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                _logger.Received().Log<object>(
                    LogLevel.Debug,
                    0,
                    Arg.Is<object>(o => o.ToString()!.Contains("Response from successful API call")),
                    null,
                    Arg.Any<Func<object, Exception, string>>()!);
            }
        }

        [Fact]
        public async Task PostSuccessfulCallback_Should_LogError_When_FlurlHttpException()
        {
            // Arrange
            var phoneNumber = "1234567890";
            var requestId = "req-123";
            var otp = new ExtendedOtp { CreatedAt = DateTime.UtcNow, OtpCode = "1234", AppId = Guid.NewGuid() };
            var listOfOtp = new List<ExtendedOtp>() { otp };
            var redisValues = listOfOtp.Select(otp => (RedisValue)JsonConvert.SerializeObject(otp)).ToArray();
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);

            _httpTest.RespondWith("some error", 500);
            // Act
            await _service.PostSuccessfulCallback(phoneNumber, requestId);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                _logger.Received().Log<object>(
                                      LogLevel.Error,
                                      0,
                                      Arg.Is<object>(o => o.ToString()!.Contains("Error sending success response")),
                                      Arg.Any<FlurlHttpException>(),
                                      Arg.Any<Func<object, Exception, string>>()!);
            }
        }

        [Fact]
        public async Task PostFailedCallback_Should_LogDebug_When_Successful()
        {
            // Arrange
            var phoneNumber = "1234567890";
            var resObject = new ApiResponse<EmptyDto>();
            // Act

            var response = _httpTest.RespondWithJson(resObject, 200);
            await _service.PostFailedCallback(phoneNumber);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                _logger.Received().Log<object>(
                    LogLevel.Debug,
                    0,
                    Arg.Is<object>(o => o.ToString()!.Contains("Response from successful API call")),
                    null,
                    Arg.Any<Func<object, Exception, string>>()!);
            }
        }

        [Fact]
        public async Task PostFailedCallback_Should_LogError_When_FlurlHttpException()
        {
            // Arrange
            var phoneNumber = "1234567890";
            _httpTest.RespondWith("some error", 500);
            // Act
            await _service.PostFailedCallback(phoneNumber);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                        _logger.Received().Log<object>(
                                     LogLevel.Error,
                                     0,
                                     Arg.Is<object>(o => o.ToString()!.Contains("Error sending success response")),
                                     Arg.Any<FlurlHttpException>(),
                                     Arg.Any<Func<object, Exception, string>>()!);

            }
        }

        [Fact]
        public async Task ReportLogin_Should_SendNotification_When_OtpFound()
        {
            // Arrange
            var phoneNumber = "1234567890";
            var otp = new ExtendedOtp { CreatedAt = DateTime.UtcNow, OtpCode = "1234", Msisdn = phoneNumber, DeviceName = "Device", Location = "Location" };
            var listOfOtp = new List<ExtendedOtp>() { otp };
            var redisValues = listOfOtp.Select(otp => (RedisValue)JsonConvert.SerializeObject(otp)).ToArray();
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);

            // Act
            await _service.ReportLogin(phoneNumber);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                await _notificationService.Received(1).SendNotification(_fraudulentEmailsConfig.Topic, Arg.Any<Notification>());
            }
        }

        [Fact]
        public async Task ReportLogin_Should_LogError_When_OtpNotFound()
        {
            // Arrange
            var phoneNumber = "1234567890";
            var listOfOtp = new List<ExtendedOtp>() { };
            var redisValues = listOfOtp.Select(otp => (RedisValue)JsonConvert.SerializeObject(otp)).ToArray();
            _otpCacheRepository.SortedSetRangeByScoreAsync(Arg.Any<RedisKey>(), Arg.Any<double>(), Arg.Any<double>(), order: Order.Descending)
                .Returns(redisValues);

            // Act
            await _service.ReportLogin(phoneNumber);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                _logger.Received().Log<object>(
                    LogLevel.Error,
                    0,
                    Arg.Is<object>(o => o.ToString()!.Contains("Login attempt not found for user")),
                    null,
                    Arg.Any<Func<object, Exception, string>>()!);
            }
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!disposedValue)
            {
                if (disposing)
                {
                    _httpTest.Dispose();
                }

                disposedValue = true;
            }
        }

        public void Dispose()
        {
            // Do not change this code. Put cleanup code in 'Dispose(bool disposing)' method
            Dispose(disposing: true);
            GC.SuppressFinalize(this);
        }
    }
}