using Confluent.Kafka;
using Flurl.Http;
using Hubtel.Authentication.Commons.Models;
using Hubtel.Kafka.Producer.Sdk.Services;
using Microsoft.Extensions.Logging;
using NSubstitute;
using NSubstitute.ExceptionExtensions;

namespace Hubtel.Authentication.Commons.Services.Tests
{
    public class NotificationServiceTests
    {
        private readonly IKafkaProducer _producer;
        private readonly ILogger<NotificationService> _logger;
        private readonly NotificationService _notificationService;

        public NotificationServiceTests()
        {
            var producerFactory = Substitute.For<IKafkaProducerFactory>();
            _producer = Substitute.For<IKafkaProducer>();
            producerFactory.CreateKafkaProducer(Arg.Any<string>()).Returns(_producer);
            _logger = Substitute.For<ILogger<NotificationService>>();
            _notificationService = new NotificationService(producerFactory, _logger);
        }

        [Fact]
        public async Task SendNotification_Should_LogDebug_When_NotificationIsPersisted()
        {
            // Arrange
            var topic = "test-topic";
            var notification = new Notification { Destination = "test-destination", TemplateId = "test-template" };
            var deliveryResult = new DeliveryResult<Null, string> { Status = PersistenceStatus.Persisted };
            _producer.ProduceAsync(Arg.Any<string>(), Arg.Any<string>()).Returns(deliveryResult);

            // Act
            await _notificationService.SendNotification(topic, notification);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                _logger.Received(1).Log(
                    LogLevel.Debug,
                    Arg.Any<EventId>(),
                    Arg.Is<object>(o => o.ToString()!.Contains("Response from sending notification")),
                    Arg.Any<Exception>(),
                    Arg.Any<Func<object, Exception, string>>()!);
            }
        }

        [Fact]
        public async Task SendNotification_Should_LogError_When_NotificationIsNotPersisted()
        {
            // Arrange
            var topic = "test-topic";
            var notification = new Notification { Destination = "test-destination", TemplateId = "test-template" };
            var deliveryResult = new DeliveryResult<Null, string> { Status = PersistenceStatus.NotPersisted };
            _producer.ProduceAsync(Arg.Any<string>(), Arg.Any<string>()).Returns(deliveryResult);

            // Act
            await _notificationService.SendNotification(topic, notification);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                _logger.Received(1).Log(
                    LogLevel.Error,
                    Arg.Any<EventId>(),
                    Arg.Is<object>(o => o.ToString()!.Contains("Response from sending notification")),
                    Arg.Any<Exception>(),
                    Arg.Any<Func<object, Exception, string>>()!);
            }
        }

        [Fact]
        public async Task SendNotification_Should_LogError_When_ExceptionIsThrown()
        {
            // Arrange
            var topic = "test-topic";
            var notification = new Notification { Destination = "test-destination", TemplateId = "test-template" };
            _producer.ProduceAsync(Arg.Any<string>(), Arg.Any<string>()).Throws(new Exception("Kafka error"));

            // Act
            await _notificationService.SendNotification(topic, notification);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
               
                _logger.Received().Log<object>(
                                     LogLevel.Error,
                                     0,
                                     Arg.Is<object>(o => o.ToString()!.Contains("An error occured sending notification")),
                                     Arg.Any<Exception>(),
                                     Arg.Any<Func<object, Exception, string>>()!);
            }
        }
    }
}
