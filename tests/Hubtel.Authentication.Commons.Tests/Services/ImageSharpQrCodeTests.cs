using System;
using FluentAssertions;
using QRCoder;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;
using Xunit;

namespace Hubtel.Authentication.Commons.Services.QrCode.Tests
{
    public class ImageSharpQrCodeTests
    {
        private static QRCodeData CreateTestQrCodeData()
        {
            var generator = new QRCodeGenerator();
            return generator.CreateQrCode("Test QR Data", QRCodeGenerator.ECCLevel.Q);
        }

        [Fact]
        public void GetGraphic_Should_RenderImage_When_ValidParametersProvided()
        {
            // Arrange
            var qrCodeData = CreateTestQrCodeData();
            var qrCode = new ImageSharpQrCode(qrCodeData);
            var darkColor = Color.Black;
            var lightColor = Color.White;
            var backgroundColor = Color.Gray;
            var pixelsPerModule = 10;

            // Act
            var image = qrCode.GetGraphic(pixelsPerModule, darkColor, lightColor, backgroundColor);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                image.Should().NotBeNull();
                image.Width.Should().BeGreaterThan(0);
                image.Height.Should().BeGreaterThan(0);
            }
        }

        [Fact]
        public void GetGraphic_Should_RenderImageWithLogo_When_LogoIsProvided()
        {
            // Arrange
            var qrCodeData = CreateTestQrCodeData();
            var qrCode = new ImageSharpQrCode(qrCodeData);
            var logoImage = new Image<Rgba32>(50, 50);

            // Act
            var image = qrCode.GetGraphic(10, Color.Black, Color.White, Color.Transparent, logoImage);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                image.Should().NotBeNull();
                image.Width.Should().BeGreaterThan(0);
                image.Height.Should().BeGreaterThan(0);
            }
        }

        //[Fact]
        //public void GetGraphic_Should_ThrowArgumentException_When_PixelSizeFactorIsGreaterThanOne()
        //{
        //    // Arrange
        //    var qrCodeData = CreateTestQrCodeData();
        //    var qrCode = new ImageSharpQrCode(qrCodeData);

        //    // Act
        //    Action act = () => qrCode.GetGraphic(10, Color.Black, Color.White, Color.Transparent, null, LogoLocation.BottomRight, LogoBackgroundShape.Circle, 1.1);

        //    // Assert
        //    act.Should().Throw<ArgumentException>().WithMessage("The parameter pixelSize must be between 0 and 1. (0-100%)");
        //}

        [Fact]
        public void GetGraphic_Should_IncludeQuietZones_When_Enabled()
        {
            // Arrange
            var qrCodeData = CreateTestQrCodeData();
            var qrCode = new ImageSharpQrCode(qrCodeData);

            // Act
            var image = qrCode.GetGraphic(10, Color.Black, Color.White, Color.Transparent);

            // Assert
            image.Should().NotBeNull();
            image.Width.Should().BeGreaterThan(0);
            image.Height.Should().BeGreaterThan(0);
        }

        [Fact]
        public void GetGraphic_Should_RenderImage_When_DefaultParametersUsed()
        {
            // Arrange
            var qrCodeData = CreateTestQrCodeData();
            var qrCode = new ImageSharpQrCode(qrCodeData);

            // Act
            var image = qrCode.GetGraphic();

            // Assert
            image.Should().NotBeNull();
            image.Width.Should().BeGreaterThan(0);
            image.Height.Should().BeGreaterThan(0);
        }

        [Fact]
        public void GetGraphic_Should_PlaceLogoAtBottomRight_When_LocationIsBottomRight()
        {
            // Arrange
            var qrCodeData = CreateTestQrCodeData();
            var qrCode = new ImageSharpQrCode(qrCodeData);
            var logoImage = new Image<Rgba32>(50, 50);

            // Act
            var image = qrCode.GetGraphic(10, Color.Black, Color.White, Color.Transparent, logoImage, LogoLocation.BottomRight);

            // Assert
            image.Should().NotBeNull();
        }

        [Fact]
        public void GetGraphic_Should_PlaceLogoAtCenter_When_LocationIsCenter()
        {
            // Arrange
            var qrCodeData = CreateTestQrCodeData();
            var qrCode = new ImageSharpQrCode(qrCodeData);
            var logoImage = new Image<Rgba32>(50, 50);

            // Act
            var image = qrCode.GetGraphic(10, Color.Black, Color.White, Color.Transparent, logoImage, LogoLocation.Center);

            // Assert
            image.Should().NotBeNull();
        }

        [Fact]
        public void GetGraphic_Should_DrawLogoWithRectangleBackground_When_LogoBackgroundShapeIsRectangle()
        {
            // Arrange
            var qrCodeData = CreateTestQrCodeData();
            var qrCode = new ImageSharpQrCode(qrCodeData);
            var logoImage = new Image<Rgba32>(50, 50);

            // Act
            var image = qrCode.GetGraphic(10, Color.Black, Color.White, Color.Transparent, logoImage, LogoLocation.Center, LogoBackgroundShape.Rectangle);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                image.Should().NotBeNull();
                image.Width.Should().BeGreaterThan(0);
                image.Height.Should().BeGreaterThan(0);
            }
        }

        [Fact]
        public void GetGraphic_Should_DrawLogoWithCircleBackground_When_LogoBackgroundShapeIsCircle()
        {
            // Arrange
            var qrCodeData = CreateTestQrCodeData();
            var qrCode = new ImageSharpQrCode(qrCodeData);
            var logoImage = new Image<Rgba32>(50, 50);

            // Act
            var image = qrCode.GetGraphic(10, Color.Black, Color.White, Color.Transparent, logoImage, LogoLocation.Center, LogoBackgroundShape.Circle);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                image.Should().NotBeNull();
                image.Width.Should().BeGreaterThan(0);
                image.Height.Should().BeGreaterThan(0);
            }
        }

        [Fact]
        public void GetGraphic_Should_RenderImageWithCustomColors_When_ColorsAreProvided()
        {
            // Arrange
            var qrCodeData = CreateTestQrCodeData();
            var qrCode = new ImageSharpQrCode(qrCodeData);
            var darkColor = Color.Red;
            var lightColor = Color.Blue;
            var backgroundColor = Color.Green;

            // Act
            var image = qrCode.GetGraphic(10, darkColor, lightColor, backgroundColor);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                image.Should().NotBeNull();
                image.Width.Should().BeGreaterThan(0);
                image.Height.Should().BeGreaterThan(0);
            }
        }

        [Fact]
        public void GetGraphic_Should_RenderImageWithDifferentPixelSize_When_PixelSizeIsChanged()
        {
            // Arrange
            var qrCodeData = CreateTestQrCodeData();
            var qrCode = new ImageSharpQrCode(qrCodeData);

            // Act
            var image = qrCode.GetGraphic(20, Color.Black, Color.White, Color.Transparent);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                image.Should().NotBeNull();
                image.Width.Should().BeGreaterThan(0);
                image.Height.Should().BeGreaterThan(0);
            }
        }

        [Fact]
        public void GetGraphic_Should_RenderImageWithDifferentLogoSize_When_LogoSizeIsChanged()
        {
            // Arrange
            var qrCodeData = CreateTestQrCodeData();
            var qrCode = new ImageSharpQrCode(qrCodeData);
            var logoImage = new Image<Rgba32>(100, 100);

            // Act
            var image = qrCode.GetGraphic(10, Color.Black, Color.White, Color.Transparent, logoImage, LogoLocation.Center, LogoBackgroundShape.Circle);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                image.Should().NotBeNull();
                image.Width.Should().BeGreaterThan(0);
                image.Height.Should().BeGreaterThan(0);
            }
        }

        [Fact]
        public void Constructor_Should_CreateInstance_When_DefaultConstructorIsUsed()
        {
            // Act
            var qrCode = new ImageSharpQrCode();

            // Assert
            qrCode.Should().NotBeNull();
        }
        [Fact]
        public void GetGraphic_Should_RenderImageWithDefaultParameters_When_CalledWithDefaultParameters()
        {
            // Arrange
            var qrCodeData = CreateTestQrCodeData();
            var qrCode = new ImageSharpQrCode(qrCodeData);

            // Act
            var image = qrCode.GetGraphic(10, null, LogoLocation.BottomRight, LogoBackgroundShape.Rectangle);

            // Assert
            using (new FluentAssertions.Execution.AssertionScope())
            {
                image.Should().NotBeNull();
                image.Width.Should().BeGreaterThan(0);
                image.Height.Should().BeGreaterThan(0);
            }
        }
    }
}