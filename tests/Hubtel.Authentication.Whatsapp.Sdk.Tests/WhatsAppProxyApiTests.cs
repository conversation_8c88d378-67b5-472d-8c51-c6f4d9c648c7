using System.Text.Json;
using FluentAssertions;
using Flurl.Http.Testing;
using Hubtel.Authentication.Whatsapp.Sdk.Client;
using Hubtel.Authentication.Whatsapp.Sdk.Models;
using Hubtel.Authentication.Whatsapp.Sdk.Options;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute;
using Xunit;

namespace Hubtel.Authentication.Whatsapp.Sdk.Tests
{
    public class WhatsAppProxyApiTests
    {
        private readonly ILogger<WhatsAppProxyApi> _logger = Substitute.For<ILogger<WhatsAppProxyApi>>();
        private readonly IOptions<WhatsAppProxyConfig> _proxyConfig = Microsoft.Extensions.Options.Options.Create(new WhatsAppProxyConfig
        {
            BaseUrl = "http://test.com",
            AuthorisationKey = "YourAuthorizationKey",
            CallBackUrl = "http://callback.com"
        });
        private readonly WhatsAppProxyApi _api;
        private readonly HttpTest _httpTest;

        public WhatsAppProxyApiTests()
        {
            _api = new WhatsAppProxyApi(_proxyConfig, _logger);
            _httpTest = new HttpTest();
        }

        private static JsonSerializerOptions _defaultOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true
        };

        [Fact]
        public async Task SendMessage_Should_ReturnSuccessfulResponseAndApiResponse_When_MessageIsSentSuccessfully()
        {
            var message = new WhatsAppMessage
            {
                To = "Test",
                Content = "Test",
                Channel = "Test",
                CallbackUrl = "Test",
                ExtraData = new Dictionary<string, string>
                {
                    { WhatsAppExtraData.Properties.Type, WhatsAppExtraData.Values.Type.MessageWithHeader },
                    { WhatsAppExtraData.Properties.SenderId,"Hubtel" }
                }
            };

            // Use HttpTest to simulate a successful response
            var expectedResponse = new WhatsAppApiResponse
            {
                Rate = 0.00,
                Status = 0,
                ClientReference = "some reference",
                MessageId = "some Message Id",
                NetworkId = "some Network Id",
                StatusDescription = "StatusDescription"
            };

            var expectedResponseJson = JsonSerializer.Serialize(expectedResponse, _defaultOptions);
            _httpTest.RespondWith(expectedResponseJson, 200);

            var (result, apiResponse, error) = await _api.SendMessage(message);

            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Should().BeTrue();
                apiResponse.Should().NotBeNull();
                error.Should().Be(string.Empty);
                apiResponse.Should().BeEquivalentTo(expectedResponse);
            }
        }

        [Fact]
        public async Task SendMessage_Should_SetCallbackUrl_When_CallbackUrlIsNullOrWhitespace()
        {
            var message = new WhatsAppMessage
            {
                To = "Test",
                Content = "Test",
                Channel = "Test",
                CallbackUrl = "",
                ExtraData = new Dictionary<string, string>
                {
                    { WhatsAppExtraData.Properties.Type, WhatsAppExtraData.Values.Type.MessageWithHeader },
                    { WhatsAppExtraData.Properties.SenderId,"Hubtel" }
                }
            };

            // Use HttpTest to simulate a successful response
            var expectedResponse = new WhatsAppApiResponse
            {
                Rate = 0.00,
                Status = 0,
                ClientReference = "some reference",
                MessageId = "some Message Id",
                NetworkId = "some Network Id",
                StatusDescription = "StatusDescription"
            };

            var expectedResponseJson = JsonSerializer.Serialize(expectedResponse, _defaultOptions);
            _httpTest.RespondWith(expectedResponseJson, 200);

            var (result, apiResponse, error) = await _api.SendMessage(message);

            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Should().BeTrue();
                apiResponse.Should().NotBeNull();
                error.Should().Be(string.Empty);
                apiResponse.Should().BeEquivalentTo(expectedResponse);
                message.CallbackUrl.Should().Be(_proxyConfig.Value.CallBackUrl);
            }
        }

        [Fact]
        public async Task SendMessage_Should_ReturnErrorResponseAndNullApiResponse_When_MessageSendingFailsWithBadRequest()
        {
            var message = new WhatsAppMessage
            {
                To = "Test",
                Content = "Test",
                Channel = "Test",
                CallbackUrl = "Test",
                ExtraData = new Dictionary<string, string>
                {
                    { WhatsAppExtraData.Properties.Type, WhatsAppExtraData.Values.Type.MessageWithHeader },
                    { WhatsAppExtraData.Properties.SenderId,"Test" }
                }
            };

            // Use HttpTest to simulate a BadRequest response
            _httpTest.RespondWith("Bad request", 400);

            var (result, apiResponse, error) = await _api.SendMessage(message);

            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Should().BeFalse();
                apiResponse.Should().BeNull();
                error.Should().NotBe(string.Empty);
            }
        }

        [Fact]
        public async Task SendMessage_Should_ReturnErrorAndExceptionMessage_When_ExceptionOccursWhileSendingMessage()
        {
            var message = new WhatsAppMessage
            {
                To = "Test",
                Content = "Test",
                Channel = "Test",
                CallbackUrl = "Test",
                ExtraData = new Dictionary<string, string>
                {
                    { WhatsAppExtraData.Properties.Type, WhatsAppExtraData.Values.Type.MessageWithHeader },
                    { WhatsAppExtraData.Properties.SenderId,"Test" }
                }
            };

            // Use HttpTest to simulate an error response
            _httpTest.RespondWith("Internal Server Error", 500);
            var (result, apiResponse, error) = await _api.SendMessage(message);
            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Should().BeFalse();
                apiResponse.Should().BeNull();
                error.Should().NotBe(null);
            }
        }

        [Fact]
        public async Task SendMessage_Should_ReturnErrorAndNullApiResponse_When_Unauthorized()
        {
            var message = new WhatsAppMessage
            {
                To = "Test",
                Content = "Test",
                Channel = "Test",
                CallbackUrl = "Test",
                ExtraData = new Dictionary<string, string>
                {
                    { WhatsAppExtraData.Properties.Type, WhatsAppExtraData.Values.Type.MessageWithHeader },
                    { WhatsAppExtraData.Properties.SenderId,"Test" }
                }
            };

            // Use HttpTest to simulate an Unauthorized response
            _httpTest.RespondWith("Unauthorized", 401);

            var (result, apiResponse, error) = await _api.SendMessage(message);

            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Should().BeFalse();
                apiResponse.Should().BeNull();
                error.Should().NotBe(string.Empty);
            }
        }

        [Fact]
        public async Task SendMessage_Should_ReturnErrorAndNullApiResponse_When_Forbidden()
        {
            var message = new WhatsAppMessage
            {
                To = "Test",
                Content = "Test",
                Channel = "Test",
                CallbackUrl = "Test",
                ExtraData = new Dictionary<string, string>
                {
                    { WhatsAppExtraData.Properties.Type, WhatsAppExtraData.Values.Type.MessageWithHeader },
                    { WhatsAppExtraData.Properties.SenderId,"Test" }
                }
            };

            // Use HttpTest to simulate a Forbidden response
            _httpTest.RespondWith("Forbidden", 403);

            var (result, apiResponse, error) = await _api.SendMessage(message);

            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Should().BeFalse();
                apiResponse.Should().BeNull();
                error.Should().NotBe(string.Empty);
            }
        }

        [Fact]
        public async Task SendMessage_Should_ReturnErrorAndNullApiResponse_When_NotFound()
        {
            var message = new WhatsAppMessage
            {
                To = "Test",
                Content = "Test",
                Channel = "Test",
                CallbackUrl = "Test",
                ExtraData = new Dictionary<string, string>
                {
                    { WhatsAppExtraData.Properties.Type, WhatsAppExtraData.Values.Type.MessageWithHeader },
                    { WhatsAppExtraData.Properties.SenderId,"Test" }
                }
            };

            // Use HttpTest to simulate a NotFound response
            _httpTest.RespondWith("Not Found", 404);

            var (result, apiResponse, error) = await _api.SendMessage(message);

            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Should().BeFalse();
                apiResponse.Should().BeNull();
                error.Should().NotBe(string.Empty);
            }
        }

        [Fact]
        public async Task SendMessage_Should_ReturnErrorAndNullApiResponse_When_ServiceUnavailable()
        {
            var message = new WhatsAppMessage
            {
                To = "Test",
                Content = "Test",
                Channel = "Test",
                CallbackUrl = "Test",
                ExtraData = new Dictionary<string, string>
                {
                    { WhatsAppExtraData.Properties.Type, WhatsAppExtraData.Values.Type.MessageWithHeader },
                    { WhatsAppExtraData.Properties.SenderId,"Test" }
                }
            };

            // Use HttpTest to simulate a ServiceUnavailable response
            _httpTest.RespondWith("Service Unavailable", 503);

            var (result, apiResponse, error) = await _api.SendMessage(message);

            using (new FluentAssertions.Execution.AssertionScope())
            {
                result.Should().BeFalse();
                apiResponse.Should().BeNull();
                error.Should().NotBe(string.Empty);
            }
        }
    }
}

