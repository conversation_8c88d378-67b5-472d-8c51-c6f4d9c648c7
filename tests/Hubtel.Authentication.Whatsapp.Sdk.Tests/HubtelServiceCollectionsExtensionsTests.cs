using FluentAssertions;
using Hubtel.Authentication.Whatsapp.Sdk.Client;
using Hubtel.Authentication.Whatsapp.Sdk.Extensions;
using Hubtel.Authentication.Whatsapp.Sdk.Options;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;

namespace Hubtel.Authentication.Whatsapp.Sdk.Tests;

public class HubtelServiceCollectionsExtensionsTests
{
    [Fact]
    public void AddWhatsAppProxyProxySdk_Should_ThrowArgumentNullException_When_ServicesIsNull()
    {
#pragma warning disable CS8600 // Converting null literal or possible null value to non-nullable type.
        IServiceCollection services = null;
#pragma warning restore CS8600 // Converting null literal or possible null value to non-nullable type.
        Action<WhatsAppProxyConfig> configure = config =>
        {
            config.BaseUrl = "https://example.com";
            config.AuthorisationKey = "key";
        };

#pragma warning disable CS8604 // Possible null reference argument.
        Action act = () => services.AddHubtelWhatsAppSdk(configure);
#pragma warning restore CS8604 // Possible null reference argument.

        act.Should().Throw<ArgumentNullException>().WithMessage("Value cannot be null. (Parameter 'services')");
    }

    [Fact]
    public void AddWhatsAppProxyProxySdk_Should_RegisterDependencies_When_ValidConfigurationIsPassed()
    {
        IServiceCollection services = new ServiceCollection();
        // ReSharper disable once ConvertToLocalFunction
        Action<WhatsAppProxyConfig> configure = config =>
        {
            config.BaseUrl = "https://example.com";
            config.AuthorisationKey = "key";
        };
        services.AddScoped<ILogger<WhatsAppProxyApi>, NullLogger<WhatsAppProxyApi>>();
        services.AddHubtelWhatsAppSdk(configure);

        var serviceProvider = services.BuildServiceProvider();
        serviceProvider.GetService<IWhatsAppProxyApi>().Should().NotBeNull();
    }
    
}