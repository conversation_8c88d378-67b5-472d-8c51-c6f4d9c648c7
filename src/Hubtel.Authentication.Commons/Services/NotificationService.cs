using Confluent.Kafka;
using Hubtel.Authentication.Commons.Models;
using Hubtel.Kafka.Producer.Sdk.Services;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Hubtel.Authentication.Commons.Services;

public class NotificationService : INotificationService
{
    private readonly IKafkaProducer _producer;
    private readonly ILogger<NotificationService> _logger;

    public NotificationService(IKafkaProducerFactory producerFactory, ILogger<NotificationService> logger)
    {
        _producer = producerFactory.CreateKafkaProducer("general-kafka");
        _logger = logger;
    }

    public async Task SendNotification(string topic, Notification data)
    {
        try
        {
            var report = await _producer.ProduceAsync(topic, JsonConvert.SerializeObject(data));

            var logLevel = PersistenceStatus.NotPersisted.Equals(report.Status)
                ? LogLevel.Error
                : LogLevel.Debug;
            
            _logger.Log(logLevel,
                "{NotificationDestination}: Response from sending notification with template:{TemplateId}" +
                "\nData:{NotificationData}\nStatus:{NotificationStatus}\n",
                data.Destination,
                data.TemplateId,
                JsonConvert.SerializeObject(data, Formatting.Indented),
                report.Status.ToString());
        }
        catch (Exception e)
        {
            _logger.LogError(e,
                "{NotificationDestination}: An error occured sending notification\nData:{NotificationData}",
                data.Destination, JsonConvert.SerializeObject(data, Formatting.Indented));
        }
    }
}