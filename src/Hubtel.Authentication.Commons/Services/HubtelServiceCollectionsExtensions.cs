using Microsoft.Extensions.DependencyInjection;

namespace Hubtel.Authentication.Commons.Services;

public static class HubtelServiceCollectionsExtensions
{
    public static void AddNotificationService(this IServiceCollection services)
    {
        if (services == null)
        {
            ArgumentNullException.ThrowIfNull(services);
        }
        
        services.AddSingleton<INotificationService, NotificationService>();
    }
}