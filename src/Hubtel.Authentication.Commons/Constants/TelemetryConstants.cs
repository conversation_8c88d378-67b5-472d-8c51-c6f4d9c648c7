using System.Diagnostics;
using System.Reflection;
using Microsoft.Extensions.Configuration;
using OpenTelemetry.Context.Propagation;

namespace Hubtel.Authentication.Commons.Constants
{
    public static class TelemetryConstants
    {
        private static readonly IConfiguration Configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
            .AddJsonFile("appsettings.Development.json", optional: true, reloadOnChange: true)
            .Build();

        public static readonly string ServiceName =
            Configuration["OpenTelemetryConfig:ServiceName"]?.ToLower()
            ?? Assembly.GetEntryAssembly()!.GetName().Name!.ToLower();

        public static readonly ActivitySource EventsActivitySource = new(ServiceName);

        public static readonly TraceContextPropagator ContextPropagator = new();
    }

}
