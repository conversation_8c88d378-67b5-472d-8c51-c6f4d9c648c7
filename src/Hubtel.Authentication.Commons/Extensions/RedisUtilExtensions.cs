using Newtonsoft.Json;
using StackExchange.Redis;

namespace Hubtel.Authentication.Commons.Extensions
{
    public static class RedisUtilExtensions
    {
        public static long StringToInt(this string sequence, bool strict = false)
        {
            if (strict)
            {
                return long.Parse(new string(sequence.ToCharArray().Where(c => char.IsDigit(c)).ToArray()));
            }

            return sequence.ToLower().Select(Convert.ToInt32).Sum();
        }
        
         public static async Task<bool> AddToSortedSet<T>(
      this IDatabase db,
      string key,
      long score,
      T model)
    {
      return await db.SortedSetAddAsync((RedisKey) key, (RedisValue) JsonConvert.SerializeObject((object)model!), (double) score);
    }

    public static async Task<long> RemoveFromSortedSet(this IDatabase db, string key, double score) => await db.SortedSetRemoveRangeByScoreAsync((RedisKey) key, score, score);

    public static async Task<IEnumerable<T>> FindInSortedSet<T>(
      this IDatabase db,
      string key,
      long startScore,
      long endScore)
    {
      return ((IEnumerable<RedisValue>) await db.SortedSetRangeByScoreAsync((RedisKey) key, (double) startScore, (double) endScore, order: Order.Descending)).Select<RedisValue, T>((Func<RedisValue, T>) (value => JsonConvert.DeserializeObject<T>((string) value!)!));
    }

    public static async Task<IEnumerable<T>> FetchFromSortedSet<T>(
      this IDatabase db,
      string key,
      Order order = Order.Descending)
    {
      return ((IEnumerable<RedisValue>) await db.SortedSetRangeByRankAsync((RedisKey) key, order: order)).Select<RedisValue, T>((Func<RedisValue, T>) (value => JsonConvert.DeserializeObject<T>((string) value!)!));
    }

    public static async Task<IEnumerable<T>> FetchFromSortedSet<T>(
      this IDatabase db,
      string key,
      int page,
      int size,
      Order order = Order.Descending)
    {
      int num1 = size * Math.Max(page - 1, 0);
      IDatabase database = db;
      RedisKey key1 = (RedisKey) key;
      Order order1 = order;
      long start = (long) num1;
      long stop = (long) (num1 + size - 1);
      int num2 = (int) order1;
      return ((IEnumerable<RedisValue>) await database.SortedSetRangeByRankAsync(key1, start, stop, (Order) num2)).Select<RedisValue, T>((Func<RedisValue, T>) (value => JsonConvert.DeserializeObject<T>((string) value!)!));
    }

    public static async Task<bool> SetKey<T>(this IDatabase db, string key, T model) => await db.StringSetAsync((RedisKey) key, (RedisValue) JsonConvert.SerializeObject((object) model!));

    public static async Task<T?> GetKey<T>(this IDatabase db, string key)
    {
        var redisValue = await db.StringGetAsync((RedisKey)key);
        var stringValue = redisValue.ToString()?? "";
        return JsonConvert.DeserializeObject<T>(stringValue);
    }

    public static async Task<long> SortedSetCardinality(this IDatabase db, string key) => await db.SortedSetLengthAsync((RedisKey) key);
    }


    public static class RedisConstants
    {
        public const string MainRedisHostName = "OTPServiceRedis";
        public const string MainCustomerDataRedisHostName = "CustomerDataServiceRedis";
        public const string DefaultOtpCacheDb = "otpcachedb";
        public const string DefaultConsumerBlacklistDb = "consumerblacklistdb";
        public const string AuthConfigurationDb = "authconfigurationdb";
    }
    
    public static class CommonConstants
    {
        public const string RedisOtpKey = "hubtel:consumerotp:interal-list";
        public const string RedisAuthConfigKey = "hubtel:authconfigs:interal-list";
        public const string RedisUssdSuccessKey = "hubtel:ussd:sucess";
        public const string UssdVerified = "hubtel:ussd:verified";
    }

    
}