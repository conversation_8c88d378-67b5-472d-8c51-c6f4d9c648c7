using Confluent.Kafka.Extensions.OpenTelemetry;
using Hubtel.Authentication.Commons.Constants;
using Hubtel.OpenTelemetry.Instrumentation.Options;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Npgsql;
using OpenTelemetry;
using OpenTelemetry.Context.Propagation;
using OpenTelemetry.Logs;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;
using StackExchange.Redis;
using B3Propagator = OpenTelemetry.Extensions.Propagators.B3Propagator;

namespace Hubtel.Authentication.Commons.Extensions
{
    public static class TelemetryExtensions
    {
        public static IServiceCollection AddAppOpentelemtry(this IServiceCollection services, IConfiguration configuration)
        {
            var otelConfig = configuration.GetSection(nameof(OpenTelemetryConfig)).Get<OpenTelemetryConfig>();
            string? appVersion = configuration["APP_VERSION"];
            Sdk.SetDefaultTextMapPropagator(new CompositeTextMapPropagator(
                new TextMapPropagator[]
                {
                        new TraceContextPropagator(),
                        new BaggagePropagator(),
                        new B3Propagator()
                }));

            var env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")!;
            services.AddOpenTelemetry()
                .ConfigureResource(resourceBuilder => {
                    resourceBuilder.AddService(TelemetryConstants.ServiceName);
                    resourceBuilder.AddAttributes([new KeyValuePair<string, object>("deployment.environment.name", env.ToLower())]);
                    if (!string.IsNullOrEmpty(appVersion))
                    {
                        resourceBuilder.AddAttributes([new KeyValuePair<string, object>("service.version", appVersion)]);
                    }
                })
                .WithTracing((builder) =>
                {
                    if (otelConfig!.ShowConsoleTrace)
                    {
                        builder.AddConsoleExporter();
                    }
                    builder.AddAspNetCoreInstrumentation()
                       .AddHttpClientInstrumentation(
                           options =>
                           {
                               options.FilterHttpRequestMessage =
                               (httpRequestMessage) =>
                                   !httpRequestMessage.RequestUri!.Host.Contains("visualstudio.com",
                                       StringComparison.OrdinalIgnoreCase);
                               options.RecordException = true;
                           })
                       .AddRedisInstrumentation(options =>
                       {
                           options.FlushInterval = TimeSpan.FromSeconds(2);
                           options.SetVerboseDatabaseStatements = true;
                           options.EnrichActivityWithTimingEvents = true;
                       })
                       .ConfigureRedisInstrumentation((provider, redisInstrumentation) =>
                       {
                           var keys = services
                               .Where(sd => sd.IsKeyedService && sd.ServiceType == typeof(IConnectionMultiplexer))
                               .Select(d => d.ServiceKey)
                               .ToList();
                           if (keys.Count == 0) return;
                           foreach (var connection in
                                    keys.Select(provider.GetRequiredKeyedService<IConnectionMultiplexer>))
                           {
                               redisInstrumentation.AddConnection(connection);
                           }
                       })
                       .AddConfluentKafkaInstrumentation()
                       .AddEntityFrameworkCoreInstrumentation(options => { options.SetDbStatementForText = true; })
                       .AddNpgsql()
                       .AddElasticsearchClientInstrumentation()
                       .AddSource(TelemetryConstants.ServiceName)
                        .AddOtlpExporter(options =>
                        {
                            options.Endpoint = new Uri($"{otelConfig.Protocol}://{otelConfig.Host}:{otelConfig.Port}");
                        });
                }).WithMetrics(metrics =>
                {
                    if (otelConfig!.ShowConsoleMetrics)
                    {
                        metrics.AddConsoleExporter();
                    }
                    metrics.AddOtlpExporter(options =>
                    {
                        options.Endpoint = new Uri($"{otelConfig!.Protocol}://{otelConfig.Host}:{otelConfig.Port}");
                    });
                    metrics.AddHttpClientInstrumentation()
                    .AddAspNetCoreInstrumentation()
                    .AddMeter("Microsoft.AspNetCore.Hosting",
                        "Microsoft.AspNetCore.Diagnostics",
                        "Microsoft.EntityFrameworkCore",
                        TelemetryConstants.ServiceName);
                }).WithLogging(logging =>
                {
                    logging.AddOtlpExporter(exporterOptions =>
                    {
                        exporterOptions.Endpoint = new Uri($"{otelConfig!.Protocol}://{otelConfig.Host}:{otelConfig.Port}");
                    });
                });
            return services;
        }
    }
}
