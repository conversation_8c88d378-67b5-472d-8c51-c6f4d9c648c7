namespace Hubtel.Authentication.Commons.Models;

/// <summary>
/// Represents the different channels that can be used for authentication.
/// </summary>
public enum AuthenticationChannel
{
    /// <summary>
    /// Enum representing the authentication channels available.
    /// </summary>
    None = 0,

    /// <summary>
    /// Represents the SMS authentication channel.
    /// </summary>
    /// <remarks>
    /// The SMS authentication channel is used for sending verification codes and one-time passwords (OTPs) via SMS messages.
    /// </remarks>
    Sms = 1,

    /// <summary>
    /// Represents the authentication channel using WhatsApp.
    /// </summary>
    WhatsApp = 2,

    /// <summary>
    /// Represents the USSD authentication channel.
    /// </summary>
    Ussd = 3,

    /// <summary>
    /// Represents the authentication channel using ProductEmail.
    /// </summary>
    Email = 4,

    /// <summary>
    /// Specifies the authentication channel as QrCode.
    /// </summary>
    QrCode = 5
}