namespace Hubtel.Authentication.Commons.Models.Fraud;

/// <summary>
/// Represents a fraud report.
/// </summary>
/// <remarks>
/// The FraudReport class represents a report of fraudulent activity. It contains properties that describe the details of the report, such as the phone number, source of the report,
/// fingerprint data, app, IP address, extra data, and creation date.
/// </remarks>
public class FraudReport
{
    /// <summary>
    /// Represents a fraud report.
    /// </summary>
    protected FraudReport()
    {
    }

    public FraudReport(string phoneNumber, string source, string fingerPrintData, string app, string ipAddress,
        Dictionary<string, object> extraData, DateTime createdAt) : this()
    {
        PhoneNumber = phoneNumber;
        Source = source;
        FingerPrintData = fingerPrintData;
        App = app;
        IpAddress = ipAddress;
        ExtraData = extraData;
        CreatedAt = createdAt;
    }


    /// <summary>
    /// Represents a fraudulent report.
    /// </summary>
    public string PhoneNumber { get; init; } = null!;

    /// <summary>
    /// Represents a fraud report.
    /// </summary>
    public string Source { get; init; } = null!;

    /// <summary>
    /// Represents the fingerprint data associated with a fraud report.
    /// </summary>
    public string FingerPrintData { get; init; } = null!;

    /// <summary>
    /// Represents a fraud report.
    /// </summary>
    public string App { get; init; } = null!;

    /// <summary>
    /// Represents the IP address of a fraud report.
    /// </summary>
    public string IpAddress { get; init; } = null!;

    /// <summary>
    /// Represents a fraud report.
    /// </summary>
    public Dictionary<string, object> ExtraData { get; init; } = null!;

    /// <summary>
    /// Represents the date and time when the FraudReport or LoginEvent was created.
    /// </summary>
    /// <remarks>
    /// This property stores the creation date and time of the FraudReport or LoginEvent.
    /// </remarks>
    public DateTime CreatedAt { get; init; }
}