

namespace Hubtel.Authentication.Commons.Models
{
    /// <summary>
    /// Represents an OTP (One-Time Password) entity.
    /// </summary>
    public class Otp
    {
        /// <summary>
        /// Gets or sets the type of OTP.
        /// </summary>
        public string OtpType { get; set; } = null!;

        /// <summary>
        /// Gets or sets the request ID associated with the OTP.
        /// </summary>
        public string RequestId { get; set; } = null!;

        /// <summary>
        /// Gets or sets the MSISDN (Mobile Station International Subscriber Directory Number) associated with the OTP.
        /// </summary>
        public string Msisdn { get; set; } = null!;

        /// <summary>
        /// Gets or sets the OTP code.
        /// </summary>
        public string OtpCode { get; set; } = null!;

        /// <summary>
        /// Gets or sets the date and time when the OTP was created.
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the OTP was verified.
        /// </summary>
        public DateTime? VerifiedAt { get; set; }

        /// <summary>
        /// Gets or sets the number of times the OTP has been used.
        /// </summary>
        public int OTPUseCount { get; set; }

        /// <summary>
        /// Gets or sets the number of times the OTP has been resent.
        /// </summary>
        public int ResendUseCount { get; set; }

        /// <summary>
        /// Gets or sets the number of times the OTP has been resent via USSD (Unstructured Supplementary Service Data).
        /// </summary>
        public int USSDResendCount { get; set; }

        /// <summary>
        /// Gets or sets the number of times the OTP has been tried again.
        /// </summary>
        public int TryAgainUseCount { get; set; }

        /// <summary>
        /// Gets or sets the sender ID associated with the OTP.
        /// </summary>
        public string SenderId { get; set; } = null!;

        /// <summary>
        /// Gets or sets the country code associated with the OTP.
        /// </summary>
        public string CountryCode { get; set; } = null!;

        /// <summary>
        /// Gets or sets the code associated with the OTP.
        /// </summary>
        public string Code { get; set; } = null!;

        /// <summary>
        /// Gets or sets the prefix associated with the OTP.
        /// </summary>
        public string Prefix { get; set; } = null!;

        /// <summary>
        /// Gets or sets a value indicating whether the OTP is verified.
        /// </summary>
        public bool IsVerified { get; set; }

        /// <summary>
        /// Gets or sets the OTP liability policy.
        /// </summary>
        public string OtpLiabilityPolicy { get; set; } = "";
        public AuthenticationChannel AuthenticationChannel { get; set; }
    }

    /// <summary>
    /// Represents an extended OTP (One-Time Password) entity.
    /// </summary>
    public class ExtendedOtp : Otp
    {
        /// <summary>
        /// Gets or sets the device name associated with the extended OTP.
        /// </summary>
        public string DeviceName { get; set; } = null!;

        /// <summary>
        /// Gets or sets the location associated with the extended OTP.
        /// </summary>
        public string Location { get; set; } = null!;

        /// <summary>
        /// Gets or sets the login app name associated with the extended OTP.
        /// </summary>
        public string LoginAppName { get; set; } = null!;

        /// <summary>
        /// Gets or sets the email associated with the extended OTP.
        /// </summary>
        public string Email { get; set; } = null!;

        /// <summary>
        /// Gets or sets the app ID associated with the extended OTP.
        /// </summary>
        public Guid AppId { get; set; } = Guid.Empty;
        
        
    }
}
