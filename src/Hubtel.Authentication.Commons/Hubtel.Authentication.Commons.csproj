<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>
    
    <ItemGroup>
        <PackageReference Include="Akka" Version="1.5.31" />
        <PackageReference Include="Flurl" Version="4.0.0" />
        <PackageReference Include="Flurl.Http" Version="4.0.2" />
        <PackageReference Include="Hubtel.Redis.Sdk" Version="8.0.9" />
        <PackageReference Include="Hubtel.Kafka.Producer.Sdk" Version="8.0.9" />
        <PackageReference Include="QRCoder" Version="1.6.0" />
        <PackageReference Include="SixLabors.ImageSharp" Version="3.1.6" />
        <PackageReference Include="SixLabors.ImageSharp.Drawing" Version="2.1.4" />
		<PackageReference Include="Confluent.Kafka" Version="2.6.1" />
		<PackageReference Include="Hubtel.Otel.Instrumentation" Version="8.0.29" />
    </ItemGroup>

</Project>
