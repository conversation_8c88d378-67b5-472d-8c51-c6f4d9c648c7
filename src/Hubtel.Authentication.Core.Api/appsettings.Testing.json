{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}, "GELF": {"Host": "localhost", "Port": 12202, "LogSource": "Hubtel.Microservices.Auth.Api", "Facility": "Hubtel.Microservices.Auth.Api", "Environment": "Development", "LogLevel": {"Default": "Debug"}}}, "AllowedHosts": "*", "KafkaProducerConfig": {"BootstrapServers": "kafka:9092"}, "ConnectionStrings": {"DbConnection": "Server=********;Port=5432;User Id=********;Password=********;Database=HubtelAuthentication;Pooling=true;CommandTimeout=120;Timeout=30"}, "KafkaExtra": {"TopicPrefix": "hubtel.notification.", "NotificationTopic": "hubtel.loyalty.upgrade.movement", "EventLogTopic": "hubtel.eventstore.events", "FraudSignInLogTopic": "hubtel.fraud.signin.logs"}, "Jwt": {"Audience": "http://hubtel.com", "Issuer": "http://hubtel.com", "SigningKey": "80e52d7a1cdf46d8ae72a4a757c4aa8e", "ValidityPeriod": 1440, "MobileValidityPeriod": 525600}, "AWSConfig": {"ClientID": "", "PoolID": "", "AccessKey": "", "AccessSecret": ""}, "ApiAccounts": [{"Key": "y573fnt66h473d", "Secret": "duib5hft2evh5ryg3g63", "Name": "Consumer OTP"}], "Notification": {"SmsFrom": "<PERSON><PERSON><PERSON>", "SmsTemplate": "Enter {0} in the space provided. This completes your verification code starting with {1}.", "EmailTo": "<EMAIL>", "EmailSubject": "Consumer ({0}) Auth Otp Issue", "RecurringTemplate": "Enter {0} as required for {1}. {2}", "EmailTemplate": "Consumer ({0}) is unable to login"}, "SocketService": {"Url": "https://qr.hubtel.com/send", "Headers": {"Authorization": "Basic eHNuZWZpYWs6d3lua3NqY3k="}}, "ConsumerData": {"Url": "http://127.0.0.1:8035"}, "ConsumerDataConfig": {"Url": "http://consumerdata.hubtel.com", "BearerToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************.qLvyZYRUSMd2GF1w8dUjxY2Qo3fJco2xGxau_cz-jnQ", "RedisHost": "redis", "RedisPort": "6379", "RedisDbNumber": 0}, "StatsdConfig": {"Server": "127.0.0.1", "Port": "6379", "Prefix": "mystatsdprefix"}, "ApplicationInsights": {"InstrumentationKey": "tbd"}, "RedisConfiguration": {"Setup": [{"Host": "redis", "Name": "OTPServiceRedis", "Port": "6379", "Databases": [{"alias": "otpcachedb", "db": 1}, {"alias": "authconfigurationdb", "db": 2}]}, {"Host": "redis", "Name": "CustomerDataServiceRedis", "Port": "6379", "Databases": [{"alias": "consumerblacklistdb", "db": 0}]}]}, "ApiDocsConfig": {"ShowSwaggerUi": true, "ShowRedocUi": false, "EnableSwaggerTryIt": true}, "BearerTokenConfig": {"Issuer": "http://hubtel.com", "Audience": "http://hubtel.com", "SigningKey": "1234"}, "LogRequestResponse": false, "IgnoreList": {"IgnoreListKeys": ["573f4e6f15d34575ac31424fbfc44842", "NHc494b17520cc437b9707377f392b679b", "NH30260986179d401bb38cde7b5656b22d", "NHb5d14b38440f4a7fb2b87658252a930f", "NHb89066e341d04622a1c347b871fa634c"]}, "SpecialOtpCases": {"Numbers": [{"Number": "+8618616792682", "Email": "<EMAIL>"}, {"Number": "+8618605127679", "Email": "<EMAIL>"}, {"Number": "+8618616331231", "Email": "<EMAIL>"}, {"Number": "+8615810542825", "Email": "<EMAIL>"}, {"Number": "+8615810536592", "Email": "<EMAIL>"}, {"Number": "+8613241954186", "Email": "<EMAIL>"}, {"Number": "233248318611", "Email": "<EMAIL>"}, {"Number": "233209045716", "Email": "<EMAIL>"}, {"Number": "+447415512691", "Email": "<EMAIL>"}, {"Number": "+919920775327", "Email": "<EMAIL>"}, {"Number": "+971523616997", "Email": "<EMAIL>"}, {"Number": "233244292888", "Email": "<EMAIL>"}, {"Number": "+60198973978", "Email": "<EMAIL>"}, {"Number": "+60183968469", "Email": "<EMAIL>"}, {"Number": "+254712591830", "Email": "<EMAIL>"}, {"Number": "+447947804914", "Email": "<EMAIL>"}, {"Number": "+***********", "Email": "<EMAIL>"}, {"Number": "+***********", "Email": "<EMAIL>"}, {"Number": "+************", "Email": "<EMAIL>"}]}, "WhatsAppProxyConfig": {"AuthorisationKey": "bXB4ZWVrYW46YXplb3lycnA=", "BaseUrl": "http://54.220.126.96:9053/v2", "CallBackUrl": "https://webhook.site/2801a259-0039-45f5-8efa-ab4396d14743", "ClientId": "yckntnhy", "ClientSecret": "egpjyiaz"}, "FraudMiddlewareExclusionList": ["/login/initiate", "/", "/swagger", "/health"], "AuthStores": {"AccountStoreConfigs": [{"Id": "d317ed35-d765-4631-825d-ef36094d14c6", "ProductName": "<PERSON><PERSON><PERSON>", "ProductLogo": "https://designs.hubtel.com/v4/website/assets/images/logo.png", "AppName": "hubtel.com", "AccountLookUpUrl": "https://mp30f18dd44fa72ef11a.free.beeceptor.com/data", "Primary": "#FFFFFF", "TextColour": "#FFFFFF", "BgLight": "#FFFFFF", "BgLighter": "#FFFFFF", "SigningKey": "", "SenderId": "<PERSON><PERSON><PERSON>"}, {"Id": "a155bc81-0edb-4bc7-b5e3-b149f77a7093", "ProductName": "<PERSON><PERSON>", "ProductLogo": "https://designs.hubtel.com/v4/assets/images/bhim-logo.svg", "AppName": "bhimconcert.hpay.co", "AccountLookUpUrl": "https://eventman-api.hubtel.com:9015/api/v1/eventManagement/auth/attendee/lookup", "Primary": "#FFFFFF", "TextColour": "#FFFFFF", "BgLight": "#FFFFFF", "BgLighter": "#FFFFFF", "SigningKey": "", "SenderId": "BhimConcert"}, {"Id": "714fa9f7-42ac-4052-9342-e7c81b3fe693", "ProductName": "<PERSON><PERSON>", "ProductLogo": "https://designs.hubtel.com/v4/assets/images/bhim-logo.svg", "AppName": "vendor-bhimconcert.hpay.co", "AccountLookUpUrl": "https://eventman-api.hubtel.com:9015/api/v1/eventManagement/auth/vendor/lookup", "Primary": "#FFFFFF", "TextColour": "#FFFFFF", "BgLight": "#FFFFFF", "BgLighter": "#FFFFFF", "SigningKey": "", "SenderId": "BhimConcert"}, {"Id": "542ebaa7-5a7c-45ea-ab1c-d4fca581940c", "ProductName": "<PERSON><PERSON>", "ProductLogo": "https://designs.hubtel.com/v4/assets/images/bhim-logo.svg", "AppName": "salesperson-bhimconcert.hpay.co", "AccountLookUpUrl": "https://eventman-api.hubtel.com:9015/api/v1/eventManagement/auth/salesperson/lookup", "Primary": "#FFFFFF", "TextColour": "#FFFFFF", "BgLight": "#FFFFFF", "BgLighter": "#FFFFFF", "SigningKey": "", "SenderId": "BhimConcert"}, {"Id": "4afe7c21-d273-4fc3-9be3-b06c87bccfdb", "ProductName": "<PERSON><PERSON>", "ProductLogo": "https://designs.hubtel.com/v4/assets/images/bhim-logo.svg", "AppName": "eventadmin-bhimconcert.hpay.co", "AccountLookUpUrl": "https://eventman-api.hubtel.com:9015/api/v1/eventManagement/auth/admin/lookup", "Primary": "#FFFFFF", "TextColour": "#FFFFFF", "BgLight": "#FFFFFF", "BgLighter": "#FFFFFF", "SigningKey": "", "SenderId": "BhimConcert"}, {"Id": "48503c0a-7bea-49fa-be68-8a2bdb773feb", "ProductName": "<PERSON><PERSON>", "ProductLogo": "https://designs.hubtel.com/v4/assets/images/bhim-logo.svg", "AppName": "eventstaff-bhimconcert.hpay.co", "AccountLookUpUrl": "https://eventman-api.hubtel.com:9015/api/v1/eventManagement/auth/staff/lookup", "Primary": "#FFFFFF", "TextColour": "#FFFFFF", "BgLight": "#FFFFFF", "BgLighter": "#FFFFFF", "SigningKey": "", "SenderId": "BhimConcert"}, {"Id": "656bec84-eaf5-4fd4-9665-14bb89fcc81d", "ProductName": "<PERSON><PERSON>", "ProductLogo": "https://designs.hubtel.com/v4/assets/images/bhim-logo.svg", "AppName": "raveexchanger-bhimconcert.hpay.co", "AccountLookUpUrl": "https://eventman-api.hubtel.com:9015/api/v1/eventManagement/auth/exchanger/lookup", "Primary": "#FFFFFF", "TextColour": "#FFFFFF", "BgLight": "#FFFFFF", "BgLighter": "#FFFFFF", "SigningKey": "", "SenderId": "BhimConcert"}]}, "Socket": {"Success": "https://auth.hubtel.com/api/ussd/success", "Failure": "https://auth.hubtel.com/api/ussd/failure"}, "Qr": {"Success": "https://auth.hubtel.com/api/qr/success", "Failure": "https://auth.hubtel.com/api/qr/failure"}, "OpenTelemetryConfig": {"ServiceName": "hubtel.authentication.core.api", "Host": "localhost", "Port": 4317, "Protocol": "http", "ShowConsoleMetrics": false, "ShowConsoleTrace": false}}