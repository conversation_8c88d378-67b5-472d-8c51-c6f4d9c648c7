<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <GenerateDocumentationFile>true</GenerateDocumentationFile>
        <NoWarn>$(NoWarn);1591</NoWarn>
        <LangVersion>latest</LangVersion>
		<Nullable>warnings</Nullable>
    </PropertyGroup>

    <ItemGroup>

        <PackageReference Include="Akka.Hosting" Version="********" />
        <PackageReference Include="AWSSDK.CognitoIdentityProvider" Version="3.7.403.36" />
        <PackageReference Include="Confluent.Kafka" Version="2.6.1" />
        <PackageReference Include="FluentValidation" Version="11.11.0" />
        <PackageReference Include="Flurl.Http" Version="4.0.2" />
        <PackageReference Include="Mapster" Version="7.4.0" />
        <PackageReference Include="Microsoft.AspNetCore.JsonPatch" Version="8.0.11" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.36" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.11" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.11">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />
        <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.22.0" />
        <PackageReference Include="Akka" Version="1.5.31" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
        <PackageReference Include="JustEat.StatsD" Version="4.2.0" />
        <PackageReference Include="Gelf.Extensions.Logging" Version="2.6.0" />
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.11" />
		<PackageReference Include="QRCoder" Version="1.6.0" />
        <PackageReference Include="Hubtel.Instrumentation" Version="8.0.9" />
        <PackageReference Include="Hubtel.Kafka.Host" Version="8.0.9" />
        <PackageReference Include="Hubtel.PhoneNumbers" Version="8.0.9" />
        <PackageReference Include="Pluralize.NET" Version="1.0.2" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.11" />
        <PackageReference Include="Swashbuckle.AspNetCore.ReDoc" Version="6.9.0" />
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.2.1" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.9.0" />
        <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="6.9.0" />


    </ItemGroup>

    <ItemGroup>
        <None Update="README.md">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Hubtel.Authentication.Commons\Hubtel.Authentication.Commons.csproj" />
      <ProjectReference Include="..\Hubtel.Authentication.Whatsapp.Sdk\Hubtel.Authentication.Whatsapp.Sdk.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Domain\" />
    </ItemGroup>

</Project>
