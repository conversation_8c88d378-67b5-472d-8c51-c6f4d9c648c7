using System;
using System.Security.Cryptography;

namespace Hubtel.Authentication.Core.Api.Components.Utils
{
    public static class PinGenerator
    {
        // Define default password length.
        private const int DefaultPasswordLength = 5;

        //No characters that are confusing: i, I, l, L, o, O, 0, 1, u, v

        public const string PasswordCharsAlpha = "abcdefghjkmnpqrstwxyzABCDEFGHJKMNPQRSTWXYZ";
        public const string PasswordCharsNumeric = "23456789";
        public const string PasswordCharsSpecial = "*$-+?_&=!%{}/";
        public const string PasswordCharsAlphanumeric = PasswordCharsAlpha + PasswordCharsNumeric;
        public const string PasswordCharsAll = PasswordCharsAlphanumeric + PasswordCharsSpecial;

        /// <summary>
        /// Generates a random password with the default length.
        /// </summary>
        /// <returns>Randomly generated password.</returns>
        public static string Generate()
        {
            return Generate(DefaultPasswordLength, PasswordCharsAll);
        }

        /// <summary>
        /// Generates a random password with the default length.
        /// </summary>
        /// <returns>Randomly generated password.</returns>
        public static string GenerateAlphaNumeric(int length)
        {
            return Generate(length, PasswordCharsAlphanumeric);
        }

        /// <summary>
        /// Generates a random password with the default length.
        /// </summary>
        /// <returns>Randomly generated password.</returns>
        public static string Generate(string passwordChars)
        {
            return Generate(DefaultPasswordLength, passwordChars);
        }

        /// <summary>
        /// Generates a random password with the default length.
        /// </summary>
        /// <returns>Randomly generated password.</returns>
        public static string Generate(int passwordLength)
        {
            return Generate(passwordLength, PasswordCharsAll);
        }

        /// <summary>
        /// Generates a random password.
        /// </summary>
        /// <returns>Randomly generated password.</returns>
        public static string Generate(int passwordLength, string passwordChars)
        {
            return GeneratePassword(passwordLength, passwordChars);
        }

        /// <summary>
        /// Generates the password.
        /// </summary>
        /// <returns></returns>
        private static string GeneratePassword(int passwordLength, string passwordCharacters)
        {
            ArgumentOutOfRangeException.ThrowIfNegative(passwordLength);

            if (string.IsNullOrEmpty(passwordCharacters))
                throw new ArgumentOutOfRangeException(nameof(passwordCharacters));

            var password = new char[passwordLength];
            var random = GetRandom();

            for (int i = 0; i < passwordLength; i++)
                password[i] = passwordCharacters[random.Next(passwordCharacters.Length)];

            return new string(password);
        }

        /// <summary>
        /// Gets a random object with a real random seed
        /// </summary>
        /// <returns></returns>
        private static Random GetRandom()
        {
            // Use a 4-byte array to fill it with random bytes and convert it then
            // to an integer value.
            var randomBytes = new byte[4];

            // Generate 4 random bytes.
            RandomNumberGenerator.Fill(randomBytes);

            // Convert 4 bytes into a 32-bit integer value.
            int seed = (randomBytes[0] & 0x7f) << 24 |
                        randomBytes[1] << 16 |
                        randomBytes[2] << 8 |
                        randomBytes[3];

            // Now, this is real randomization.
            return new Random(seed);
        }
    }
}
