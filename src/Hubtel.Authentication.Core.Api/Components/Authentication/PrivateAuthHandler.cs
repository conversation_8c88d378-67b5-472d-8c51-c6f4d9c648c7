using System;
using System.Net.Http.Headers;
using System.Security.Claims;
using System.Text.Encodings.Web;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Hubtel.Authentication.Core.Api.Components.Authentication
{
    public class PrivateAuthHandler : AuthenticationHandler<AuthenticationSchemeOptions>
    {
        private const string MissingAuthorizationHeaderError = "Missing Authorization Header";
        private const string InvalidAuthorizationHeaderError = "Invalid Authorization Header";
        private const string InvalidAuthParameterError = "Invalid auth parameter";

        public PrivateAuthHandler
        (
            ILoggerFactory logger,
            UrlEncoder encoder,
            IOptionsMonitor<AuthenticationSchemeOptions> options
        ) : base(options, logger, encoder)
        {
        }

        protected override Task<AuthenticateResult> HandleAuthenticateAsync()
        {
            if (!HaveAuthorizationHeader())
                return Task.FromResult(AuthenticateResult.Fail(MissingAuthorizationHeaderError));

            var auth = GetAuthorizationValue();
            if (auth == null)
                return Task.FromResult(AuthenticateResult.Fail(InvalidAuthorizationHeaderError));

            if (!IsSchemeValid(auth))
                return Task.FromResult(AuthenticateResult.NoResult());

            var isKeyValid = ValidateKey(auth.Parameter);
            if (!isKeyValid)
                return Task.FromResult(AuthenticateResult.Fail(InvalidAuthParameterError));

            var ticket = CreateAuthenticationTicket();
            return Task.FromResult(AuthenticateResult.Success(ticket));
        }

        private bool HaveAuthorizationHeader() =>
            Request.Headers.ContainsKey("Authorization");

        private AuthenticationHeaderValue GetAuthorizationValue()
        {
            if (!Request.Headers.TryGetValue("Authorization", out var headerValue) ||
                string.IsNullOrWhiteSpace(headerValue)) return null;
            try
            {
                var authHeader = headerValue.ToString();
                return AuthenticationHeaderValue.Parse(authHeader);
            }
            catch
            {
                return null;
            }
            
        }
        
        private static bool IsSchemeValid(AuthenticationHeaderValue auth) =>
            "privatekey".Equals(auth.Scheme, StringComparison.OrdinalIgnoreCase);

        private static bool ValidateKey(string key)
        {
            return !string.IsNullOrWhiteSpace(key);
        }

        private AuthenticationTicket CreateAuthenticationTicket()
        {
            var claims = new[]
            {
                //by convention, .NET Core authenticated user expects Name and NameIdentifier claims
                new Claim(ClaimTypes.NameIdentifier, "<username here>"),
                new Claim(ClaimTypes.Name, "<username here>"),
            };
            var identity = new ClaimsIdentity(claims, Scheme.Name);
            var principal = new ClaimsPrincipal(identity);
            return new AuthenticationTicket(principal, Scheme.Name);
        }
    }
}