using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Security.Claims;
using System.Text;
using System.Text.Encodings.Web;
using System.Threading.Tasks;
using Hubtel.Authentication.Core.Api.Options;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Hubtel.Authentication.Core.Api.Components.Authentication
{
    public class BasicAuthHandler : AuthenticationHandler<AuthenticationSchemeOptions>
    {
        private readonly IConfiguration _configuration;
        private readonly IOptions<List<ApiAccount>> _accounts;

        public BasicAuthHandler
        (
            IConfiguration configuration,
            ILoggerFactory logger,
            UrlEncoder encoder,
            ISystemClock clock,
            IOptionsMonitor<AuthenticationSchemeOptions> options,
            IOptions<List<ApiAccount>> accounts
        ) : base(options, logger, encoder, clock)
        {
            _configuration = configuration;
            _accounts = accounts;
        }

        protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
        {
            if (!Request.Headers.ContainsKey("Authorization"))
                return AuthenticateResult.Fail("Missing Authorization Header");

            await Task.Delay(0);

            bool passed = false;
            ApiAccount account;

            try
            {
                if (!Request.Headers.TryGetValue("Authorization", out var authHeaderValue))
                {
                    return AuthenticateResult.Fail("Missing Authorization Header");
                }

                var auth = AuthenticationHeaderValue.Parse(authHeaderValue!);

                if (!"basic".Equals(auth.Scheme, StringComparison.OrdinalIgnoreCase))
                {
                    return AuthenticateResult.NoResult();
                }

                var bytes = Convert.FromBase64String(auth.Parameter!);
                var credentials = Encoding.UTF8.GetString(bytes).Split(new[] { ':' }, 2);
                var username = credentials[0];
                var password = credentials[1];

                account = _accounts.Value.FirstOrDefault(s => s.Key.Equals(username) && s.Secret.Equals(password));

                passed = account != null;
            }
            catch
            {
                return AuthenticateResult.Fail("Invalid Authorization Header");
            }

            if (!passed)
                return AuthenticateResult.Fail("Invalid auth parameter");

            var claims = new[]
            {
                new Claim(ClaimTypes.NameIdentifier, account!.Name),
                new Claim(ClaimTypes.Name, account.Name),
                new Claim(ClaimTypes.Actor, account.Key)
            };

            var identity = new ClaimsIdentity(claims, Scheme.Name);
            var principal = new ClaimsPrincipal(identity);
            var ticket = new AuthenticationTicket(principal, Scheme.Name);

            return AuthenticateResult.Success(ticket);
        }
    }
}