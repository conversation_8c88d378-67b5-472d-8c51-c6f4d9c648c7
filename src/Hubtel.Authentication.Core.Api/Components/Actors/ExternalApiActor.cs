using Akka.Actor;
using Flurl.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using Hubtel.Authentication.Core.Api.Models.AppModels;
using Hubtel.Authentication.Core.Api.Options;
using Hubtel.Authentication.Core.Api.Extensions;

namespace Hubtel.Authentication.Core.Api.Components.Actors
{
    public class ExternalApiActor : ReceiveActor
    {
        private readonly SocketService _socketServiceConfig;
        private readonly HttpClient _httpClient;
        private readonly ILogger<ExternalApiActor> _logger;

        public ExternalApiActor(IHttpClientFactory httpClientFactory, 
            ILogger<ExternalApiActor> logger,
            IOptions<SocketService> socketServiceConfig)
        {
            _socketServiceConfig = socketServiceConfig.Value;
            _httpClient = httpClientFactory.CreateClient(nameof(ApiType.ConsumerAuth));
            _logger = logger;

            InitActor();
        }

        private void InitActor()
        {
            Receive<NotifyLoginService>(data =>
            {
                var jsonTemp = JsonConvert.SerializeObject(data.Result, new JsonSerializerSettings
                {
                    ContractResolver = new Newtonsoft.Json.Serialization.CamelCasePropertyNamesContractResolver()
                });

                var json = jsonTemp.Replace("data", "Data");

                var stringContent = new StringContent(json, Encoding.UTF8, "application/json");

                _socketServiceConfig.Url.WithHeader("Authorization", _socketServiceConfig.Headers.Authorization)
                    .PostAsync(stringContent).PipeTo(Self);
            });

            Receive<ConsumerAuthMessage>(data =>
            {
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", data.Token);
                _httpClient.PostAsJsonAsync("/api/Customer", new { mobileNumber = data.MobileNumer }).PipeTo(Self);
            });

            ReceiveAsync<HttpResponseMessage>(async data =>
            {
                var result = await data.Content.ReadAsStringAsync();
                _logger.LogWarning("------>> Response -> {Response} {RequestUri} !!!!", result, data.RequestMessage?.RequestUri);
            });
        }
    }

    public struct NotifyLoginService
    {
        public AppResult Result { get; }

        public NotifyLoginService(AppResult result)
        {
            Result = result;
        }
    }
}
