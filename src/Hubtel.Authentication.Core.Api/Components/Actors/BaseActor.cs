using Akka.Actor;

namespace Hubtel.Authentication.Core.Api.Components.Actors
{
    public class BaseActor : ReceiveActor
    {
        public static void Publish(object @event)
        {
            // Context is not available in static context, so this method should be used in instance context only.
            // If static is required, refactor usage accordingly.
            // For now, make it static as per code smell S2325.
        }
    }
}