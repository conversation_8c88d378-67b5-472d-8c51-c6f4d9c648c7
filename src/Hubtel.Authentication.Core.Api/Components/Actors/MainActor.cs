using Hubtel.Redis.Sdk.Services;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Hubtel.Authentication.Commons.Extensions;
using Hubtel.Authentication.Commons.Models;
using Hubtel.Authentication.Core.Api.Components.Actors.ActorMessages;
using Hubtel.Authentication.Core.Api.Dtos;
using Hubtel.Authentication.Core.Api.Models.AppModels;
using Hubtel.Authentication.Core.Api.Options;
using Hubtel.Authentication.Core.Api.Services.Interface;

namespace Hubtel.Authentication.Core.Api.Components.Actors
{
    public class MainActor : BaseActor
    {
        private readonly IKafkaService _service;
        private readonly NotificationConfig _notificationConfig;
        private readonly IDatabase _otpCacheRepository;
        private readonly ILogger<MainActor> _logger;

        public MainActor(IKafkaService service,
           IMultiRedisHostCacheRepository multiRedisHostCacheRepository,
            IOptions<NotificationConfig> notificationConfig,
            ILogger<MainActor> logger)
        {
            _service = service;
            _otpCacheRepository = multiRedisHostCacheRepository.GetDb(RedisConstants.MainRedisHostName, RedisConstants.DefaultOtpCacheDb);
            _notificationConfig = notificationConfig.Value;
            _logger = logger;

            ReceiveAsync<ProcessOtpRequest>(DoProcessOtpRequest);
            ReceiveAsync<ProcessEmailOtpRequest>(DoProcessEmailOtpRequest);
            ReceiveAsync<ProcessResendOtpRequest>(DoProcessResendOtpRequest);
        }

        private async Task DoProcessOtpRequest(ProcessOtpRequest message)
        {
            try
            {
                var score = message.Otp.Msisdn.StringToInt(true);
                _ =  _otpCacheRepository.SortedSetRemoveRangeByScore(CommonConstants.RedisOtpKey, score, score);
               _ = await _otpCacheRepository.AddToSortedSet(CommonConstants.RedisOtpKey, score, message.Otp);

                await SendNewNotificationAsync(message.Otp, senderId: message.Otp.SenderId);

       
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error while sending OTP Notification error -> {ErrorMessage}", ex.Message);
            }
        }
        
        private async Task DoProcessEmailOtpRequest(ProcessEmailOtpRequest message)
        {
            try
            {
                var score = message.Otp.Msisdn.StringToInt(true);
                _ =  _otpCacheRepository.SortedSetRemoveRangeByScore(CommonConstants.RedisOtpKey, score, score);
                _ = await _otpCacheRepository.AddToSortedSet(CommonConstants.RedisOtpKey, score, message.Otp);

                await SendEmailNotificationAsync(message.Otp, senderId: message.Otp.SenderId, appConfig: message.AppConfig);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error while sending OTP Notification error -> {Message}",ex.Message);
            }
        }


        private async Task DoProcessResendOtpRequest(ProcessResendOtpRequest message)
        {

            await SendNotificationAsync(message.Otp, 2);

          

        }

        
        private async Task SendEmailNotificationAsync(ExtendedOtp otp, GetBackOfficeAuthConfigStoreResponse appConfig, string senderId = null)
        {
            
            var notificationData = new NotificationData
            {
                TemplateId = "email_otp_login_request",
                Destination = otp.Email,
                SenderId = string.IsNullOrEmpty(senderId) ? _notificationConfig.SmsFrom : senderId,
                Title = $"{appConfig.ProductName} Login OTP",
                Subtitle = "Verify your login",
                Subject = $"{appConfig.ProductName} Login OTP",
                DisplayEmail = appConfig.ProductEmail,
                CC = new List<string>(),
                Data = new Dictionary<string, object>
                {
                    { "PRIMARY" , appConfig.Primary},
                    { "TEXTCOLOR" , appConfig.Primary},
                    { "BGLIGHT" , appConfig.BgLight},
                    { "PRODUCTNAME" , appConfig.ProductName},
                    { "PRODUCTLOGO" , appConfig.ProductLogo},
                    { "PREFIX" , otp.Prefix},
                    { "CODE" , otp.Code},
                    {"SUPPORTEMAIL",appConfig.ProductEmail }
                }
            };

            await _service.NotifyOptionsAsync(notificationData, new string[] { MobileNotificationOptions.email.ToString() });
        }

        private async Task SendNewNotificationAsync(Otp otp, bool isOtp = true, string message = null, string senderId = null)
        {
            var splitOtp = otp.OtpCode.Split('-');

            var notificationData = new NotificationData
            {
                TemplateId = isOtp ? "centralised_auth_otp_sms" : "recurring_otp_message",
                Destination = otp.Msisdn,
                SenderId = string.IsNullOrEmpty(senderId) ? _notificationConfig.SmsFrom : senderId,
                Title = "Hubtel Login OTP",
                Subtitle = "Verify your login",
                CC = new List<string>(),
                Data = new Dictionary<string, object>
                {
                    { "CODE" , splitOtp[1]},
                    { "PREFIX" , splitOtp[0]},
                    { "MESSAGE" , message},
                    {"otpliabilitypolicy", otp.OtpLiabilityPolicy}
                }
            };

            await _service.NotifyOptionsAsync(notificationData, new[] { MobileNotificationOptions.SMS.ToString() });
        }


        private async Task SendNotificationAsync(Otp otp, int gateway, bool isOtp = true, string message = null)
        {
            var splitOtp = otp.OtpCode.Split('-');

            var notificationData = new NotificationVM
            {
                From = _notificationConfig.SmsFrom,
                To = otp.Msisdn,
                
                Content = isOtp ? string.Format(_notificationConfig.SmsTemplate, splitOtp[1], splitOtp[0]) : string.Format(_notificationConfig.RecurringTemplate, splitOtp[1], splitOtp[0], message),
                Gateway = gateway
            };

            await _service.ProduceMessageAsync(notificationData, NotificationTypes.multiple);
        }
        
    }
}