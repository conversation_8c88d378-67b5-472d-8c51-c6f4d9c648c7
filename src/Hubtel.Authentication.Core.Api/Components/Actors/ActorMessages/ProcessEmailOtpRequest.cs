using Hubtel.Authentication.Commons.Models;
using Hubtel.Authentication.Core.Api.Dtos;

namespace Hubtel.Authentication.Core.Api.Components.Actors.ActorMessages;

public class ProcessEmailOtpRequest
{
    public GetBackOfficeAuthConfigStoreResponse AppConfig;
    public ExtendedOtp Otp { get; }

    public ProcessEmailOtpRequest(ExtendedOtp otp, GetBackOfficeAuthConfigStoreResponse appConfig)
    {
        AppConfig = appConfig;
        Otp = otp;
    }
}