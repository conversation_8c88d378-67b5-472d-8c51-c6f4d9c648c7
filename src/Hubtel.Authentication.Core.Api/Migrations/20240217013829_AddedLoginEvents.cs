using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.Authentication.Core.Api.Migrations
{
    public partial class AddedLoginEvents : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "MetabaseEvents");

            migrationBuilder.CreateTable(
                name: "LoginEvent",
                schema: "MetabaseEvents",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    PhoneNumber = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    Source = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    FingerPrintData = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    App = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    IpAddress = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    ExtraData = table.Column<string>(type: "text", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LoginEvent", x => x.Id);
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "LoginEvent",
                schema: "MetabaseEvents");
        }
    }
}
