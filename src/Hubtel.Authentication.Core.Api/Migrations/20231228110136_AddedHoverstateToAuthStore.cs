using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.Authentication.Core.Api.Migrations
{
    /// <inheritdoc />
    public partial class AddedHoverstateToAuthStore : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "BgLighter",
                table: "AuthenticationConfigurations");

            migrationBuilder.RenameColumn(
                name: "TextColour",
                table: "AuthenticationConfigurations",
                newName: "HoverState");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "HoverState",
                table: "AuthenticationConfigurations",
                newName: "TextColour");

            migrationBuilder.AddColumn<string>(
                name: "BgLighter",
                table: "AuthenticationConfigurations",
                type: "character varying(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "");
        }
    }
}
