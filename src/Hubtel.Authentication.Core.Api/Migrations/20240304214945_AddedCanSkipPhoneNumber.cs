using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.Authentication.Core.Api.Migrations
{
    public partial class AddedCanSkipPhoneNumber : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "RequiresConfig",
                table: "AuthenticationConfigurations",
                newName: "CanSkipPhoneNumber");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "CanSkipPhoneNumber",
                table: "AuthenticationConfigurations",
                newName: "RequiresConfig");
        }
    }
}
