using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.Authentication.Core.Api.Migrations
{
    public partial class AddEmailUrlLookupModel : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "EmailLookUpUrl",
                table: "AuthenticationConfigurations",
                type: "character varying(255)",
                maxLength: 255,
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "EmailLookUpUrl",
                table: "AuthenticationConfigurations");
        }
    }
}
