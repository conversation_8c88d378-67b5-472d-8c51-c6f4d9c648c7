using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.Authentication.Core.Api.Migrations
{
    /// <inheritdoc />
    public partial class AddedValidityPeriodToAuthConfig : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<double>(
                name: "ValidityPeriod",
                table: "AuthenticationConfigurations",
                type: "double precision",
                nullable: false,
                defaultValue: 0.0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ValidityPeriod",
                table: "AuthenticationConfigurations");
        }
    }
}
