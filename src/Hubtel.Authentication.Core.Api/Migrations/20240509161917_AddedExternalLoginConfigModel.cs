using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Hubtel.Authentication.Core.Api.Migrations
{
    public partial class AddedExternalLoginConfigModel : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ExternalLoginConfigurations",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ClientId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    Secret = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    Authority = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    AuthenticationConfigurationId = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ExternalLoginConfigurations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ExternalLoginConfigurations_AuthenticationConfigurations_Au~",
                        column: x => x.AuthenticationConfigurationId,
                        principalTable: "AuthenticationConfigurations",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_ExternalLoginConfigurations_AuthenticationConfigurationId",
                table: "ExternalLoginConfigurations",
                column: "AuthenticationConfigurationId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ExternalLoginConfigurations");
        }
    }
}
