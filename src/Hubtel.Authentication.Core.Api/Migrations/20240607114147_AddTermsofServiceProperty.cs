using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.Authentication.Core.Api.Migrations
{
    public partial class AddTermsofServiceProperty : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "HasNotificationAccess",
                table: "AuthenticationConfigurations",
                newName: "HasPushNotificationAccess");

            migrationBuilder.AddColumn<string>(
                name: "TermsOfServiceUrl",
                table: "AuthenticationConfigurations",
                type: "character varying(255)",
                maxLength: 255,
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "TermsOfServiceUrl",
                table: "AuthenticationConfigurations");

            migrationBuilder.RenameColumn(
                name: "HasPushNotificationAccess",
                table: "AuthenticationConfigurations",
                newName: "HasNotificationAccess");
        }
    }
}
