using System;
using Microsoft.EntityFrameworkCore.Migrations;
// <auto-generated />

#nullable disable

namespace Hubtel.Authentication.Core.Api.Migrations
{
    /// <inheritdoc />
    public partial class Initial : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AuthenticationConfigurations",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ProductName = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    ProductLogo = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    AppName = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    AccountLookUpUrl = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    Primary = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    TextColour = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    SenderId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    BgLight = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    BgLighter = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    SigningKey = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AuthenticationConfigurations", x => x.Id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AuthenticationConfigurations");
        }
    }
}
