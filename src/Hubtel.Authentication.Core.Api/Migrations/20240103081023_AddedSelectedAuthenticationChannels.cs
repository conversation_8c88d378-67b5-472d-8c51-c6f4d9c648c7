using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Hubtel.Authentication.Core.Api.Migrations
{
    /// <inheritdoc />
    public partial class AddedSelectedAuthenticationChannels : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "AuthenticationConfigurations",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.CreateTable(
                name: "SelectedAuthChannels",
                columns: table => new
                {
                    AuthenticationConfigurationId = table.Column<Guid>(type: "uuid", nullable: false),
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    AuthenticationChannel = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SelectedAuthChannels", x => new { x.AuthenticationConfigurationId, x.Id });
                    table.ForeignKey(
                        name: "FK_SelectedAuthChannels_AuthenticationConfigurations_Authentic~",
                        column: x => x.AuthenticationConfigurationId,
                        principalTable: "AuthenticationConfigurations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "SelectedAuthChannels");

            migrationBuilder.DropColumn(
                name: "IsActive",
                table: "AuthenticationConfigurations");
        }
    }
}
