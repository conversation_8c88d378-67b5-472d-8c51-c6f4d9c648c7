using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.Authentication.Core.Api.Migrations
{
    public partial class RemoveAllDuplicationChecks : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "HasPushNotificationAccess",
                table: "AuthenticationConfigurations",
                newName: "HideProductLogo");

            migrationBuilder.AddColumn<string>(
                name: "ScopeId",
                table: "ExternalLoginConfigurations",
                type: "character varying(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "ShowTermsAndConditions",
                table: "AuthenticationConfigurations",
                type: "boolean",
                nullable: false,
                defaultValue: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ScopeId",
                table: "ExternalLoginConfigurations");

            migrationBuilder.DropColumn(
                name: "ShowTermsAndConditions",
                table: "AuthenticationConfigurations");

            migrationBuilder.RenameColumn(
                name: "HideProductLogo",
                table: "AuthenticationConfigurations",
                newName: "HasPushNotificationAccess");
        }
    }
}
