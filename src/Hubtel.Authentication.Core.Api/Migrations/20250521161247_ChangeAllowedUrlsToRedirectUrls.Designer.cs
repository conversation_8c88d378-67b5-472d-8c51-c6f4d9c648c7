// <auto-generated />
using System;
using Hubtel.Authentication.Core.Api.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Hubtel.Authentication.Core.Api.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250521161247_ChangeAllowedUrlsToRedirectUrls")]
    partial class ChangeAllowedUrlsToRedirectUrls
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.11")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Hubtel.Authentication.Commons.Models.Fraud.LoginEvent", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("App")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("ExtraData")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("FingerPrintData")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("IpAddress")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Source")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.HasKey("Id");

                    b.ToTable("LoginEvent", "MetabaseEvents");
                });

            modelBuilder.Entity("Hubtel.Authentication.Core.Api.Data.Entities.AuthenticationConfiguration", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AccountLookUpUrl")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("AllowedRedirectUrls")
                        .HasColumnType("text");

                    b.Property<string>("AppName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Audience")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("BgLight")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<bool>("CanSkipPhoneNumber")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("DefaultChannel")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("EmailLookUpUrl")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<bool>("HideProductLogo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<string>("HoverState")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Issuer")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("OtpLiabilityPolicy")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Primary")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("ProductEmail")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("ProductLogo")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("ProductName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("SenderId")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<bool>("ShowTermsAndConditions")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<string>("SigningKey")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("TermsOfServiceUrl")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp without time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<double>("ValidityPeriod")
                        .HasColumnType("double precision");

                    b.HasKey("Id");

                    b.ToTable("AuthenticationConfigurations");
                });

            modelBuilder.Entity("Hubtel.Authentication.Core.Api.Data.Entities.ExternalLoginConfiguration", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<Guid?>("AuthenticationConfigurationId")
                        .HasColumnType("uuid");

                    b.Property<string>("Authority")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("ClientId")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<bool>("DisablePhoneLookup")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<string>("ScopeId")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Secret")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("AuthenticationConfigurationId");

                    b.ToTable("ExternalLoginConfigurations");
                });

            modelBuilder.Entity("Hubtel.Authentication.Core.Api.Data.Entities.AuthenticationConfiguration", b =>
                {
                    b.OwnsMany("Hubtel.Authentication.Core.Api.Data.Entities.SelectedAuthChannel", "AuthenticationChannels", b1 =>
                        {
                            b1.Property<Guid>("AuthenticationConfigurationId")
                                .HasColumnType("uuid");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b1.Property<int>("Id"));

                            b1.Property<int>("AuthenticationChannel")
                                .HasColumnType("integer");

                            b1.HasKey("AuthenticationConfigurationId", "Id");

                            b1.ToTable("SelectedAuthChannels");

                            b1.WithOwner()
                                .HasForeignKey("AuthenticationConfigurationId");
                        });

                    b.OwnsMany("Hubtel.Authentication.Core.Api.Data.Entities.WhiteList", "WhiteLists", b1 =>
                        {
                            b1.Property<Guid>("AuthenticationConfigurationId")
                                .HasColumnType("uuid");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b1.Property<int>("Id"));

                            b1.Property<string>("Otp")
                                .HasMaxLength(10)
                                .HasColumnType("character varying(10)");

                            b1.Property<string>("PhoneNumber")
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)");

                            b1.HasKey("AuthenticationConfigurationId", "Id");

                            b1.ToTable("WhiteLists");

                            b1.WithOwner()
                                .HasForeignKey("AuthenticationConfigurationId");
                        });

                    b.Navigation("AuthenticationChannels");

                    b.Navigation("WhiteLists");
                });

            modelBuilder.Entity("Hubtel.Authentication.Core.Api.Data.Entities.ExternalLoginConfiguration", b =>
                {
                    b.HasOne("Hubtel.Authentication.Core.Api.Data.Entities.AuthenticationConfiguration", null)
                        .WithMany("ExternalLoginConfigurations")
                        .HasForeignKey("AuthenticationConfigurationId");
                });

            modelBuilder.Entity("Hubtel.Authentication.Core.Api.Data.Entities.AuthenticationConfiguration", b =>
                {
                    b.Navigation("ExternalLoginConfigurations");
                });
#pragma warning restore 612, 618
        }
    }
}
