using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hubtel.Authentication.Core.Api.Migrations
{
    /// <inheritdoc />
    public partial class AddedSoftDeleteImplementation : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<DateTime>(
                name: "UpdatedAt",
                table: "AuthenticationConfigurations",
                type: "timestamp without time zone",
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "timestamp without time zone");

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "AuthenticationConfigurations",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "AuthenticationConfigurations");

            migrationBuilder.AlterColumn<DateTime>(
                name: "UpdatedAt",
                table: "AuthenticationConfigurations",
                type: "timestamp without time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                oldClrType: typeof(DateTime),
                oldType: "timestamp without time zone",
                oldNullable: true);
        }
    }
}
