namespace Hubtel.Authentication.Core.Api.Options
{
    public class JwtConfig
    {
        /// <summary>
        ///
        /// </summary>
        public string Audience { get; set; }

        /// <summary>
        ///
        /// </summary>
        public string Issuer { get; set; }

        /// <summary>
        ///
        /// </summary>
        public string SigningKey { get; set; }

        /// <summary>
        ///
        /// </summary>
        public double ValidityPeriod { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public double MobileValidityPeriod { get; set; }
    }
}
