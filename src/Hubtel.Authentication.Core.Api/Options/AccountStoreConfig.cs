using System;
using System.Text.Json.Serialization;

namespace Hubtel.Authentication.Core.Api.Options;

/// <summary>
/// Represents the configuration settings for an account store.
/// </summary>
public class AccountStoreConfig
{
    /// <summary>
    /// Gets or sets the name of the product.
    /// </summary>
    /// <value>
    /// The name of the product.
    /// </value>
    public string ProductName { get; set; }

    /// <summary>
    /// Gets or sets the logo of the product.
    /// </summary>
    /// <value>
    /// The logo of the product.
    /// </value>
    public string ProductLogo { get; set; }

    /// <summary>
    /// Gets or sets the name of the client application.
    /// </summary>
    /// <value>
    /// A string representing the name of the client application.
    /// </value>
    public string AppName { get; set; }

    /// <summary>
    /// Gets or sets the URL used for account lookup.
    /// </summary>
    /// <value>
    /// The account lookup URL.
    /// </value>
    [JsonIgnore]
    public string AccountLookUpUrl { get; set; }

    /// <summary>
    /// Gets or sets the unique identifier for the application.
    /// </summary>
    /// <value>
    /// The unique identifier for the object.
    /// </value>
    public Guid Id { get; set; }

    /// <summary>
    /// Gets or sets the primary colour.
    /// </summary>
    /// <value>
    /// The primary colour.
    /// </value>
    public string Primary { get; set; }

    /// <summary>
    /// Gets or sets the secondary colour of the client application.
    /// </summary>
    /// <value>
    /// The secondary colour for the client application.
    /// </value>
    public string TextColour { get; set; }

    /// <summary>
    /// Gets or sets the sender ID of the client application.
    /// </summary>
    /// <value>
    /// The sender ID.
    /// </value>
    [JsonIgnore]
    public string SenderId { get; set; }

    /// <summary>
    /// Gets or sets the value for the BgLight property.
    /// </summary>
    /// <value>
    /// The value for the BgLight property.
    /// </value>
    public string BgLight { get; set; }

    /// <summary>
    /// Gets or sets the value of the BgLighter property.
    /// </summary>
    /// <value>
    /// The value of the BgLighter property.
    /// </value>
    public string BgLighter { get; set; }

    /// <summary>
    /// Gets or sets the signing key.
    /// </summary>
    /// <value>
    /// The signing key.
    /// </value>
    public string SigningKey { get; set; }
}