using System;
using System.Threading;
using System.Threading.Channels;
using System.Threading.Tasks;
using Hubtel.Authentication.Commons.Models.Fraud;
using Hubtel.Authentication.Core.Api.Services.Interface;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Hubtel.Authentication.Core.Api.Data;
using Mapster;
using Microsoft.Extensions.DependencyInjection;
using System.Diagnostics.CodeAnalysis;

namespace Hubtel.Authentication.Core.Api.BgWorker;

/// <summary>
/// Service for dispatching login events.
/// </summary>
public class LoginEventsWorker:BackgroundService
{
    /// <summary>
    /// Represents a service for interacting with Apache Kafka.
    /// </summary>
    private readonly IKafkaService _kafkaService;

    /// <summary>
    /// The configuration object used by the LoginEventsDispatchService class. </summary>
    /// /
    private readonly IConfiguration _configuration;

    /// <summary>
    /// Represents the service provider used for dependency injection.
    /// </summary>
    private readonly IServiceProvider _serviceProvider;

    /// <summary>
    /// Represents the ChannelReader used to read FraudReports for processing.
    /// </summary>
    private readonly ChannelReader<FraudReport> _fraudChannel;

    /// <summary>
    /// Background service responsible for dispatching login events to Kafka.
    /// </summary>
    public LoginEventsWorker(IKafkaService kafkaService, IConfiguration configuration,Channel<FraudReport> fraudChannel,IServiceProvider serviceProvider)
    {
        _kafkaService = kafkaService;
        _configuration = configuration;
        _serviceProvider = serviceProvider;
        _fraudChannel = fraudChannel.Reader;
    }

    /// <summary>
    /// Executes the async operation to dispatch login events.
    /// </summary>
    /// <param name="stoppingToken">The cancellation token that can be used to cancel the operation.</param>
    /// <returns>A Task representing the asynchronous operation.</returns>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
       
        
        var fraudSignInLogTopic = _configuration["KafkaExtra:FraudSignInLogTopic"];
        await foreach (var message in _fraudChannel.ReadAllAsync(stoppingToken))
        {
            if (string.IsNullOrWhiteSpace(message.App)&&string.IsNullOrWhiteSpace(message.Source)&&string.IsNullOrWhiteSpace(message.IpAddress)&&string.IsNullOrWhiteSpace(message.PhoneNumber))
            {
                continue;
            }
            _ = _kafkaService.ProduceMessageAsync(message, fraudSignInLogTopic);
            using var scope = _serviceProvider.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            var loginEvent = message.Adapt<LoginEvent>();
            await  dbContext.LoginEvents.AddAsync(loginEvent!, stoppingToken);
            await dbContext.SaveChangesAsync(stoppingToken);
        }
    }
}