using System;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Akka.Actor;
using Akka.Hosting;
using Akka.Routing;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using StackExchange.Redis;
using Amazon.CognitoIdentityProvider;
using Amazon;
using Hubtel.Authentication.Core.Api.Components.Actors;
using Hubtel.Authentication.Core.Api.Components.Authentication;
using Hubtel.Authentication.Core.Api.Data;
using Hubtel.Authentication.Core.Api.Options;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.Routing;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Hubtel.Authentication.Core.Api.Data.Entities;
using Hubtel.Authentication.Core.Api.Dtos;
using Mapster;

namespace Hubtel.Authentication.Core.Api.Extensions
{
    public static class ServiceCollectionExtension
    {
        public static async Task RunMigrationsAsync(this IHost app)
        {
            using var scope = app.Services.CreateScope();
            var logger = app.Services.GetRequiredService<ILogger<Program>>();
            var services = scope.ServiceProvider;
            try
            {
                var storageContext = services.GetRequiredService<ApplicationDbContext>();
                var pendingMigrations = await storageContext.Database.GetPendingMigrationsAsync();
                var count = pendingMigrations.Count();
                if (count > 0)
                {
                    logger.LogInformation("found {Count} pending migrations to apply. will proceed to apply them",
                        count);
                    await storageContext.Database.MigrateAsync();
                    logger.LogInformation("done applying pending migrations");
                }
                else
                {
                    logger.LogInformation("no pending migrations found! :)");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while performing migration -> {ErrorMessage}", ex.Message);
            }
        }

        public static string ToRedactedNumber(this string number)
        {
            var numberStr = number;

            // Ensure the number has a sufficient length to be masked
            if (numberStr.Length < 10)
            {
                throw new ArgumentException("Number is too short to be masked", nameof(number));
            }

            var visibleStart = numberStr.Substring(0, 6); // First six digits
            var visibleEnd = numberStr.Substring(numberStr.Length - 2); // Last two digits
            var maskedPart = new string('*', numberStr.Length - 8); // Masked part

            return $"{visibleStart}{maskedPart}{visibleEnd}";
        } 
        public static bool IsInternationalNumber(this string number)
        {
            return !(number.StartsWith("+233") || number.StartsWith("233"));

        }          


        public static string ToRedactedEmail(this string email)
        {
            var emailPattern = @"^([a-zA-Z0-9_\-\.]+)@((\[[0-9]{1,3}" +
                               @"\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([a-zA-Z0-9\-]+\" +
                               @".)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$";
            var regex = new Regex(emailPattern, RegexOptions.None, TimeSpan.FromMilliseconds(100));

            if (!regex.IsMatch(email))
            {
                return string.Empty;
            }

            var parts = email.Split('@');
            if (parts.Length != 2)
            {
                return string.Empty;
            }

            var localPart = MaskPart(parts[0]);
            var domainSplit = parts[1].Split('.');
            var domainPart = MaskPart(domainSplit[0]);

            return $"{localPart}@{domainPart}.{domainSplit[1]}";
        }

        private static string MaskPart(string part)
        {
            if (string.IsNullOrEmpty(part) || part.Length < 2)
            {
                return "*";
            }

            return $"{part[0]}{new string('*', part.Length - 1)}";
        }

        public static IServiceCollection AddCognitoClient(this IServiceCollection services, AWSConfig config)
        {
            services.Configure<AWSConfig>(c =>
            {
                c.AccessKey = config.AccessKey;
                c.AccessSecret = config.AccessSecret;
                c.ClientID = config.ClientID;
                c.PoolID = config.PoolID;
            });
            var cognito =
                new AmazonCognitoIdentityProviderClient(config.AccessKey, config.AccessSecret, RegionEndpoint.EUWest1);

            services.AddSingleton(cognito);

            return services;
        }

        public static IServiceCollection AddActorSystem(this IServiceCollection services, string actorSystemName)
        {
            if (services == null)
            {
                throw new ArgumentNullException(nameof(services));
            }

            services.AddAkka(actorSystemName, (configurationBuilder =>
            {
                configurationBuilder.WithActors((system, registry, resolver) =>
                {
                    var defaultStrategy = new OneForOneStrategy(
                        3, TimeSpan.FromSeconds(3), ex =>
                        {
                            if (ex is not ActorInitializationException)
                                return Directive.Resume;

                            system?.Terminate().Wait(1000);

                            return Directive.Stop;
                        });
                    var externalApiActorProps = resolver
                        .Props<ExternalApiActor>()
                        .WithRouter(new RoundRobinPool(1, new DefaultResizer(1, 5)))
                        .WithSupervisorStrategy(defaultStrategy);
                    var externalApiActor = system.ActorOf(externalApiActorProps, nameof(ExternalApiActor));
                    registry.Register<ExternalApiActor>(externalApiActor);

                    var mainActorProps = resolver
                        .Props<MainActor>()
                        .WithSupervisorStrategy(defaultStrategy);
                    var mainActor = system.ActorOf(mainActorProps, nameof(MainActor));
                    registry.Register<MainActor>(mainActor);
                });
            }));
            return services;
        }


        public static IServiceCollection AddRedisStorage(this IServiceCollection services,
            InMemoryConfig configureOptions)
        {
            if (services == null)
            {
                throw new ArgumentNullException(nameof(services));
            }

            services.Configure<InMemoryConfig>(c =>
            {
                c.Port = configureOptions.Port;
                c.Database = configureOptions.Database;
                c.OtpDatabase = configureOptions.OtpDatabase;
                c.Server = configureOptions.Server;
                c.BaseKey = configureOptions.BaseKey;
            });
            var configuration = ConfigurationOptions.Parse($"{configureOptions.Server}:{configureOptions.Port}", true);
            configuration.ResolveDns = false;
            configuration.AbortOnConnectFail = false;
            configuration.AllowAdmin = true;
            configuration.DefaultDatabase = configureOptions.Database;
            configuration.ReconnectRetryPolicy = new LinearRetry(500);


            var connection = ConnectionMultiplexer.Connect(configuration);

            connection.GetDatabase();
            connection.GetDatabase(configureOptions.OtpDatabase);

            return services;
        }

        public static IServiceCollection EnableAuthentication(this IServiceCollection services
            , bool enableBasic = false, bool enablePrivateKey = false, bool enableBearer = false,
            Action<BearerTokenConfig> bearerTokenConfigAction = null)
        {
            if (services == null)
            {
                throw new ArgumentNullException(nameof(services));
            }


            if (enableBearer)
            {
                if (bearerTokenConfigAction == null)
                {
                    throw new ArgumentNullException(nameof(bearerTokenConfigAction));
                }


                var bearerConfig = new BearerTokenConfig();
                bearerTokenConfigAction.Invoke(bearerConfig);
                services.AddAuthentication(x =>
                    {
                        x.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                        x.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
                    })
                    .AddJwtBearer(x =>
                    {
                        x.SaveToken = true;
                        x.TokenValidationParameters = new TokenValidationParameters
                        {
                            ValidateIssuer = true,
                            ValidIssuer = bearerConfig.Issuer,
                            ValidateIssuerSigningKey = true,
                            IssuerSigningKey =
                                new SymmetricSecurityKey(Encoding.UTF8.GetBytes(bearerConfig.SigningKey)),
                            ValidAudience = bearerConfig.Audience,
                            ValidateAudience = true,
                            ValidateLifetime = false,
                            //  ValidAlgorithms = new[] {SecurityAlgorithms.HmacSha256Signature},
                            //  ClockSkew = TimeSpan.FromMinutes(1)
                        };
                    });
            }

            if (enableBasic)
            {
                services.AddAuthentication("Basic").AddScheme<AuthenticationSchemeOptions, BasicAuthHandler>("Basic",
                    null);
            }

            if (enablePrivateKey)
            {
                services.AddAuthentication("PrivateKey").AddScheme<AuthenticationSchemeOptions, PrivateAuthHandler>(
                        "PrivateKey",
                        null)
                    ;
            }

            return services;
        }

        /// <summary>
        /// Adds Hubtel SignalR to the specified service collection.
        /// </summary>
        /// <param name="serviceCollection">The service collection to add Hubtel SignalR to.</param>
        /// <param name="hubOptions">The delegate used to configure Hubtel options.</param>
        /// <returns>The modified service collection.</returns>
        public static IServiceCollection AddHubtelSignalR(this IServiceCollection serviceCollection,
            Action<HubOptions> hubOptions)
        {
            serviceCollection.AddSignalR(hubOptions);
            return serviceCollection;
        }

        /// <summary>
        /// Maps the specified Hub class to the given route in the Signal
        /// R pipeline.
        /// </summary>
        /// <typeparam name="THub">The type of the Hub.</typeparam>
        /// <param name="endpoints">The <see cref="IEndpointRouteBuilder"/> to map the Hub to.</param>
        /// <param name="route">The route at which the Hub will be mapped.</param>
        /// <param name="connectionDispatcherOptions">An action to configure the <see cref="HttpConnectionDispatcherOptions"/>.</param>
        /// <returns>The <see cref="IEndpointRouteBuilder"/> to chain additional method calls.</returns>
        public static IEndpointRouteBuilder MapHubtelSignalRHub<THub>(this IEndpointRouteBuilder endpoints,
            string route,
            Action<HttpConnectionDispatcherOptions> connectionDispatcherOptions) where THub : Hub
        {
            endpoints.MapHub<THub>(route, connectionDispatcherOptions);
            return endpoints;
        }

        public static void AddMapsterCustomConfigurations(this IServiceCollection services)
        {
            TypeAdapterConfig<AuthenticationConfiguration, GetAuthConfigStoreResponse>
            .NewConfig()
            .Map(dest => dest.Channels,      
                src => src.AuthenticationChannels.Select(x => x.AuthenticationChannel))
            .Map(dest => dest.ExternalChannels,
            src => src.ExternalLoginConfigurations);

            TypeAdapterConfig<AuthenticationConfiguration, GetBackOfficeAuthConfigStoreResponse>
            .NewConfig()
            .Map(dest => dest.Channels,
                src => src.AuthenticationChannels.Select(x => x.AuthenticationChannel))
            .Map(dest => dest.ExternalChannels,
            src => src.ExternalLoginConfigurations);
        }

       
    }
}