using System;
using System.Collections.Generic;
using System.Linq;
using Hubtel.Authentication.Core.Api.Models;
using Microsoft.AspNetCore.Mvc;

namespace Hubtel.Authentication.Core.Api.Extensions
{
    /// <summary>
    /// Provides extension methods for working with ApiResponse objects.
    /// </summary>
    public static class ApiResponseExtensions
    {
        /// <summary>
        /// Returns a collection of BadRequestModel objects representing errors in the given ActionContext's ModelState.
        /// </summary>
        /// <param name="actionContext">The ActionContext instance to retrieve ModelState errors from.</param>
        /// <returns>A collection of BadRequestModel objects representing the errors in the ModelState of the ActionContext.</returns>
        public static IEnumerable<BadRequestModel> GetError(this ActionContext actionContext)
        {
            return actionContext.ModelState
                .Where(modelError => modelError.Value.Errors.Count > 0)
                .Select(modelError => new BadRequestModel
                {
                    Field = modelError.Key,
                    ErrorMessage = modelError.Value.Errors.FirstOrDefault()?.ErrorMessage
                });
        }


        /// <summary>
        /// Retrieves a paged result from the given query.
        /// </summary>
        /// <typeparam name="T">The type of elements in the query.</typeparam>
        /// <param name="query">The query to be paged.</param>
        /// <param name="page">The page number to retrieve.</param>
        /// <param name="pageSize">The number of elements per page.</param>
        /// <returns>A <see cref="PagedResult{T}"/> object containing the paged result.</returns>
        public static PagedResult<T> GetPaged<T>(this IEnumerable<T> query, int page, int pageSize) where T : class
        {
            var result = new PagedResult<T>
            {
                PageIndex = page,
                PageSize = pageSize,
                TotalCount = (int)query.LongCount()
            };

            var pageCount = (double)result.TotalCount / pageSize;
            result.TotalPages = (int)Math.Ceiling(pageCount);

            result.Results = query.Skip((page - 1) * pageSize).Take(pageSize).ToList();

            return result;
        }
    }
}