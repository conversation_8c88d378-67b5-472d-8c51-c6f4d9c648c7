using System.Linq;
using Hubtel.Authentication.Core.Api.Data.Entities;
using Hubtel.Authentication.Core.Api.Dtos;
using Microsoft.EntityFrameworkCore;

namespace Hubtel.Authentication.Core.Api.Extensions;

/// <summary>
/// Provides extension methods for querying the <see cref="AuthenticationConfiguration"/> entity.
/// </summary>
public static class QueryExtensions
{
    /// <summary>
    /// Filters the query based on the active flag for back office.
    /// </summary>
    /// <typeparam name="T">The type of AuthenticationConfiguration.</typeparam>
    /// <param name="query">The query to filter.</param>
    /// <param name="dto">The type of DTO.</param>
    /// <returns>The filtered query.</returns>
    public static IQueryable<T> IgnoreActiveFlagForBackOffice<T>(this IQueryable<T> query, System.Type dto)
        where T : AuthenticationConfiguration
    {
        if (dto!= typeof(GetBackOfficeAuthConfigStoreResponse))
        {
            query = query.Where(x => x.IsActive).Include(x=>x.WhiteLists);
        }

        return query;
    }
}