using Hubtel.Authentication.Core.Api.Models.Responses;
using System.Collections.Generic;

namespace Hubtel.Authentication.Core.Api.Extensions
{
    public static class ApplicationExtensions
    {
        public static T FilterForEmailOnly<T>(this T lookupData) where T : BaseLookupData
        {
            if( lookupData == null)
            {
                return null;
            }
            if (lookupData.TokenData == null)
            {
                return lookupData;
            }
            if (lookupData.TokenData.TryGetValue("email", out var email))
            {
                lookupData.TokenData = new Dictionary<string, string>
                {
                    { "email", email }
                };
                return lookupData;
            }
            lookupData.TokenData.Clear();
            return lookupData;
        }
    }
}
