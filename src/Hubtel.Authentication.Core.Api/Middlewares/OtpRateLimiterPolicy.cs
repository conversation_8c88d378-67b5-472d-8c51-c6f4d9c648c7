using Hubtel.Authentication.Core.Api.Models;
using Hubtel.Authentication.Core.Api.Models.AppModels;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.RateLimiting;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.RateLimiting;
using System.Threading.Tasks;

namespace Hubtel.Authentication.Core.Api.Middlewares
{
    public class OtpRateLimiterPolicy : IRateLimiterPolicy<string>
    {
        private readonly Func<OnRejectedContext, CancellationToken, ValueTask> _onRejected;
        private readonly ILogger<OtpRateLimiterPolicy> _logger;
        private string _partitionKey;

        public Func<OnRejectedContext, CancellationToken, ValueTask> OnRejected => _onRejected!;

        public OtpRateLimiterPolicy(ILogger<OtpRateLimiterPolicy> logger)
        {
            _logger = logger;
            _onRejected = async (ctx, token) =>
             {
                 if (ctx.HttpContext.Items.ContainsKey("RateLimitParseError"))
                 {
                     ctx.HttpContext.Response.StatusCode = StatusCodes.Status400BadRequest;
                     var apiResponse = new ApiResponse<EmptyDto>(AppResponses.InvalidPayload, null)
                     {
                         Code = StatusCodes.Status400BadRequest.ToString()
                     };
                     await ctx.HttpContext.Response.WriteAsJsonAsync(apiResponse, token);
                     _logger.LogWarning("Bad request payload on {Endpoint}", ctx.HttpContext.Request.Path);
                 }
                 else
                 {
                     ctx.HttpContext.Response.StatusCode = StatusCodes.Status429TooManyRequests;
                     var apiResponse = new ApiResponse<EmptyDto>(AppResponses.TooManyRequests, null)
                     {
                         Code = StatusCodes.Status429TooManyRequests.ToString()
                     };
                     await ctx.HttpContext.Response.WriteAsJsonAsync(apiResponse, token);
                     _logger.LogWarning("Too many requests by {PhoneNumber} on {Endpoint}", _partitionKey, ctx.HttpContext.Request.Path);
                 }
             };

        }
        public RateLimitPartition<string> GetPartition(HttpContext httpContext)
        {
            string body = string.Empty;
            try
            {
                httpContext.Request.EnableBuffering();
                using var memoryStream = new MemoryStream();
                httpContext.Request.Body.CopyToAsync(memoryStream);
                memoryStream.Position = 0;
                body = Encoding.UTF8.GetString(memoryStream.ToArray());
                Dictionary<string, object> dictionary = JsonSerializer.Deserialize<Dictionary<string, object>>(body);
                if (dictionary != null)
                {
                    object phoneNumber = dictionary.GetValueOrDefault("phoneNumber") ?? dictionary.GetValueOrDefault("PhoneNumber");
                    object appId = dictionary.GetValueOrDefault("appId") ?? dictionary.GetValueOrDefault("AppId");

                    ArgumentNullException.ThrowIfNull(phoneNumber);
                    ArgumentNullException.ThrowIfNull(appId);
                    _partitionKey = $"{phoneNumber}_{appId}";
                }

                httpContext.Request.Body.Position = 0;
                return RateLimitPartition.GetFixedWindowLimiter(_partitionKey, _ => new FixedWindowRateLimiterOptions
                {
                    PermitLimit = 5,
                    Window = TimeSpan.FromMinutes(10),
                    QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                    QueueLimit = 0
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting rate limit partition {Payload}", body);
                httpContext.Items["RateLimitParseError"] = true;

                return RateLimitPartition.GetFixedWindowLimiter("invalid", _ => new FixedWindowRateLimiterOptions
                {
                    PermitLimit = 1,
                    Window = TimeSpan.FromMinutes(1),
                    QueueLimit = 0
                });

            }
        }
    }
}
