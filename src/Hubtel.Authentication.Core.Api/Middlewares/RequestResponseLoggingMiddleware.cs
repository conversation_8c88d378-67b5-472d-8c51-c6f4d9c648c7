using System;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Hubtel.Authentication.Core.Api.Middlewares
{
    public class RequestResponseLoggingMiddleware
    {
        private readonly ILogger<RequestResponseLoggingMiddleware> _logger;
        private readonly RequestDelegate _next;

        public RequestResponseLoggingMiddleware(RequestDelegate next, ILogger<RequestResponseLoggingMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task Invoke(HttpContext context)
        {
            var request = await FormatRequest(context.Request);

            _logger.LogInformation("Request: {Request}", request);

            var originalBodyStream = context.Response.Body;

            using (var responseBody = new MemoryStream())
            {
                context.Response.Body = responseBody;
                await _next(context);
                var response = await FormatResponse(context.Response);
                _logger.LogInformation("Response: {Response}", response);
                await responseBody.CopyToAsync(originalBodyStream);
            }
        }

        private static async Task<string> FormatRequest(HttpRequest request)
        {
            //This line allows us to set the reader for the request back at the beginning of its stream.
            request.EnableBuffering();

            //We now need to read the request stream.  First, we create a new byte[] with the same length as the request stream...
            var buffer = new byte[Convert.ToInt32(request.ContentLength)];

            //...Then we copy the entire request stream into the new buffer.
            await request.Body.ReadAsync(buffer, 0, buffer.Length).ConfigureAwait(false);

            //We convert the byte[] into a string using UTF8 encoding...
            var bodyAsText = Encoding.UTF8.GetString(buffer);

            //..and finally, assign the read body back to the request body, which is allowed because of EnableRewind()
            request.Body.Position = 0;

            var sb = new StringBuilder();
            foreach (var requestHeader in request.Headers)
            {
                sb.Append($"{requestHeader.Key}={requestHeader.Value}");
                sb.AppendLine();
            }

            var log = $"Request:\r\nUrl: {request.Scheme}://{request.Host}{request.Path} {request.QueryString} " +
                      $"\r\nMethod: {request.Method}" +
                      $"\r\nCLIENT IP: {request.HttpContext.Connection.RemoteIpAddress}" +
                      $"\r\nCLIENT Port: {request.HttpContext.Connection.RemotePort}" +
                      $"\r\nConnection ID: {request.HttpContext.Connection.Id}" +
                      $"\r\nHeaders:\r\n{sb}" +
                      $"\r\nBody:\r\n{bodyAsText}" +
                      "\r\n";
            return log;
        }

        private static async Task<string> FormatResponse(HttpResponse response)
        {
            //We need to read the response stream from the beginning...
            response.Body.Seek(0, SeekOrigin.Begin);

            //...and copy it into a string
            var text = await new StreamReader(response.Body).ReadToEndAsync();

            //We need to reset the reader for the response so that the client can read it.
            response.Body.Seek(0, SeekOrigin.Begin);

            //Return the string for the response, including the status code (e.g. 200, 404, 401, etc.)
            var sb = new StringBuilder();
            foreach (var requestHeader in response.Headers)
            {
                sb.Append($"{requestHeader.Key}={requestHeader.Value}");
                sb.AppendLine();
            }

            return $"Response:\r\nHeaders:\r\n{sb}\r\nBody:\r\n{text}";
        }
    }
}