using System.Net;
using Hubtel.Authentication.Core.Api.Models;
using JustEat.StatsD;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace Hubtel.Authentication.Core.Api.Middlewares
{
    public static class PipelineMiddlewareExtensions
    {
        public static void ConfigureExceptionHandler(this IApplicationBuilder app, ILogger<Program> logger,
            IStatsDPublisher metrics)
        {
            app.UseExceptionHandler(appError =>
            {
                appError.Run(async context =>
                {
                    context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                    context.Response.ContentType = "application/json";

                    var contextFeature = context.Features.Get<IExceptionHandlerFeature>();
                    if (contextFeature != null)
                    {
                        metrics.Increment("internal_errors");
                        logger.LogError(contextFeature.Error, "Something went wrong: {ErrorMessage}", contextFeature.Error?.Message);

                        var respJson = JsonConvert.SerializeObject(new ApiResponse<EmptyDto>()
                        {
                            Message =
                                "Ooops, something really bad happened. Please try again later.",
                        }, new JsonSerializerSettings
                        {
                            ContractResolver = new CamelCasePropertyNamesContractResolver()
                        });

                        context.Response.ContentLength = respJson.Length;

                        await context.Response.WriteAsync(respJson);
                    }
                });
            });
        }
    }
}