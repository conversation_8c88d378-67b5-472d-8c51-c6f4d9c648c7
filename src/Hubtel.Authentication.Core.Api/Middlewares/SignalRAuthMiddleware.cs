using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;

namespace Hubtel.Authentication.Core.Api.Middlewares;

public class SignalRAuthMiddleware : IMiddleware
{
    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        // web sockets cannot pass headers so we must take the access token from query param and
        // add it to the header before authentication middleware runs
        if (context.Request.Path.StartsWithSegments("/signalrAuth", StringComparison.InvariantCultureIgnoreCase))
        {
            context.Request.Query.TryGetValue("access_token", out var accessToken);
            context.Request.Headers.Authorization = $"Basic {accessToken.ToString()}";
        }

        await next(context);
    }
}