using System;
using System.Collections.Generic;
using System.Threading.Channels;
using System.Threading.Tasks;
using Hubtel.Authentication.Commons.Models.Fraud;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace Hubtel.Authentication.Core.Api.Middlewares;

/// <summary>
/// Middleware for logging login events to a Kafka service.
/// </summary>
public class LoginEventsMiddleware : IMiddleware
{
    /// <summary>
    /// This variable represents an array of strings that contains the allowable paths.
    /// The paths represented in this array are considered to be valid within the context of the code that uses it.
    /// The variable is marked as 'readonly' to ensure that the array is not modified after initialization.
    /// </summary>
    /// <remarks>
    /// It is important to ensure that the values stored in this array are correctly defined and maintained,
    /// in order to avoid unexpected runtime behavior or security vulnerabilities.
    /// </remarks>
    private readonly List<string> _excludedPaths;

    /// <summary>
    /// The writer for the fraud reports.
    /// </summary>
    /// <remarks>
    /// This variable is used in the LoginEventsMiddleware class to write fraud reports to a channel.
    /// </remarks>
    private readonly ChannelWriter<FraudReport> _fraudReportWriter;

    /// <summary>
    /// Represents a logger for the LoginEventsMiddleware class.
    /// </summary>
    private readonly ILogger<LoginEventsMiddleware> _logger;

    /// <summary>
    /// Middleware for logging login events to a Kafka service.
    /// </summary>
    public LoginEventsMiddleware(Channel<FraudReport> fraudReportChannel, IOptions<List<string>> allowablePaths,
        IConfiguration configuration, ILogger<LoginEventsMiddleware> logger)
    {
        _excludedPaths = allowablePaths.Value;
        _fraudReportWriter = fraudReportChannel.Writer;
        _logger = logger;
    }

    /// <summary>
    /// Invokes the given <see cref="RequestDelegate"/> after performing some analytics and logging operations.
    /// </summary>
    /// <param name="context">The <see cref="HttpContext"/> containing the request and response objects.</param>
    /// <param name="next">The next middleware delegate.</param>
    /// <returns>A task that represents the asynchronous operation.</returns>
    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        if (!_excludedPaths.Contains(context.Request.Path.Value))
        {
            var headers = context.Request.Headers;
            _logger.LogDebug("Processing the incoming request headers {Headers}",
                JsonConvert.SerializeObject(headers));

            string GetHeaderValue(string key) => headers.TryGetValue(key, out var header) ? header.ToString() : "";   

            var ipAddress = GetHeaderValue("X-Ip-Address");
            var phoneNumber = GetHeaderValue("x-Phone-Number");
            var fingerPrintData = GetHeaderValue("x-Fingerprint-Data");
            var latitude = GetHeaderValue("X-Lat");
            var longitude = GetHeaderValue("X-Lng");
            var location = GetHeaderValue("X-Location");
            var clientType = GetHeaderValue("X-Client-Type");
            var clientName = GetHeaderValue("X-Client-Name");
            var clientVersion = GetHeaderValue("X-Client-Version");
            var clientEngine = GetHeaderValue("X-Client-Engine");
            var clientEngineVersion = GetHeaderValue("X-Client-Engine-Version");
            var userAgent = GetHeaderValue("X-User-Agent");
            var appId = GetHeaderValue("X-App-Id");
            var appName = GetHeaderValue("X-App-Name");
            var appVersion = GetHeaderValue("X-App-Version");
            var osPlatform = GetHeaderValue("X-Os-Platform");
            var osVersion = GetHeaderValue("X-Os-Version");
            var osName = GetHeaderValue("X-Os-Name");
            var deviceName = GetHeaderValue("X-Device-Name");
            var deviceType = GetHeaderValue("x-Device-Type");
            var deviceBrand = GetHeaderValue("X-Device-Brand");
            var deviceModel = GetHeaderValue("X-Device-Model");
            var eventName = GetHeaderValue("X-Event-Name");
            
            
           

            var fraudReport = new FraudReport(
                phoneNumber,
                appName,
                fingerPrintData,
                appId,
                ipAddress,
                new Dictionary<string, object>
                {
                    {
                        nameof(Client), new Client(
                            clientType,
                            clientName,
                            clientVersion,
                            clientEngine,
                            clientEngineVersion
                        )
                    },
                    {
                        nameof(Os), new Os(
                            osName,
                            osVersion,
                            osPlatform
                        )
                    },
                    {
                        nameof(Device), new Device(
                            deviceType,
                            deviceBrand,
                            deviceModel
                        )
                    },
                    {
                        nameof(userAgent), userAgent
                    },
                    {
                        nameof(appVersion), appVersion
                    },
                    {
                        nameof(deviceName), deviceName
                    },
                    {
                        nameof(eventName), eventName
                    },
                    {
                        nameof(latitude), latitude
                    },
                    {
                        nameof(longitude), longitude
                    },
                    {
                        nameof(location), location
                    }
                },
                DateTime.UtcNow
            );
            _logger.LogDebug("Creating FraudReport for phoneNumber: {PhoneNumber}, appName: {AppName} with payload: {Payload}",
                phoneNumber, appName,JsonConvert.SerializeObject(fraudReport));
           _ = _fraudReportWriter.TryWrite(fraudReport);

        }
        else
        {
            _logger.LogDebug("Skipping log for excluded path {Path}", context.Request.Path);
        }

        _logger.LogDebug("Invoking the next middleware in the pipeline");


        await next.Invoke(context);
    }
}