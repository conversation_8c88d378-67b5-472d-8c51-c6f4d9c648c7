using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text.Json;
using Gelf.Extensions.Logging;
using Hubtel.Instrumentation.Extensions;
using Hubtel.Kafka.Host.Extensions;
using Hubtel.Redis.Sdk.Extensions;
using Hubtel.Redis.Sdk.Options;
using Hubtel.Authentication.Core.Api.Extensions;
using JustEat.StatsD;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using Hubtel.PhoneNumbers.Extensions;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Channels;
using Hubtel.Authentication.Commons.Models.Fraud;
using Hubtel.Authentication.Core.Api.Data;
using Hubtel.Authentication.Core.Api.Middlewares;
using Hubtel.Authentication.Core.Api.Models;
using Hubtel.Authentication.Core.Api.Models.AppModels;
using Hubtel.Authentication.Core.Api.Options;
using Hubtel.Authentication.Core.Api.Services;
using Hubtel.Authentication.Core.Api.Services.Interface;
using Hubtel.Authentication.Core.Api.Services.Provider;
using Hubtel.Authentication.Whatsapp.Sdk.Extensions;
using Hubtel.Authentication.Whatsapp.Sdk.Options;
using Hubtel.Kafka.Host.Core;
using Microsoft.AspNetCore.HttpLogging;
using Microsoft.EntityFrameworkCore;
using Hubtel.Authentication.Core.Api.Constants;
using Hubtel.Authentication.Commons.Extensions;
using Hubtel.Authentication.Core.Api.BgWorker;
using Hubtel.Authentication.Core.Api.Repositories.Interfaces;
using Hubtel.Authentication.Core.Api.Repositories.Provider;
using Hubtel.Authentication.Core.Api.Data.Entities;

namespace Hubtel.Authentication.Core.Api
{
    public class Startup
    {
        private const string Basic = "Basic";
        private readonly IConfiguration _configuration;

        public Startup(IConfiguration configuration)
        {
            _configuration = configuration;
        }


        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddLogging(loggingBuilder => loggingBuilder.AddGelf(options =>
            {
                options.AdditionalFields = new Dictionary<string, object>
                {
                    { "facility", _configuration.GetSection("Logging")["GELF:Facility"] },
                    { "Environment", _configuration.GetSection("Logging")["GELF:Environment"] },
                    { "machine_name", Environment.MachineName }
                };
                options.Host = _configuration.GetSection("Logging")["GELF:Host"];
                options.LogSource = _configuration.GetSection("Logging")["GELF:LogSource"];
                options.Port = int.Parse(_configuration.GetSection("Logging")["GELF:Port"]);
            }));
            services.AddHttpLogging(httpLoggingOptions =>
            {
                httpLoggingOptions.LoggingFields =
                    HttpLoggingFields.RequestPath | 
                    HttpLoggingFields.RequestMethod | 
                    HttpLoggingFields.ResponseStatusCode;
            });

            services.Configure<ForwardedHeadersOptions>(options =>
            {
                options.ForwardedHeaders = Microsoft.AspNetCore.HttpOverrides.ForwardedHeaders.All;
            });
            
            services.AddDbContext<ApplicationDbContext>(options =>
                options.UseNpgsql(_configuration.GetConnectionString("DbConnection")));
            services.AddStatsD(provider =>
            {
                return new StatsDConfiguration
                {
                    Host = _configuration["StatsdConfig:Server"],
                    Port = int.Parse(_configuration["StatsdConfig:Port"]),
                    Prefix = _configuration["StatsdConfig:Prefix"],
                    OnError = ex => true
                };
            });
            services
                .AddScoped<IAuthenticationConfigStoreService,
                    AuthenticationConfigStoreService>();
            services.Configure<RouteOptions>(options => options.LowercaseUrls = true);
            services.AddHubtelKafkaProducer(_configuration);
            services.AddMapsterCustomConfigurations();
            services.AddApplicationInsightsTelemetry();
            services.AddHubtelTelemetry(_configuration);

            services.AddHubtelRedisSdk(c => _configuration.GetSection(nameof(RedisConfiguration)).Bind(c));

            
            //ensure that controller or action methods has matching auth schemes only e.g.   [Authorize(AuthenticationSchemes = "PrivateKey,Basic,Bearer")]
            services.EnableAuthentication(true, false, false,
                (c => _configuration.GetSection(nameof(BearerTokenConfig)).Bind(c)));


            services.AddHealthChecks();
            services.AddHttpContextAccessor();
            services.AddCors();
            services.AddControllers().AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.WriteIndented = true;
                options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
                options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
            }).ConfigureApiBehaviorOptions(options =>
            {
                //add custom error response
                options.InvalidModelStateResponseFactory = context => new BadRequestObjectResult(
                    new BadRequestApiResponse("validation errors")
                    {
                        Errors = context.GetError()
                    });
            });
            services.Configure<ApiDocsConfig>(c => _configuration.GetSection(nameof(ApiDocsConfig)).Bind(c));
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo
                {
                    Title = "Hubtel.Authentication.Core.Api",
                    Description = "_APIServiceDescription_", //to be templated
                    Version = "v1"
                });

                c.EnableAnnotations();

                c.AddSecurityDefinition(Basic, new OpenApiSecurityScheme
                {
                    Description = @"Enter '[schemeName]' [space] and then your token in the text input below.<br/>
                      Example: 'Bearer 12345abcdef'",
                    Name = "Authorization",
                    In = ParameterLocation.Header,
                    Type = SecuritySchemeType.Http,
                    Scheme = Basic
                });

                c.AddSecurityRequirement(new OpenApiSecurityRequirement()
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = Basic
                            },
                            Scheme = "oauth2",
                            Name = Basic,
                            In = ParameterLocation.Header,
                        },
                        new List<string>()
                    }
                });


                c.DocumentFilter<AdditionalParametersDocumentFilter>();

                // Set the comments path for the Swagger JSON and UI.
                var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
                var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
                c.IncludeXmlComments(xmlPath);
            });

            services.AddHubtelPhoneNumberSdk();
            services.AddDistributedMemoryCache();
            services.Configure<List<ApiAccount>>(c => _configuration.GetSection("ApiAccounts").Bind(c));

            services.Configure<KafkaProducerConfig>(options =>
                _configuration.GetSection(nameof(KafkaProducerConfig)).Bind(options));
            services.Configure<KafkaExtra>(options => _configuration.GetSection(nameof(KafkaExtra)).Bind(options));
            services.Configure<AuthStoreSection>(options => _configuration.GetSection("AuthStores").Bind(options));
            services.Configure<List<string>>(options => _configuration.GetSection("FraudMiddlewareExclusionList").Bind(options));
            services.Configure<SocketOptions>(options => _configuration.GetSection("Socket").Bind(options));
            services.Configure<QrOptions>(options => _configuration.GetSection("Qr").Bind(options));

            services.Configure<SocketService>(options =>
                _configuration.GetSection(nameof(SocketService)).Bind(options));
            
            services.Configure<HostOptions>(option =>
            {
                option.BackgroundServiceExceptionBehavior = BackgroundServiceExceptionBehavior.Ignore;
            });

            services.Configure<NotificationConfig>(_configuration.GetSection("Notification"));

            var awsConfig = _configuration.GetSection("AWSConfig");
            services.AddCognitoClient(new AWSConfig
            {
                AccessKey = awsConfig.GetValue<string>("AccessKey"),
                AccessSecret = awsConfig.GetValue<string>("AccessSecret"),
                ClientID = awsConfig.GetValue<string>("ClientID"),
                PoolID = awsConfig.GetValue<string>("PoolID")
            });

            services.Configure<JwtConfig>(_configuration.GetSection("Jwt"));

            // jwt authentication
            var issuer = _configuration.GetValue<string>("Jwt:Issuer");
            var audience = _configuration.GetValue<string>("Jwt:Audience");
            var key = _configuration.GetValue<string>("Jwt:SigningKey");
            services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                .AddJwtBearer(options =>
                {
                    options.TokenValidationParameters = new TokenValidationParameters
                    {
                        ValidateIssuer = true,
                        ValidateAudience = true,
                        ValidateLifetime = false,
                        ValidateIssuerSigningKey = true,
                        ValidIssuer = issuer,
                        ValidAudience = audience,
                        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(key))
                    };
                });

            services.AddHttpClient(nameof(ApiType.ConsumerAuth), client =>
            {
                client.BaseAddress = new Uri(_configuration["ConsumerData:Url"]);
                client.DefaultRequestHeaders.Accept.Add(
                    new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
            });
            
            services.AddSingleton<IKafkaService, KafkaService>();
            services.AddSingleton<ICipherService, CipherServiceSimple>();
            services.AddSingleton<IJwtService,JwtService>();
            services.AddSingleton(Channel.CreateUnbounded<FraudReport>());
            services.AddHostedService<LoginEventsWorker>();
            services.AddHubtelSignalR(options =>
            {
                options.EnableDetailedErrors = true;
                options.MaximumParallelInvocationsPerClient = 2;
            });
            services.AddScoped<IOtpService, OtpService>();
            services.AddScoped<IAccountLookupService, AccountLookupService>();
            services.AddScoped<IQrService, QrService>();
            services.AddScoped<IUssdService, UssdService>();
            services.AddScoped<IEmailService, EmailService>();
            services.AddScoped<LoginEventsMiddleware>();
            services.AddScoped(typeof(IAuthConfigRepository<AuthenticationConfiguration>),typeof(AuthConfigRepository));

            services.AddActorSystem("HubtelOtpActorSystem");
            services.AddHubtelWhatsAppSdk(options =>
                _configuration.GetSection(nameof(WhatsAppProxyConfig)).Bind(options));
            services.AddScoped<SignalRAuthMiddleware>();
            services.AddRateLimiter(options =>
            {
                options.AddPolicy<string,OtpRateLimiterPolicy>(CommonStringConstants.OtpRateLimitPolicy);
            });
            services.AddAppOpentelemtry(_configuration);
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }

            var docsConfig = app.ApplicationServices.GetService<IOptions<ApiDocsConfig>>().Value;

            if (docsConfig.ShowSwaggerUi)
            {
                app.UseSwagger();
                app.UseSwaggerUI(c =>
                {
                    c.SwaggerEndpoint("/swagger/v1/swagger.json", "Hubtel API");
                    if (!docsConfig.EnableSwaggerTryIt)
                    {
                        c.SupportedSubmitMethods();
                    }
                });
            }

            if (docsConfig.ShowRedocUi)
            {
                //help from https://christian-schou.dk/how-to-make-api-documentation-using-swagger/
                app.UseReDoc(options =>
                {
                    //visit {baseURL}/api-docs to see this in action...
                    options.DocumentTitle = "Hubtel API Template";
                    options.SpecUrl = "/swagger/v1/swagger.json";
                });
            }

            
            app.UseForwardedHeaders();
            app.ConfigureExceptionHandler(app.ApplicationServices.GetService<ILogger<Program>>(),
                app.ApplicationServices.GetService<IStatsDPublisher>());

            app.UseRouting();
            app.UseRateLimiter();
            app.UseCors(x => x
                .AllowAnyMethod()
                .AllowAnyHeader()
                .SetIsOriginAllowed(origin => true) // allow any origin
                .AllowCredentials()); // allow
            
            app.UseMiddleware<SignalRAuthMiddleware>();
            app.UseAuthentication();
            app.UseAuthorization();
            
            var logRequestResponse = _configuration.GetValue<bool>("LogRequestResponse");

            if (logRequestResponse)
            {
                app.UseMiddleware<RequestResponseLoggingMiddleware>();
            }

            app.UseMiddleware<LoginEventsMiddleware>();
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapHealthChecks("/health");
                endpoints.MapControllers();
            });
        }
    }

    public class AdditionalParametersDocumentFilter : IDocumentFilter
    {
        public void Apply(OpenApiDocument swaggerDoc, DocumentFilterContext context)
        {
            foreach (var schema in context.SchemaRepository.Schemas.Select(x => x.Value))
            {
                if (schema.AdditionalProperties == null)
                {
                    schema.AdditionalPropertiesAllowed = true;
                }
            }
        }
    }
}