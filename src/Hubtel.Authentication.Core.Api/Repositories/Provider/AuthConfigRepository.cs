using Hubtel.Authentication.Core.Api.Data;
using Hubtel.Authentication.Core.Api.Data.Entities;
using Hubtel.Authentication.Core.Api.Repositories.Interfaces;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;

namespace Hubtel.Authentication.Core.Api.Repositories.Provider
{
    public class AuthConfigRepository:IAuthConfigRepository<AuthenticationConfiguration>
    {
        private readonly ApplicationDbContext _dbContext;

        public DbSet<AuthenticationConfiguration> DbSet { get; init; }

        public AuthConfigRepository(ApplicationDbContext dbContext)
        {
            DbSet = dbContext.Set<AuthenticationConfiguration>();
            _dbContext = dbContext;
        }

        /// <summary>
        /// Retrieves an entity of type T by its ID.
        /// </summary>
        public async Task<AuthenticationConfiguration> GetByIdAsync(Guid id, CancellationToken ct = default)
        {
            return await DbSet.FindAsync(new object[] { id, ct }, cancellationToken: ct);
        }

        /// <summary>
        /// Retrieves all entities of type T.
        /// </summary>
        public async Task<IEnumerable<AuthenticationConfiguration>> GetAllAsync(CancellationToken ct = default)
        {
            return await DbSet.ToListAsync(ct);
        }

        /// <summary>
        /// Finds entities of type T based on a predicate.
        /// </summary>
        public async Task<IEnumerable<AuthenticationConfiguration>> FindAsync(Expression<Func<AuthenticationConfiguration, bool>> predicate, CancellationToken ct = default)
        {
            return await DbSet.Where(predicate).ToListAsync(ct);
        }

        /// <summary>
        /// Finds a single entity of type T based on a predicate.
        /// </summary>
        public async Task<AuthenticationConfiguration> FindFirstOneAsync(Expression<Func<AuthenticationConfiguration  , bool>> predicate, CancellationToken ct = default)
        {
            return await DbSet.FirstOrDefaultAsync(predicate, ct);
        }

        /// <summary>
        /// Finds a single entity of type T based on a predicate without tracking it in the DbContext.
        /// </summary>
        public async Task<AuthenticationConfiguration> FindOneAsNoTrackingAsync(Expression<Func<AuthenticationConfiguration, bool>> predicate, CancellationToken ct = default)
        {
            return await DbSet.AsNoTracking().FirstOrDefaultAsync(predicate, ct);
        }

        /// <summary>
        /// Adds a new entity of type T.
        /// </summary>
        public async Task<int> AddConfigAsync(AuthenticationConfiguration entity, CancellationToken ct = default)
        {
            await DbSet.AddAsync(entity, ct);
            return await _dbContext.SaveChangesAsync(ct);
        }

        /// <summary>
        /// Adds a range of new entities of type T.
        /// </summary>
        public async Task<int> AddRangeAsync(List<AuthenticationConfiguration> entities, CancellationToken ct = default)
        {
            DbSet.AddRange(entities);
            return await _dbContext.SaveChangesAsync(ct);
        }

        /// <summary>
        /// Updates an existing entity of type T.
        /// </summary>
        public async Task<int> UpdateAsync(AuthenticationConfiguration entity, CancellationToken ct = default)
        {
            DbSet.Update(entity);
            return await _dbContext.SaveChangesAsync(ct);
        }

        /// <summary>
        /// Updates a range of existing entities of type T.
        /// </summary>
        public async Task<int> UpdateRangeAsync(List<AuthenticationConfiguration> entities, CancellationToken ct = default)
        {
            DbSet.UpdateRange(entities);
            return await _dbContext.SaveChangesAsync(ct);
        }

        /// <summary>
        /// Deletes an existing entity of type T.
        /// </summary>
        public async Task<int> DeleteAsync(AuthenticationConfiguration entity, CancellationToken ct = default)
        {
            DbSet.Remove(entity);
            return await _dbContext.SaveChangesAsync(ct);
        }

        /// <summary>
        /// Deletes existing entities of type T.
        /// </summary>
        public async Task<int> DeleteRangeAsync(List<AuthenticationConfiguration> entities, CancellationToken ct = default)
        {
            DbSet.RemoveRange(entities);
            return await _dbContext.SaveChangesAsync(ct);
        }

        public async Task<int> RemoveRangeAsync(IQueryable<AuthenticationConfiguration> entities, CancellationToken ct = default)
        {
            DbSet.RemoveRange(entities);
            return await _dbContext.SaveChangesAsync(ct);
        }

        /// <summary>
        /// Returns a queryable of all entities of type T without tracking them in the DbContext.
        /// </summary>
        public IQueryable<AuthenticationConfiguration> GetQueryableAsNoTracking() => DbSet.AsNoTracking().AsQueryable();
        public IQueryable<AuthenticationConfiguration> GetQueryable() => DbSet.AsQueryable();
        public Task<int> SaveChangesAsync(CancellationToken ct = default)
        {
            return _dbContext.SaveChangesAsync(ct); 
        }
    }
}
