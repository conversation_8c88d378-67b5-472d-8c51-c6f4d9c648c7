# API Documentation: Account Lookup - Initiate Method

The `Initiate` method is a part of the 'Login' Process and is responsible for beginning the account store configuration
retrieval process for a specific client application identified by a given `appId`.

**Route**: `Initiate` (HTTP GET)

## Request Format

**Headers**:

- `Content-Type`: `application/json`

**Parameters**:

- `appId` (UUID): The ID of the client application to retrieve the stored configuration. Sent in query string.

## Responses

The method returns an `IActionResult` which can encapsulate either a success or error response. The specific types of
responses you might receive are:

**Status Code: 200 OK**

Successful operation. The matching `AccountStoreConfig` object related to the `appId` was found and returned.

Response body contains `ApiResponse` object with:

- A `code` field containing the HTTP status code as a string.
- A `message` field containing the response message as a string ("details retrieved" in this scenario).
- A `data` field containing the `AccountStoreConfig` object data.

**Example 400 Response**:
`{ "Code": "400", "Message": "The provided application ID may not be configured correctly, please verify your input", "data": null }`
**Example 200 Response**:
`json {
"message": "details retrieved",
"code": "200",
"data": {
"productName": "Hubtel",
"productLogo": "https://designs.hubtel.com/v4/website/assets/images/logo.png",
"appName": "hubtel.com",
"id": "d317ed35-d765-4631-825d-ef36094d14c6",
"primary": "#FFFFFF",
"text-color": "#FFFFFF",
"bg-light": "#FFFFFF",
"bg-lighter": "#FFFFFF",
"signingKey": ""
}
}` 
