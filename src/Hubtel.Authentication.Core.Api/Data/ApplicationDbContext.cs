using System;
using System.Reflection;
using Hubtel.Authentication.Commons.Models.Fraud;
using Hubtel.Authentication.Core.Api.Data.Core;
using Hubtel.Authentication.Core.Api.Data.Entities;
using Hubtel.Authentication.Core.Api.Data.EntityConfigurations;
using Microsoft.EntityFrameworkCore;

namespace Hubtel.Authentication.Core.Api.Data;

public class ApplicationDbContext:DbContext
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
        : base(options)
    {
        AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);
        AppContext.SetSwitch("Npgsql.DisableDateTimeInfinityConversions", true);
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        optionsBuilder.AddInterceptors(new SoftDeleteInterceptor());
        optionsBuilder.AddInterceptors(new AuditableEntityInterceptor());
        base.OnConfiguring(optionsBuilder);
       
    }

    public DbSet<AuthenticationConfiguration> AuthenticationConfigurations { get; set; }
    public DbSet<SelectedAuthChannel> SelectedAuthChannels { get; set; }
    public DbSet<LoginEvent> LoginEvents { get; set; } = null!;
    public DbSet<WhiteList> WhiteLists { get; set; } = null!;
    public DbSet<ExternalLoginConfiguration> ExternalLoginConfigurations { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.SetGlobalStringLength(255);
        modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
        base.OnModelCreating(modelBuilder);
    }
}