using System;

namespace Hubtel.Authentication.Core.Api.Data.Audit;

/// <summary>
/// Represents an interface for objects that should be audited.
/// </summary>
public interface IAuditable:ICreatedUpdatedBy
{
    // Audit properties
    /// <summary>
    /// Gets or sets the created date/time.
    /// </summary>
    /// <value>
    /// The created date/time.
    /// </value>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Gets or sets the updated date/time.
    /// </summary>
    /// <value>
    /// The updated date/time.
    /// </value>
    public DateTime? UpdatedAt { get; set; }
}