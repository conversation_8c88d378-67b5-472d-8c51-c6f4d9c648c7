using Hubtel.Authentication.Commons.Models;
using Hubtel.Authentication.Core.Api.Data.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using System.Collections.Generic;
using System;
using System.Diagnostics.CodeAnalysis;
using static Microsoft.ApplicationInsights.MetricDimensionNames.TelemetryContext;
using System.Reflection.Emit;
using System.Linq;

namespace Hubtel.Authentication.Core.Api.Data.EntityConfigurations;


public class AuthenticationConfigurationStoreEntityConfiguration : IEntityTypeConfiguration<AuthenticationConfiguration>
{
    private static readonly char[] CommaSeparator = [','];

    /// <summary>
    /// Configures the entity type for AuthenticationConfiguration.
    /// </summary>
    /// <param name="builder">The entity type builder.</param>
    public void Configure(EntityTypeBuilder<AuthenticationConfiguration> builder)
    {
        builder.HasKey(x => x.Id);
        builder.Property(x => x.ProductName).IsRequired();
        builder.Property(x => x.ProductLogo).IsRequired();
        builder.Property(x => x.AppName).IsRequired();
        builder.Property(x => x.AccountLookUpUrl).IsRequired();
        builder.Property(x => x.Primary).IsRequired();
        builder.Property(x => x.SenderId).IsRequired();
        builder.Property(x => x.BgLight).IsRequired();
        builder.Property(x => x.HideProductLogo).HasDefaultValue(false);
        builder.Property(x => x.ShowTermsAndConditions).HasDefaultValue(true);
        builder.Property(x => x.HoverState).IsRequired();
        builder.Property(x => x.SigningKey).IsRequired();
        builder.OwnsMany(x => x.AuthenticationChannels);
        builder.OwnsMany(parent => parent.WhiteLists, list =>
        {
            list.Property(x => x.PhoneNumber).HasMaxLength(20);
            list.Property(x => x.Otp).HasMaxLength(10);
        });
        builder
            .Property(e => e.DefaultChannel)
            .HasConversion(new EnumToStringConverter<AuthenticationChannel>())
            .HasMaxLength(20);
        builder.HasQueryFilter(x => !x.IsDeleted);

        builder.Property(p => p.AllowedRedirectUrls)
            .HasConversion(
             clrToDb => string.Join(",", clrToDb),
             dbToClr => dbToClr.Split(CommaSeparator, StringSplitOptions.RemoveEmptyEntries).ToList()
            );
    }
}
