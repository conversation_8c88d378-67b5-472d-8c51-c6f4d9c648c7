using Microsoft.EntityFrameworkCore;

namespace Hubtel.Authentication.Core.Api.Data.EntityConfigurations;

/// <summary>
/// Provides extension methods for entity configuration.
/// </summary>
public static class EntityConfigurationExtensions
{
    /// <summary>
    /// Sets the maximum length for all string properties in the model.
    /// </summary>
    /// <param name="builder">The ModelBuilder instance.</param>
    /// <param name="maxLength">The maximum length for string properties.</param>
    public static void SetGlobalStringLength(this ModelBuilder builder, int maxLength)
    {
        foreach (var entity in builder.Model.GetEntityTypes())
        {
            foreach (var property in entity.GetProperties())
            {
                if (property.ClrType == typeof(string))
                {
                    property.SetMaxLength(maxLength);
                }
            }
        }
    }
}