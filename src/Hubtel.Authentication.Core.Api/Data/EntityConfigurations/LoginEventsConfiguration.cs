using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json;
using Hubtel.Authentication.Commons.Models.Fraud;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Hubtel.Authentication.Core.Api.Data.EntityConfigurations;

public class LoginEventsConfiguration : IEntityTypeConfiguration<LoginEvent>
{
    public void Configure(EntityTypeBuilder<LoginEvent> builder)
    {
        builder.HasKey(o => o.Id);

        builder.Property(c => c.CreatedAt).HasColumnType("timestamp");

        var options=new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        };
        builder
            .Property(c => c.ExtraData)
            .HasConversion(
                v => JsonSerializer.Serialize(v, options),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, options)!
            );
    }
}