using Hubtel.Authentication.Core.Api.Data.Core;
using Hubtel.Authentication.Core.Api.Data.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using System.Diagnostics.CodeAnalysis;

namespace Hubtel.Authentication.Core.Api.Data.EntityConfigurations
{
    /// <summary>
    /// Represents the entity configuration for the ExternalLoginConfiguration entity.
    /// </summary>
  
    public class ExternalLoginConfigurationStoreEntityConfigurations : IEntityTypeConfiguration<ExternalLoginConfiguration>
    {
        /// <summary>
        /// Configures the ExternalLoginConfiguration entity.
        /// </summary>
        /// <param name="builder">The entity type builder.</param>
        public void Configure(EntityTypeBuilder<ExternalLoginConfiguration> builder)
        {
            builder.HasKey(x => x.Id);
            builder.Property(e => e.ClientId);
            builder.Property(e => e.Secret);
            builder.Property(e => e.DisablePhoneLookup).HasDefaultValue(false);
            builder.Property(e => e.Type).HasConversion(new EnumToStringConverter<ExternalAuthenticationType>());
        }
    }
}
