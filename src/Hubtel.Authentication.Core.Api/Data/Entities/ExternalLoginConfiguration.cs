

using Hubtel.Authentication.Core.Api.Data.Core;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations;

namespace Hubtel.Authentication.Core.Api.Data.Entities
{
    public class ExternalLoginConfiguration
    {

        /// <value>
        /// The Id property represents the primary value of the configuration.
        /// </value>
        [JsonRequired]
        public int Id { get; set; }
        /// <value>
        /// The ClientId property represents the clientId of the configuration.
        /// </value>
        public string ClientId { get; set; }

        /// <value>
        /// The Secret property represents the Secret of the configuration.
        /// </value>
        public string Secret { get; set; }

        /// <value>
        /// The Authority property represents the Authority of the configuration.
        /// </value>
        public string Authority { get; set; }

        /// <value>
        /// The AuthenticationType property represents the authentication type of the configuration.
        /// </value>
        
        [JsonRequired]
        public ExternalAuthenticationType Type { get; set; } 

        /// <value>
        /// The ScopeId property represents the scope ID of the configuration.
        /// </value>
        public string ScopeId { get; set; }
        /// <summary>
        /// Gets or sets a value indicating whether phone lookup is disabled during authentication.
        /// </summary>
        public bool DisablePhoneLookup { get; set; } = false;
    }

}
