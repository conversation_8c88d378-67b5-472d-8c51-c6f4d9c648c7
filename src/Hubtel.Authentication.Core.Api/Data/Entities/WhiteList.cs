namespace Hubtel.Authentication.Core.Api.Data.Entities;

/// <summary>
/// Represents a whitelist entry that stores phone numbers and OTPs.
/// </summary>
public class WhiteList
{
    /// <summary>
    /// Represents a phone number.
    /// </summary>
    public string PhoneNumber { get; set; }

    /// <summary>
    /// Represents a one-time password (OTP) stored in the whitelist entry.
    /// </summary>
    public string Otp { get; set; }
}