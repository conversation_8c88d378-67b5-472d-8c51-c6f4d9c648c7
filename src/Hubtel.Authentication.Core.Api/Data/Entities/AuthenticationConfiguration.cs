using System;
using System.Collections.Generic;
using Hubtel.Authentication.Commons.Models;
using Hubtel.Authentication.Core.Api.Data.Audit;
using Hubtel.Authentication.Core.Api.Data.Core;

namespace Hubtel.Authentication.Core.Api.Data.Entities;

/// <summary>
/// Represents the configuration settings for an account store.
/// </summary>
public class AuthenticationConfiguration : ISoftDelete, IAuditable
{
    /// <summary>
    /// Represents the configuration settings for an account store.
    /// </summary>
    public AuthenticationConfiguration()
    {
        IsActive = false;
        AuthenticationChannels = new();
        WhiteLists = new();
        ExternalLoginConfigurations = new();
        ProductEmail = "";
    }

    /// <summary>
    /// Represents the configuration settings for an account store.
    /// </summary>
    public string ProductName { get; set; }

    /// <summary>
    /// Represents the logo of a product.
    /// </summary>
    public string ProductLogo { get; set; }

    /// <summary>
    /// Gets or sets the name of the application.
    /// </summary>
    public string AppName { get; set; }

    /// <summary>
    /// Represents the configuration settings for an account store.
    /// </summary>
    public string AccountLookUpUrl { get; set; }

    /// <summary>
    /// Represents the URL used for email lookup in the authentication configuration.
    /// </summary>
    public string EmailLookUpUrl { get; set; }

    /// <summary>
    /// Gets or sets the ID of the authentication configuration.
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Represents the primary property of the AuthenticationConfiguration class.
    /// </summary>
    /// <value>
    /// The primary property represents the primary value of the configuration.
    /// </value>
    public string Primary { get; set; }

    /// <summary>
    /// Represents the configuration setting for the hover state of an account store.
    /// </summary>
    /// <remarks>
    /// The HoverState property determines the hover state of an account store.
    /// </remarks>
    public string HoverState { get; set; }

    /// <summary>
    /// Represents the SenderId property of the AuthenticationConfiguration class.
    /// </summary>
    public string SenderId { get; set; }

    /// <summary>
    /// Gets or sets the BgLight property of the AuthenticationConfiguration.
    /// </summary>
    public string BgLight { get; set; }

    /// <summary>
    /// Represents the signing key for an account store.
    /// </summary>
    /// <remarks>
    /// The signing key is a secret key used for signing and verifying authentication tokens.
    /// It is a required property for the <see cref="AuthenticationConfiguration"/> class.
    /// </remarks>
    public string SigningKey { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the authentication configuration is active.
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Gets or sets the validity period of the authentication configuration.
    /// </summary>
    /// <remarks>
    /// This property represents the duration of time for which the authentication configuration is valid.
    /// </remarks>
    public double ValidityPeriod { get; set; }

    /// <summary>
    /// Represents the available authentication channels for an authentication configuration.
    /// </summary>
    public List<SelectedAuthChannel> AuthenticationChannels { get; private set; }

    /// <summary>
    /// Represents a list of white-listed numbers.
    /// </summary>
    public List<WhiteList> WhiteLists { get; set; }

    /// <summary>
    /// Gets the list of external login configurations associated with the authentication configuration.
    /// </summary>
    /// <value>
    /// The list of external login configurations.
    /// </value>
    public List<ExternalLoginConfiguration> ExternalLoginConfigurations { get; private set; }

    /// <summary>
    /// Method to add a new authentication channel to the list.
    /// </summary>
    /// <param name="channel">The authentication channel to be added.</param>
    public void AddAuthenticationChannels(IEnumerable<SelectedAuthChannel> channel)
    {
        AuthenticationChannels.AddRange(channel);
    }

    /// <summary>
    /// Adds external login configurations to the AuthenticationConfiguration.
    /// </summary>
    /// <param name="externalLoginConfigurations">The list of ExternalLoginConfiguration objects to be added.</param>
    public void AddExternalLoginConfigurations(IEnumerable<ExternalLoginConfiguration> externalLoginConfigurations)
    {
        ExternalLoginConfigurations.AddRange(externalLoginConfigurations);
    }

    /// <summary>
    /// Method to remove an authentication channel from the list.
    /// </summary>
    /// <param name="channel">The authentication channel to be removed.</param>
    public void RemoveAuthenticationChannel(SelectedAuthChannel channel)
    {
        AuthenticationChannels.Remove(channel);
    }

    /// <summary>
    /// Removes the specified external login configurations from the authentication configuration.
    /// </summary>
    /// <param name="externalLoginConfigurations">The external login configurations to be removed.</param>
    /// <returns>The number of external login configurations removed.</returns>
    public int RemoveExternalLoginConfigurations(ExternalLoginConfiguration externalLoginConfigurations)
    {
        return ExternalLoginConfigurations.RemoveAll(p => p.Id == externalLoginConfigurations.Id);
    }

    /// <summary>
    /// Gets or sets the name of the user who last updated the authentication configuration.
    /// </summary>
    /// <value>
    /// The name of the user who last updated the authentication configuration.
    /// </value>
    public string UpdatedBy { get; set; }

    /// <summary>
    /// Gets or sets the user who created the AuthenticationConfiguration.
    /// </summary>
    /// <remarks>
    /// This property represents the username or identifier of the user who created the AuthenticationConfiguration.
    /// </remarks>
    /// <value>
    /// The username or identifier of the user who created the AuthenticationConfiguration.
    /// </value>
    public string CreatedBy { get; set; }

    /// <summary>
    /// Gets or sets the date and time when the authentication configuration was created.
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Gets or sets the date and time when the entity was last updated.
    /// </summary>
    /// <remarks>
    /// This property is used to track the last update time of the entity.
    /// </remarks>
    public DateTime? UpdatedAt { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the entity is deleted.
    /// </summary>
    /// <remarks>
    /// This property is used to soft delete the entity.
    /// When set to true, it indicates that the entity is deleted.
    /// When set to false, it indicates that the entity is not deleted.
    /// </remarks>
    public bool IsDeleted { get; set; }


    /// <summary>
    /// Gets or sets a value indicating whether the phone number can be skipped during authentication.
    /// </summary>
    /// <remarks>
    /// By default, the CanSkipPhoneNumber property is set to false, indicating that the phone number is required during authentication.
    /// If the CanSkipPhoneNumber property is set to true, it means that the phone number can be skipped during authentication.
    /// </remarks>
    public bool CanSkipPhoneNumber { get; set; }

    /// <summary>
    /// Gets or sets the default authentication channel.
    /// </summary>
    /// <remarks>
    /// The default authentication channel determines the primary channel used for authentication.
    /// </remarks>
    /// <value>The default authentication channel.</value>
    public AuthenticationChannel DefaultChannel { get; set; }

    /// <summary>
    /// Gets or sets the OTP liability policy.
    /// </summary>
    /// <value>
    /// The OTP liability policy.
    /// </value>
    public string OtpLiabilityPolicy { get; set; }

    /// <summary>
    /// Represents the product email for an account store.
    /// </summary>
    public string ProductEmail { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the user has notification access.
    /// </summary>
    public string TermsOfServiceUrl { get; set; }
    /// <summary>
    ///Gets or sets a value indicating if the product logo should be displayed.
    /// </summary>
    public bool HideProductLogo { get; set; }
    /// <summary>
    /// Gets or sets a value indicating if the terms and conditions should be displayed.
    /// </summary>
    public bool ShowTermsAndConditions { get; set; }
    public string Audience { get; set; }
    public string Issuer { get; set; }

    //simple comma separated string in the db, but will be an array in the dtos
    public List<string> AllowedRedirectUrls { get; set; } = new List<string>();
}
