using System;
using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Hubtel.Authentication.Core.Api.Models.Requests
{
    public class QrData
    {
        [Required]
        public string PlayerId { get; set; }
        [Required]
        public string PhoneNumber { get; set; }
        [JsonRequired]
        public Guid AppId { get; set; }

    }

    public class QrChallengeResponse
    {
        public string QrCodeDataUrl { get; set; }
        public string QrCodeData { get; set; }
        public string QrId { get; set; }
        /// <summary>
        /// Represents an account object.
        /// </summary>
        /// <remarks>
        /// This property is used to store information about an account.
        /// It provides access to various properties and methods to manage the account.
        /// </remarks>
        public dynamic Account { get; set; }
    }

    public class QrDataRequest
    {
        [Required]
        [DataType(DataType.PhoneNumber)]
        public string PhoneNumber { get; set; }
        [Required]
        public string QrData { get; set; }
        public string Platform { get; set; }
        public string Version { get; set; }
        public string Device { get; set; }
        public string DeviceId { get; set; }
        [JsonRequired]
        public Guid AppId { get; set; }
    }

    public class QrDataResponse
    {
        public string QrCodeDataUrl { get; set; }
        public string QrCodeData { get; set; }
        public string QrId { get; set; }
    }
}
