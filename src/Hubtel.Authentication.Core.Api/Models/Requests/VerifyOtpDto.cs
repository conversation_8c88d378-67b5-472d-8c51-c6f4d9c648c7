using System;
using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Hubtel.Authentication.Core.Api.Models.Requests
{
    /// <summary>
    /// Represents a data transfer object (DTO) used for verifying OTP (One-Time Password) codes.
    /// </summary>
    public class VerifyOtpDto
    {
        /// <summary>
        /// Gets or sets the phone number for a user.
        /// </summary>
        /// <value>
        /// A string representing the phone number.
        /// </value>
        [Required]
        public string PhoneNumber { get; set; }

        /// <summary>
        /// Gets or sets the unique identifier of a request.
        /// </summary>
        /// <remarks>
        /// This property is required and must be a non-null string value.
        /// </remarks>
        [Required]
        public string RequestId { get; set; }

        /// <summary>
        /// Gets or sets the prefix.
        /// </summary>
        /// <value>
        /// The prefix.
        /// </value>
        [Required]
        public string Prefix { get; set; }

        /// <summary>
        /// Gets or sets the OTP (One-Time Password) code.
        /// </summary>
        /// <value>
        /// The OTP (One-Time Password) code.
        /// </value>
        /// <remarks>
        /// <para>
        /// The OTP code is a unique numerical or alphanumerical code used for authentication or verification purposes.
        /// </para>
        /// <para>
        /// This property is required and must be provided.
        /// </para>
        /// </remarks>
        [Required]
        public string OtpCode { get; set; }

        /// <summary>
        /// Gets or sets the unique identifier for the client application.
        /// </summary>
        
        [JsonRequired]
        public Guid AppId { get; set; }
        public string LinkedEmail { get; set; } = string.Empty;
    }
}
