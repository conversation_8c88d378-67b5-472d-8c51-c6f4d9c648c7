using System;
using System.ComponentModel.DataAnnotations;
using Hubtel.Authentication.Commons.Models;
using Newtonsoft.Json;

namespace Hubtel.Authentication.Core.Api.Models.Requests
{
    public class GenerateOtpDto
    {
        [Required]
        public string PhoneNumber { get; set; }
        public string CountryCode { get; set; } = "GH";
        [JsonRequired]
        public Guid AppId { get; set; }
        public string LinkedEmail { get; set; } = string.Empty;
    }
    public class CreateOtpRequest:GenerateOtpDto
    {
        public AuthenticationChannel AuthenticationChannel { get; set; }
    }
}
