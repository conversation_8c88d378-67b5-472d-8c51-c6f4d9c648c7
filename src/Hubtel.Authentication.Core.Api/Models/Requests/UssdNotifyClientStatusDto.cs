using System;
using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Hubtel.Authentication.Core.Api.Models.Requests;

/// <summary>
/// Represents the USSD Notify Client Status DTO (Data Transfer Object).
/// </summary>
public class UssdNotifyClientStatusDto
{
    /// <summary>
    /// The phone number property of the client being notified.
    /// </summary>
    /// <remarks>
    /// This property represents the phone number of the client being notified.
    /// </remarks>
    [Required]
    public string PhoneNumber { get; set; }
    [Required]
    public string RequestId { get; set; }

    /// <summary>
    /// Gets or sets the country code.
    /// </summary>
    /// <value>
    /// The country code.
    /// </value>
    public string CountryCode { get; set; } = "GH";

    /// <summary>
    /// Gets or sets the unique identifier for the property.
    /// </summary>
    /// <value>
    /// The unique identifier for the property.
    /// </value>
    /// <remarks>
    /// The AppId property is a Guid that represents the unique identifier for the client application.
    /// </remarks>
    
    [JsonRequired]
    public Guid AppId { get; set; }
}