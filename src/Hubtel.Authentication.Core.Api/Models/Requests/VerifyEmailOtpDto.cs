using System;
using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Hubtel.Authentication.Core.Api.Models.Requests;

/// <summary>
/// Represents the data transfer object for verifying the email OTP.
/// </summary>
public class VerifyEmailOtpDto
{
    /// <summary>
    /// Gets or sets the phone number.
    /// </summary>
    /// <remarks>
    /// This property is required.
    /// </remarks>
    [Required]
    public string PhoneNumber { get; set; }

    /// <summary>
    /// Gets or sets the request ID.
    /// </summary>
    [Required]
    public string RequestId { get; set; }

    /// <summary>
    /// Gets or sets the prefix.
    /// </summary>
    /// <value>
    /// The prefix.
    /// </value>
    [Required]
    public string Prefix { get; set; }

    /// <summary>
    /// Gets or sets the OTP code.
    /// </summary>
    /// <remarks>
    /// This property is required.
    /// </remarks>
    [Required]
    public string OtpCode { get; set; }

    /// <summary>
    /// Gets or sets the unique identifier for the property.
    /// </summary>
    /// <value>
    /// The unique identifier for the property.
    /// </value>
    
    [JsonRequired]
    public Guid AppId { get; set; }
}