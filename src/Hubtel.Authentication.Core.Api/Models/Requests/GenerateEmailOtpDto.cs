using System;
using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Hubtel.Authentication.Core.Api.Models.Requests
{
    public class GenerateEmailOtpDto
    {
        [Required]
        public string PhoneNumber { get; set; }
        [JsonIgnore]
        public string LinkedEmail { get; set; } = "";
        public string CountryCode { get; set; } = "GH";
        
        [JsonRequired]
        public Guid AppId { get; set; } 
    }
}
