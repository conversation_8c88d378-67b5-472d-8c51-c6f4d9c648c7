using System;
using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Hubtel.Authentication.Core.Api.Models.Requests;

/// <summary>
/// This class represents the data transfer object for generating USSD OTP.
/// </summary>
public class GenerateUssdOtpDto
{
    /// <summary>
    /// Gets or sets the phone number associated with a user.
    /// </summary>
    /// <remarks>
    /// This property is required and must be provided when creating or updating a user.
    /// </remarks>
    [Required]
    public string PhoneNumber { get; set; }

    /// <summary>
    /// Gets or sets the name of the device.
    /// </summary>
    /// <value>
    /// The device name.
    /// </value>
    public string DeviceName { get; set; }

    /// <summary>
    /// Gets or sets the location of the property.
    /// </summary>
    /// <value>
    /// The location.
    /// </value>
    public string Location { get; set; }

    /// <summary>
    /// Gets or sets the country code.
    /// </summary>
    /// <value>
    /// A string representing the country code.
    /// </value>
    public string CountryCode { get; set; } = "GH";

    /// <summary>
    /// Gets or sets the unique identifier of the application.
    /// </summary>
    /// <value>
    /// The unique identifier of the application.
    /// </value>
    
    [JsonRequired]
    public Guid AppId { get; set; } 
    
}