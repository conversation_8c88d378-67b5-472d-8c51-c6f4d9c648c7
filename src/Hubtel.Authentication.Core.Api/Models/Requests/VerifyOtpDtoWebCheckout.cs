using System;
using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Hubtel.Authentication.Core.Api.Models.Requests;

/// <summary>
/// Represents the DTO (Data Transfer Object) used for verifying OTP (One-Time Password) in a web checkout flow.
/// </summary>
public class VerifyOtpDtoWebCheckout
{
    /// <summary>
    /// Gets or sets the phone number. </summary> <remarks>
    /// This property is required. </remarks>
    /// /
    [Required]
    public string PhoneNumber { get; set; }

    /// <summary>
    /// Gets or sets the identifier of the request.
    /// </summary>
    /// <remarks>
    /// The RequestId property represents the unique identifier of a request.
    /// It is used to track and reference individual requests within the system.
    /// </remarks>
    [Required]
    public string RequestId { get; set; }

    /// <summary>
    /// Gets or sets the OTP code.
    /// </summary>
    /// <value>The OTP code.</value>
    [Required]
    public string OtpCode { get; set; }

    /// Gets or sets the unique identifier of the client application.
    /// /
    
    [JsonRequired]
    public Guid AppId { get; set; } 
}