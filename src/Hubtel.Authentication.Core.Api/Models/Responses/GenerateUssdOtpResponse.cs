using System.Collections.Generic;
using Hubtel.Authentication.Commons.Models;

namespace Hubtel.Authentication.Core.Api.Models.Responses;

/// <summary>
/// Represents the response after generating USSD OTP.
/// </summary>
public class GenerateUssdOtpResponse
{
    public string RequestId { get; set; }

    /// <summary>
    /// Gets or sets the verification USSD.
    /// </summary>
    /// <value>
    /// The verification USSD.
    /// </value>
    public string VerificationUssd { get; set; }

    /// <summary>
    /// Gets or sets the OTP code.
    /// </summary>
    /// <value>
    /// A string representing the OTP code.
    /// </value>
    public string OtpCode { get; set; }

    /// Gets or sets a value indicating whether the pin has been set.
    /// /
    public bool HasSetPin { get; set; }

    /// <summary>
    /// Represents an account object.
    /// </summary>
    /// <remarks>
    /// This property is used to store information about an account.
    /// It provides access to various properties and methods to manage the account.
    /// </remarks>
    public dynamic Account { get; set; }

    public List<AuthenticationChannel> Channels { get; set; }
}