namespace Hubtel.Authentication.Core.Api.Models.Responses
{
    /// <summary>
    /// Represents the response for verifying OTP.
    /// </summary>
    public class VerifyOtpResponse
    {
        /// <summary>
        /// Gets or sets the mobile number.
        /// </summary>
        public string MobileNumber { get; set; }

        /// <summary>
        /// Gets or sets the token.
        /// </summary>
        public string Token { get; set; }

        /// <summary>
        /// Gets or sets the expiry.
        /// </summary>
        public string Expiry { get; set; }

        /// <summary>
        /// Gets or sets the player ID.
        /// </summary>
        public string PlayerId { get; set; }

        /// <summary>
        /// Gets or sets the request ID.
        /// </summary>
        public string RequestId { get; set; }

        /// <summary>
        /// Gets or sets the email.
        /// </summary>
        public string Email { get; set; } = string.Empty;
    }
}
