using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Hubtel.Authentication.Core.Api.Models.Responses
{
    public class EmailLookUpData : BaseLookupData
    {
        [JsonPropertyName("phoneNumbers")]
        public IEnumerable<PhoneNumber> PhoneNumbers { get; set; }
    }
    public class PhoneNumber
    {
        [Required]
        [JsonPropertyName("countryCode")]
        public string CountryCode { get; set; }
        [Required]
        [JsonPropertyName("number")]
        public string Number { get; set; }
    }
}
