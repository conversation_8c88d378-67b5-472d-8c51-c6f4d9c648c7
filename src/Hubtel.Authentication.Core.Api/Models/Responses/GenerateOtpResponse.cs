namespace Hubtel.Authentication.Core.Api.Models.Responses
{
    public class GenerateOtpResponse
    {
        public string RequestId { get; set; }
        public string OtpPrefix { get; set; }
        /// <summary>
        /// Represents an account object.
        /// </summary>
        /// <remarks>
        /// This property is used to store information about an account.
        /// It provides access to various properties and methods to manage the account.
        /// </remarks>
        public dynamic Account { get; set; }

    }
}
