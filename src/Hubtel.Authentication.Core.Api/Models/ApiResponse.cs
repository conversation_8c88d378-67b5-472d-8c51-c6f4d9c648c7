namespace Hubtel.Authentication.Core.Api.Models
{
    public class ApiResponse<T>
    {
        public ApiResponse(string message, T data)
        {
            Message = message;
            Data = data;
        }

        public ApiResponse(string message, string code, T data)
        {
            Message = message;
            Code = code;
            Data = data;
        }

        public ApiResponse()
        {
        }

        public string Message { get; set; }

        public string Code { get; set; }
        public T Data { get; set; }

        
    }

    public static class HelperApi
    {
        public static ApiResponse<T> CreateApiResponse<T>(int code, string message , T data)
        {
            return new ApiResponse<T>(message, data)
            {
                Code = code.ToString()
            };
        }
        
    }


}