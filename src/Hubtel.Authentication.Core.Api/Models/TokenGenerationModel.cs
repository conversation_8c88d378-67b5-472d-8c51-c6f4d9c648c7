using Hubtel.Authentication.Core.Api.Dtos;
using Hubtel.Authentication.Core.Api.Models.Requests;
using Hubtel.Authentication.Core.Api.Models.Responses;

namespace Hubtel.Authentication.Core.Api.Models
{
    public class TokenGenerationModel
    {
        
        public GetBackOfficeAuthConfigStoreResponse AuthenticationConfiguration { get; set; }
        public QrData QrDataRequest { get; set; } = null;
        public PhoneNumberLookupData AccountLookupData { get; set; }
        public string RequestId { get; set; } = "";
    }
}
