using Newtonsoft.Json;

namespace Hubtel.Authentication.Core.Api.Models.AppModels
{
    public class AppResult
    {
        public string Message { get; set; }

        public AppStatus Status { get; set; }

        public object Data { get; set; }

        [JsonIgnore]
        public bool IsSuccessful { get; set; }

        public static AppResult Result(AppStatus status, string message = null, object data = null)
        {
            var result = new AppResult { Status = status, Message = message ?? status.ToString(), Data = data, IsSuccessful = status == AppStatus.Success };
            return result;
        }

        public static AppResult Error(string message = null, object data = null) => Result(AppStatus.Error, message, data);

        public static AppResult Success(string message = null, object data = null) => Result(AppStatus.Success, message, data);

        public static AppResult Fail(string message = null, object data = null)
        {
            return Result(AppStatus.Fail, message, data);
        }

        public static AppResult NotFound(string message = null, object data = null) => Result(AppStatus.NotFound, message, data);

        public static AppResult ModelError(string message = "Please provide valid data", object data = null) => Result(AppStatus.ModelError, message, data);

        public override string ToString()
        {
            return JsonConvert.SerializeObject(this);
        }
    }


    public class BaseAppResult
    {
        public string Message { get; set; }

        public AppStatus Status { get; set; }

       
        public static AppResult Result(AppStatus status, string message = null, object data = null)
        {
            var result = new AppResult
            {
                Status = status,
                Message = message ?? status.ToString(),
                Data = data,
                IsSuccessful = status == AppStatus.Success
            };

            return result;
        }

        public static AppResult Error(string message = null, object data = null) => Result(AppStatus.Error, message, data);

        public static AppResult Success(string message = null, object data = null) => Result(AppStatus.Success, message, data);

        public static AppResult Fail(string message = null, object data = null) => Result(AppStatus.Fail, message, data);


    }

    public class AppResult<T> : BaseAppResult where T : class
    {
        public new T Data { get; set; }

        public static AppResult<T> Result(AppStatus status, string message = null, T data = null) =>
            new AppResult<T> { Status = status, Message = message ?? status.ToString(), Data = data};

        public static AppResult<T> Error(string message = null, T data = null) => Result(AppStatus.Error, message, data);

        public static AppResult<T> Success(string message = null, T data = null) => Result(AppStatus.Success, message, data);

        public static AppResult<T> Fail(string message = null, T data = null) => Result(AppStatus.Fail, message, data);

        public static AppResult<T> NotFound(string message = null, T data = null) => Result(AppStatus.NotFound, message, data);

        public static AppResult<T> ModelError(string message = "Please provide valid data", T data = null) => Result(AppStatus.ModelError, message, data);
    }

    public enum AppStatus
    {
        Success = 200,
        Error = 500,
        Fail = 400,
        NotFound = 404,
        ModelError = 400,
        OutsideZone = 416,
        IncorrentPin = 5001
    }
}
