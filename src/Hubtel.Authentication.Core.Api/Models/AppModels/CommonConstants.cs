namespace Hubtel.Authentication.Core.Api.Models.AppModels
{
    public static class AppResponses
    {
        public const string InvalidMobileNumber = "Your mobile number is invalid.";
        public const string InvalidAppId= "Invalid app Id";
        public const string InvalidPayload = "Invalid Payload";

        public const string FingerPrintValidationFailure =
            "Something went wrong. You may have breached a security protocol";

        public const string InvalidRequestId =
            "Hmmm... Something doesn't look right with your login request. Please start the login process again.";

        public const string InvalidOtp = "The OTP you entered is not correct.";
        public const string InvalidPin = "The Pin you entered is not correct.";
        public const string PinRequired = "Account requires PIN to login";
        public const string OtpExpired = "Your login duration took longer than expected";

        public const string OtpAttemptsExceed =
            "Looks like you have used up all your attempts for an OTP. Wait a few minutes and start the process again.";

        public const string AppMayNotBeConfigured =
            "App may not have been configured";

        public const string AppConfigNotUpdated =
            "could not update aplication configuration. Please contact the administrator";
        public const string PatchEndpointAcceptedOperation =
            "This endpoint only accepts the replace patch operation.";

        public const string PhoneNumberMayNotExist =
            "Looks like your phone number has not yet been configured for login. Please contact the administrator";
        public const string PhoneNumberNotAssociatedWithAccount =
            "This number is not associated with any business";
        public const string EmailMayNotExist =
            "Looks like your email has not yet been configured for login. Please contact the administrator";
        public const string HttpRequestFailed = "Sorry, we encountered an unexpected error while processing your request. Please contact support if the problem persists.";
        public const string TooManyRequests = "You have reached the limit for otp requests. Please try again later.";

        public const string AppConfigUpdated = "Successfully updated app configuration";
        public const string AppConfigApproved = "Successfully approved app configuration";
        public const string UnknownError = "Unknown Error";
        public const string LoginFailed = "Login failed";
        public const string CouldNotFindAuthConfig = "Could not find auth config with the specified Id";
        public const string CouldNotUpdateAuthConfig = "Could not update auth config with the specified Id";
        public const string CouldNotUpdateWhiteList = "Could not update whitelist with the specified Id";
        public const string SuccessfullyRemovedChannels = "Successfully removed specified authentication channels";
        public const string SuccessfullyRemovedAuthenticationTypes = "Successfully removed specified authentication channels";
        public const string SuccessfullyAddedChannels = "Successfully added specified authentication channel(s)";
        public const string SuccessfullyAddedExternalLogins = "Successfully added specified authentication external login(s)";
        public const string SuccessfullyAddedWhiteListedNumbers = "Successfully added white listed number(s)";

        public const string ChannelsMayNotBeConfigured =
            "Channels may not have been configured for your application yet.";

        public const string YouNeedToProvideWhiteList = "Please provide a list of numbers to be added";
        public const string YouNeedToProvideWhiteListToRemove = "Please provide a list of numbers to be removed";
        public const string SuccessfullyRemovedWhiteListedNumbers = "Successfully removed white listed number(s)";
        public const string CouldNotRetrieveWhiteList = "Error Occured. Could not retrieve application whitelist(s)";
        public const string AccountInformationIsNull = "Account Information Supplied for token generation was NULL";
        public const string EmailIsInvalid = "ProductEmail provided is invalid.";
        public const string AuthCookieKey = "auth_token";
        public const string FailedToUpdateConfigWithId = "Failed to update configuration with id {Id}";
    }
  
}