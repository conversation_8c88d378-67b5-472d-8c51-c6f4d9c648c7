using System.Collections.Generic;

namespace Hubtel.Authentication.Core.Api.Models.AppModels
{
    public class NotificationData
    {
        public string FCMSound { get; set; }
        public string ChannelId { get; set; }
        public List<string> CC { get; set; }
        public string TemplateId { get; set; }
        public string Type { get; set; }
        public string Destination { get; set; }
        public string SenderId { get; set; }

        public string Subject { get; set; }
        
        public string DeepLink { get; set; }
        public string AppDeepLink { get; set; }
        public string HubtelSalesFcmId { get; set; }

        public string EducationFcmId { get; set; }
        public IEnumerable<string> SenderDestination { get; set; }
        
        public string AttachedFile { get; set; }
        
        public string DisplayEmail { get; set; }

        public string Title { get; set; }
         public string Subtitle { get; set; }
        
        public Dictionary<string,object> Data { get; set; }
    }

    public enum MobileNotificationOptions
    {
        SMS,
        fcm,
        stream,
        email
    }

    public enum NotificationTypes
    {
        email,
        sms,
        fcm,
        multiple
    }
}
