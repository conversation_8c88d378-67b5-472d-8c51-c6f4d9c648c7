using System;
using System.Collections.Generic;

namespace Hubtel.Authentication.Core.Api.Models
{
    /// <summary>
    /// Represents a paged result of items.
    /// </summary>
    public class PagedResult<T> : PagedResultBase where T : class
    {
        /// <summary>
        /// Gets or sets the list of results.
        /// </summary>
        /// <typeparam>The type of the results.</typeparam>
        /// <value>The list of results.</value>
        public IList<T> Results { get; set; }

        /// <summary>
        /// Represents a paged result.
        /// </summary>
        public PagedResult()
        {
            Results = new List<T>();
        }

        /// <summary>
        /// Represents a paged result containing a collection of items on a specific page.
        /// </summary>
        public PagedResult(IEnumerable<T> result, int pageIndex, int pageSize, long count)
        {
            this.Results = (IList<T>)result;
            this.PageIndex = pageIndex;
            this.TotalCount = (int)count;
            this.TotalPages = (int)Math.Ceiling(count / (double)pageSize);
        }
    }

    /// <summary>
    /// Provides extension methods for manipulating paged result data.
    /// </summary>
    public static class PagedResultExtensions
    {
        /// <summary>
        /// Converts a collection to a paged result.
        /// </summary>
        /// <typeparam>The type of objects in the collection.</typeparam>
        /// <typeparam name="T">the type parameter.</typeparam>
        /// <param name="result">The collection to be converted.</param>
        /// <param name="pageIndex">The index of the page.</param>
        /// <param name="pageSize">The size of the page.</param>
        /// <param name="count">The total count of the collection.</param>
        /// <returns>A new instance of PagedResult{T} with the provided collection, page index, page size, and count.</returns>
        public static PagedResult<T> ToPagedResult<T>(this IEnumerable<T> result, int pageIndex, int pageSize,
            long count = 0)
            where T : class
        {
            return new PagedResult<T>(result, pageIndex, pageSize, count);
        }
    }
}