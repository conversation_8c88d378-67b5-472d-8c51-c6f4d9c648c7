using System.ComponentModel.DataAnnotations;

namespace Hubtel.Authentication.Core.Api.Models
{
    public class BaseFilter
    {

        [Range(1, int.MaxValue)]
        public int Page { get; set; } = 1;
        [Range(1, int.MaxValue)]
        public int PageSize { get; set; } = 10;

        public string SortColumn { get; set; } = "";
        public string SortDir { get; set; } = "asc";
        public string SearchTerm { get; set; } = string.Empty;
    }
}