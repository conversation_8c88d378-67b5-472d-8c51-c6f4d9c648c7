using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Hubtel.Authentication.Commons.Models;
using Hubtel.Authentication.Core.Api.Data.Audit;
using Newtonsoft.Json;

namespace Hubtel.Authentication.Core.Api.Dtos;

/// <summary>
/// Represents a request to update the authentication configuration store.
/// </summary>
public class UpdateAuthConfigStoreRequest : IUpdatedBy
{
    /// <summary>
    /// Represents a request to update the authentication configuration store.
    /// </summary>
    [Required]
    public string ProductName { get; set; }

    /// <summary>
    /// Represents a product logo for the authentication configuration store.
    /// </summary>
    [Required]
    public string ProductLogo { get; set; }

    /// <summary>
    /// Represents the name of the application associated with the authentication configuration store.
    /// </summary>
    [Required]
    public string AppName { get; set; }

    /// <summary>
    /// Represents a request object used to update the authentication configuration store.
    /// </summary>
    
    public string AccountLookUpUrl { get; set; }
    /// <summary>
    /// Represents a request object used to update the authentication configuration store.
    /// </summary>
    public string EmailLookUpUrl { get; set; }
    /// .
    [Required]
    public string Primary { get; set; }

    /// <summary>
    /// Represents the SenderId property of the UpdateAuthConfigStoreRequest class.
    /// </summary>
    [Required]
    public string SenderId { get; set; }

    /// <summary>
    /// Represents the BgLight property of the UpdateAuthConfigStoreRequest class.
    /// </summary>
    [Required]
    public string BgLight { get; set; }

    /// <summary>
    /// Represents the hover state property of the UpdateAuthConfigStoreRequest class.
    /// </summary>
    [Required]
    public string HoverState { get; set; }

    /// <summary>
    /// Gets or sets the signing key.
    /// </summary>
    /// <remarks>
    /// The signing key is used to sign authentication tokens for secure validation and verification.
    /// </remarks>
    /// <value>
    /// The signing key.
    /// </value>
    [Required]
    public string SigningKey { get; set; }

    /// <summary>
    /// Represents the validity period of an authentication configuration store update request.
    /// </summary>

    public double ValidityPeriod { get; set; } = 180;

    /// <summary>
    /// Represents an entity that has an updated by property.
    /// </summary>
    [Required]
    public string UpdatedBy { get; set; }


    /// <summary>
    /// Gets or sets a value indicating whether the phone number can be skipped.
    /// </summary>
    /// <value><c>true</c> if the phone number can be skipped; otherwise, <c>false</c>.</value>
    
    [JsonRequired]
    public bool CanSkipPhoneNumber { get; set; } 

    /// <summary>
    /// Gets or sets the status for the app config.
    /// </summary>
    
    [JsonRequired]
    public bool IsActive { get; set; } 

    /// <summary>
    /// Gets or sets the default authentication channel for logging in to the application.
    /// </summary>
    
    [JsonRequired]
    public AuthenticationChannel DefaultChannel { get; set; }

    /// <summary>
    /// Gets or sets the OTP liability policy.
    /// </summary>
    public string OtpLiabilityPolicy { get; set; }
    /// <summary>
    /// Represents the email property of the registered application.
    /// </summary>
    /// <remarks>
    /// This property stores the email associated with the back office authentication configuration store response.
    /// </remarks>
    public string ProductEmail { get; set; }
    /// <summary>
    /// Gets or sets the date and time when the entity was last updated.
    /// </summary>
    /// <remarks>
    /// This property is used to track the last update time of the entity.
    /// </remarks>
    public DateTime? UpdatedAt { get; set; }=DateTime.Now;
    /// <summary>
    /// Gets or sets a value indicating whether the user has notification access.
    /// </summary>
    public string TermsOfServiceUrl { get; set; }

    /// <summary>
    ///Gets or sets a value indicating if the product logo should be displayed.
    /// </summary>
    [JsonRequired]
    public bool HideProductLogo { get; set; }

    /// <summary>
    /// Gets or sets a value indicating if the terms and conditions should be displayed.
    /// </summary>
    [JsonRequired]
    public bool ShowTermsAndConditions { get; set; }
    /// <summary>
    /// Gets or sets the audience for the authentication configuration.
    /// </summary>
    public string Audience { get; set; }
    public string Issuer { get; set; }
    [JsonProperty(Order = 2)]
    public IEnumerable<string> AllowedRedirectUrls { get; set; }

}