namespace Hubtel.Authentication.Core.Api.Dtos
{
    /// <summary>
    /// Represents a request for external login configuration.
    /// </summary>
    public class ExternalLoginConfigurationRequest
    {
        /// <summary>
        /// Gets or sets the client ID.
        /// </summary>
        public string ClientId { get; set; }

        /// <summary>
        /// Gets or sets the secret.
        /// </summary>
        public string Secret { get; set; }

        /// <summary>
        /// Gets or sets the authority.
        /// </summary>
        public string Authority { get; set; }

        /// <summary>
        /// Gets or sets the type.
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// Gets or sets the scope ID.
        /// </summary>
        public string ScopeId { get; set; }
        /// <summary>
        /// Gets or sets a value indicating whether phone lookup is disabled during authentication.
        /// </summary>
        public bool DisablePhoneLookup { get; set; } = false;
    }
}
