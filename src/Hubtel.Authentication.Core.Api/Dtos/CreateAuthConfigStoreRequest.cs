using System.Collections;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Hubtel.Authentication.Commons.Models;
using Hubtel.Authentication.Core.Api.Data.Audit;
using Newtonsoft.Json;

namespace Hubtel.Authentication.Core.Api.Dtos;

public record CreateAuthConfigStoreRequest : ICreatedBy
{
    
    /// <summary>
    /// Gets or sets the name of the product.
    /// </summary>
    /// <value>
    /// The name of the product.
    /// </value>
    [Required]
    public string ProductName { get; set; }

    /// <summary>
    /// Gets or sets the logo of the product.
    /// </summary>
    /// <value>
    /// The logo of the product.
    /// </value>
    [Required]
    public string ProductLogo { get; set; }

    /// <summary>
    /// Gets or sets the name of the client application.
    /// </summary>
    /// <value>
    /// A string representing the name of the client application.
    /// </value>
    [Required]
    public string AppName { get; set; }

    /// <summary>
    /// Gets or sets the URL used for account lookup.
    /// </summary>
    /// <value>
    /// The account lookup URL.
    /// </value>
    
    public string AccountLookUpUrl { get; set; }
    /// <summary>
    /// Gets or sets the URL used for customer info lookup by email.
    /// </summary>
    /// <value>
    /// The account lookup URL.
    /// </value>
    public string EmailLookUpUrl { get; set; }
    /// <summary>
    /// Gets or sets the primary colour.
    /// </summary>
    /// <value>
    /// The primary colour.
    /// </value>
    [Required]
    public string Primary { get; set; }

    /// <summary>
    /// Gets or sets the sender ID of the client application.
    /// </summary>
    /// <value>
    /// The sender ID.
    /// </value>
    [Required]
    public string SenderId { get; set; }

    /// <summary>
    /// Gets or sets the value for the BgLight property.
    /// </summary>
    /// <value>
    /// The value for the BgLight property.
    /// </value>
    [Required]
    public string BgLight { get; set; }

    /// <summary>
    /// Gets or sets the hover state of the client application.
    /// </summary>
    /// <value>
    /// The hover state of the client application.
    /// </value>
    [Required]
    public string HoverState { get; set; }

    /// <summary>
    /// Gets or sets the signing key.
    /// </summary>
    /// <value>
    /// The signing key.
    /// </value>
    [Required]
    [MinLength(32, ErrorMessage = "Signing key must be at least 32 characters long")]
    public string SigningKey { get; set; }

    /// <summary>
    /// Represents the validity period of a configuration store in the authentication system.
    /// </summary>
    /// <remarks>
    /// The ValidityPeriod property determines the duration for which a configuration store is considered valid.
    /// This property is used in the CreateAuthConfigStoreRequest class to specify the validity period of the configuration store.
    /// </remarks>
    [JsonRequired]
    [Required]
    public double ValidityPeriod { get; set; }


    /// <summary>
    /// Gets or sets a value indicating whether the phone number can be skipped.
    /// </summary>
    /// <remarks>
    /// This property determines whether the user is required to provide a phone number during the authentication process.
    /// If set to 'true', the phone number can be skipped, and the user can proceed without providing a phone number.
    /// If set to 'false', the phone number is required, and the user must provide a valid phone number during authentication.
    /// </remarks>
    /// <value>
    /// <c>true</c> if the phone number can be skipped; otherwise, <c>false</c>.
    /// </value>

    public bool CanSkipPhoneNumber { get; set; } = false;

    /// <summary>
    /// Gets or sets the name of the entity creator.
    /// </summary>
    [Required]
    public string CreatedBy { get; set; }

    /// <summary>
    /// Gets or sets the the default authentication channel
    /// </summary>
    public AuthenticationChannel? DefaultChannel { get; set; }=AuthenticationChannel.Ussd;
    /// <summary>
    /// Gets or sets the OTP liability policy.
    /// </summary>
    public string OtpLiabilityPolicy { get; set; }

    /// <summary>
    /// Represents the email property of the registered application.
    /// </summary>
    /// <remarks>
    /// This property stores the email associated with the back office authentication configuration store response.
    /// </remarks>
    public string ProductEmail { get; set; }
    /// <summary>
    /// Gets or sets the Terms of Service URL for the authentication configuration.
    /// </summary>
    public string TermsOfServiceUrl { get; set; }

    /// <summary>
    ///Gets or sets a value indicating if the product logo should be displayed.
    /// </summary>

    public bool HideProductLogo { get; set; } = false;

    /// <summary>
    /// Gets or sets a value indicating if the terms and conditions should be displayed.
    /// </summary>

    public bool ShowTermsAndConditions { get; set; } = false;

    public string Audience { get; set; }
    public string Issuer { get; set; }
    [JsonProperty(Order = 2)]
    public IEnumerable<string> AllowedRedirectUrls { get; set; }

}