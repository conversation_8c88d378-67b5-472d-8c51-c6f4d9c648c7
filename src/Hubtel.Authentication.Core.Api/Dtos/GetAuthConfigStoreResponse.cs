using System.Text.Json.Serialization;

namespace Hubtel.Authentication.Core.Api.Dtos;

/// <inheritdoc />
public class GetAuthConfigStoreResponse : AuthConfigStoreResponse
{
    

    /// <summary>
    /// Gets or sets the URL used for account lookup.
    /// </summary>
    /// <value>
    /// The account lookup URL.
    /// </value>
    [Newtonsoft.Json.JsonIgnore]
    [JsonIgnore]
    public override string AccountLookUpUrl { get; set; }
    [Newtonsoft.Json.JsonIgnore]
    [JsonIgnore]
    public override string EmailLookUpUrl { get; set; }
    /// <summary>
    /// Gets or sets the signing key.
    /// </summary>
    /// <value>
    /// The signing key.
    /// </value>
    [Newtonsoft.Json.JsonIgnore]
    [JsonIgnore]
    public override string SigningKey { get; set; }

    /// <summary>
    /// Represents a the response from the authentication configuration store.
    /// </summary>
    public override bool CanSkipPhoneNumber { get; set; }

    /// <summary>
    /// Gets or sets the validity period of the property.
    /// </summary>
    /// <value>
    /// The validity period. Default value is 0.
    /// </value>
    [Newtonsoft.Json.JsonIgnore]
    [JsonIgnore]
    public override double ValidityPeriod { get; set; }
    
}