using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;

namespace Hubtel.Authentication.Core.Api.Dtos;


/// <summary>
/// Represents the response for retrieving the back office authentication configuration store.
/// </summary>
public class GetBackOfficeAuthConfigStoreResponse : AuthConfigStoreResponse
{
    /// <summary>
    /// Gets or sets the list of whitelisted numbers.
    /// </summary>
    [JsonPropertyOrder(1)]
    public IEnumerable<WhiteListedNumber> WhiteLists { get; set; } = Enumerable.Empty<WhiteListedNumber>();

    /// <summary>
    /// Gets or sets the OTP liability policy.
    /// </summary>
    public string OtpLiabilityPolicy { get; set; }
    /// <summary>
    /// Represents the email property of the registered application.
    /// </summary>
    /// <remarks>
    /// This property stores the email associated with the back office authentication configuration store response.
    /// </remarks>
    public string ProductEmail { get; set; }
}

   
