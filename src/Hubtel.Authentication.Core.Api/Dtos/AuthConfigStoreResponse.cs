using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;
using Hubtel.Authentication.Commons.Models;
using Hubtel.Authentication.Core.Api.Data.Audit;
using Hubtel.Authentication.Core.Api.Data.Entities;
using Newtonsoft.Json;

namespace Hubtel.Authentication.Core.Api.Dtos;

public abstract class AuthConfigStoreResponse:IAuditable
{
    protected AuthConfigStoreResponse()
    {
        Channels = Enumerable.Empty<string>();
    }
    /// <summary>
    /// Gets or sets the name of the product.
    /// </summary>
    /// <value>
    /// The name of the product.
    /// </value>
    public string ProductName { get; set; }

    /// <summary>
    /// Gets or sets the logo of the product.
    /// </summary>
    /// <value>
    /// The logo of the product.
    /// </value>
    public string ProductLogo { get; set; }

    /// <summary>
    /// Gets or sets the name of the client application.
    /// </summary>
    /// <value>
    /// A string representing the name of the client application.
    /// </value>
    [JsonPropertyOrder(-1)]
    public string AppName { get; set; }

    /// <summary>
    /// Gets or sets the URL used for account lookup by phone number.
    /// </summary>
    /// <value>
    /// The account lookup URL.
    /// </value>

    public virtual string AccountLookUpUrl { get; set; }
    /// <summary>
    /// Gets or sets the URL used for account lookup by email.
    /// </summary>
    /// <value>
    /// The account lookup URL.
    /// </value>
    public virtual string EmailLookUpUrl { get; set; }

    /// <summary>
    /// Gets or sets the unique identifier for the application.
    /// </summary>
    /// <value>
    /// The unique identifier for the object.
    /// </value>
    [JsonPropertyOrder(-2)]
    public Guid Id { get; set; }

    /// <summary>
    /// Gets or sets the primary colour.
    /// </summary>
    /// <value>
    /// The primary colour.
    /// </value>
    public string Primary { get; set; }

    /// <summary>
    /// Gets or sets the sender ID of the client application.
    /// </summary>
    /// <value>
    /// The sender ID.
    /// </value>
    public virtual string SenderId { get; set; }

    /// <summary>
    /// Gets or sets the value for the BgLight property.
    /// </summary>
    /// <value>
    /// The value for the BgLight property.
    /// </value>
    public string BgLight { get; set; }


    /// <summary>
    /// Gets or sets the hover state of the client application.
    /// </summary>
    /// <value>
    /// The hover state of the client application.
    /// </value>
    public string HoverState { get; set; }

    /// <summary>
    /// Gets or sets the signing key.
    /// </summary>
    /// <value>
    /// The signing key.
    /// </value>

    public virtual string SigningKey { get; set; }


    /// <summary>
    /// Gets or sets a value indicating whether the phone number can be skipped.
    /// </summary>
    /// <remarks>
    /// This property is used in the context of authentication configuration.
    /// When set to true, it indicates that the user can skip providing a phone number during the authentication process.
    /// When set to false, it indicates that the user must provide a phone number during the authentication process.
    /// </remarks>
    public virtual bool CanSkipPhoneNumber { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the client application is active.
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Gets or sets the validity period of the property.
    /// </summary>
    /// <value>
    /// The validity period. Default value is 0.
    /// </value>
    public virtual double ValidityPeriod { get; set; }

    /// <summary>
    /// Gets or sets the name of the person who created the object.
    /// </summary>
    /// <value>
    /// The name of the creator.
    /// </value>
    public string CreatedBy { get; set; }

    /// <summary>
    /// Gets or sets the name of the person who last updated the property.
    /// </summary>
    /// <value>
    /// The name of the person who last updated the property.
    /// </value>
    public string UpdatedBy { get; set; }

    /// <summary>
    /// Gets or sets the date and time when the object was created.
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Gets or sets the date and time when the property was last updated.
    /// </summary>
    public DateTime? UpdatedAt { get; set; }
    /// <summary>
    /// Gets or sets the default authentication channel for logging in to the application.
    /// </summary>
    public AuthenticationChannel DefaultChannel { get; set; }
    /// <summary>
    /// Returns all the authentication channels configured for this application.
    /// </summary>
    public IEnumerable<string> Channels { get; init; }
    /// <summary>
    /// Returns all the external authentication channels configured for this application.
    /// </summary>
    public IEnumerable<ExternalLoginConfiguration> ExternalChannels { get; set; } = Enumerable.Empty<ExternalLoginConfiguration>();
    /// <summary>
    /// Gets or sets the Terms of Service URL for the authentication configuration.
    /// </summary>
    public string TermsOfServiceUrl { get; set; }
    /// <summary>
    ///Gets or sets a value indicating if the product logo should be displayed.
    /// </summary>
    public bool HideProductLogo { get; set; }
    /// <summary>
    /// Gets or sets a value indicating if the terms and conditions should be displayed.
    /// </summary>
    public bool ShowTermsAndConditions { get; set; }
    public string Audience { get; set; }
    public string Issuer { get; set; }
    [JsonProperty(Order = 1)]
    public IEnumerable<string> AllowedRedirectUrls { get; set; }

}