using System.ComponentModel.DataAnnotations;

namespace Hubtel.Authentication.Core.Api.Dtos;

/// <summary>
/// Represents a class for storing white-listed phone numbers.
/// </summary>
public class WhiteListedNumber
{
    /// <summary>
    /// Represents a phone number.
    /// </summary>
    [StringLength(20)]
    public string PhoneNumber { get; set; }

    /// <summary>
    /// Represents the One Time Password (OTP) property.
    /// </summary>
    /// <remarks>
    /// This property is used for storing the One Time Password (OTP) value.
    /// The OTP is typically a randomly generated number or code that is valid for a single use only.
    /// </remarks>
    [StringLength(10)]
    public string Otp { get; set; }
}