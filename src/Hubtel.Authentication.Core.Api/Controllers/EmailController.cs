using Hubtel.PhoneNumbers.Extensions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Swashbuckle.AspNetCore.Annotations;
using System.Net.Mime;
using System.Threading.Tasks;
using Hubtel.Authentication.Core.Api.Models;
using Hubtel.Authentication.Core.Api.Models.AppModels;
using Hubtel.Authentication.Core.Api.Models.Requests;
using Hubtel.Authentication.Core.Api.Models.Responses;
using Hubtel.Authentication.Core.Api.Services.Interface;
using Hubtel.Authentication.Core.Api.Services.Provider;
using System;
using Hubtel.Authentication.Core.Api.Extensions;
using System.Text.RegularExpressions;
using System.Linq;
using Hubtel.Authentication.Core.Api.Data.Core;
using System.Globalization;
using Microsoft.AspNetCore.RateLimiting;
using Hubtel.Authentication.Core.Api.Constants;
using Hubtel.Otel.Instrumentation.Extensions;

namespace Hubtel.Authentication.Core.Api.Controllers
{
    [Route("[controller]")]
    [ApiController]
    [Authorize(AuthenticationSchemes = "Basic")]
    public class EmailController : HubtelApiControllerBase
    {
        private readonly IEmailService _otpService;
        private readonly IPhoneNumberParser _phoneNumberParser;
        private readonly ILogger<EmailController> _logger;
        private readonly IAccountLookupService _accountLookupService;
        private readonly IJwtService _jwtService;

        public EmailController(IEmailService otpService,
            IPhoneNumberParser phoneNumberParser,
            ILogger<EmailController> logger, IAccountLookupService accountLookupService,
            IJwtService jwtService)
        {
            _otpService = otpService;
            _phoneNumberParser = phoneNumberParser;
            _logger = logger;
            _accountLookupService = accountLookupService;
            _jwtService= jwtService;
        }

        /// <summary>
        /// Sends Consumer Otp
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("send")]
        [Consumes(MediaTypeNames.Application.Json)]
        [Produces(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GenerateOtpResponse>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<EmptyDto>))]
        [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<EmptyDto>))]
        [SwaggerOperation(nameof(SendOtp), OperationId = nameof(SendOtp))]
        [EnableRateLimiting(CommonStringConstants.OtpRateLimitPolicy)]
        public async Task<IActionResult> SendOtp([FromBody] GenerateEmailOtpDto model)
        {
            if (!_phoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, model.PhoneNumber.Length > 10 ? string.Empty : model.CountryCode, out var correctMobileNumber))
            {
                _logger.LogWarning("invalid phone number {PhoneNumber} req: {Request}",model.PhoneNumber ,model.ToFormattedJsonString());
                return StatusCode(StatusCodes.Status400BadRequest, new ApiResponse<EmptyDto>(AppResponses.InvalidMobileNumber, null)
                {
                    Code = $"{StatusCodes.Status400BadRequest}"
                });
            }
            
            var accountDetails = await _accountLookupService.RetrieveCachedAccountInfo(model.AppId, model.PhoneNumber);
            if (accountDetails == null)
            {
                var errorResponse = new ApiResponse<EmptyDto>(AppResponses.OtpExpired, null)
                {
                    Code = $"{StatusCodes.Status400BadRequest}"
                };
                
                return StatusCode(StatusCodes.Status400BadRequest,errorResponse);
            }
            var appConfig = await _accountLookupService.ClientAppDetails(model.AppId);
            if (appConfig == null)
            {
                _logger.LogError("ClientAppDetails for AppId: {AppId} returned null", model.AppId);
                return StatusCode(StatusCodes.Status400BadRequest,
                    new ApiResponse<EmptyDto>(AppResponses.AppMayNotBeConfigured, null)
                    {
                        Code = $"{StatusCodes.Status400BadRequest}"
                    });
            }
            var phoneNumberLookupResponse =
                    await _accountLookupService.LookUpCustomerInfoByPhoneNumber(appConfig.AccountLookUpUrl,
                        model.PhoneNumber,linkedEmail:model.LinkedEmail);
            if (phoneNumberLookupResponse == null)
            {
                _logger.LogError(
                    "Error getting AccountLookup information from: {AccountLookupUrl} and PhoneNumber: {PhoneNumber}",
                    appConfig.AccountLookUpUrl, model.PhoneNumber);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    new ApiResponse<EmptyDto>(AppResponses.HttpRequestFailed, null)
                    {
                        Code = $"{StatusCodes.Status500InternalServerError}"
                    });
            }
            if (int.Parse(phoneNumberLookupResponse.Code) == StatusCodes.Status403Forbidden)
            {
                return StatusCode(Convert.ToInt32(phoneNumberLookupResponse.Code),
                    new ApiResponse<PhoneNumberLookupData>(phoneNumberLookupResponse.Message, phoneNumberLookupResponse.Data)
                    {
                        Code = phoneNumberLookupResponse.Code
                    });
            }
            if (int.Parse(phoneNumberLookupResponse.Code) == StatusCodes.Status404NotFound)
            {
                return NotFound(
                    new ApiResponse<PhoneNumberLookupData>(phoneNumberLookupResponse.Message, phoneNumberLookupResponse.Data)
                    {
                        Code = phoneNumberLookupResponse.Code
                    });
            }
            if (int.Parse(phoneNumberLookupResponse.Code) == StatusCodes.Status500InternalServerError)
            {
                return StatusCode(StatusCodes.Status500InternalServerError,
                    new ApiResponse<PhoneNumberLookupData>(phoneNumberLookupResponse.Message, phoneNumberLookupResponse.Data)
                    {
                        Code= phoneNumberLookupResponse.Code
                    });
            }
            await _accountLookupService.SaveAccountInfoToCache(model.AppId, phoneNumberLookupResponse.Data);
            model.PhoneNumber = correctMobileNumber;
            model.LinkedEmail = accountDetails.Email;
            var response = await _otpService.SendOtpAsync(model, appConfig);
            response.Data.Account = new
            {
                MobileNumber = phoneNumberLookupResponse.Data.MobileNumber.ToRedactedNumber(),
                Email = string.IsNullOrWhiteSpace(phoneNumberLookupResponse.Data.Email)
                        ? null
                        : phoneNumberLookupResponse.Data.Email.ToRedactedEmail()
            };
            return StatusCode(int.Parse(response.Code), response);
        }

        /// <summary>
        /// Verify Consumer Otp
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("verify")]
        [Consumes(MediaTypeNames.Application.Json)]
        [Produces(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<VerifyOtpResponse>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<EmptyDto>))]
        [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<EmptyDto>))]
        [SwaggerOperation(nameof(VerifyOtp), OperationId = nameof(VerifyOtp))]
        public async Task<IActionResult> VerifyOtp([FromBody] VerifyEmailOtpDto model)
        {
            if (!_phoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, model.PhoneNumber.Length > 10 ? string.Empty : "GH", out var correctMobileNumber))
            {
                _logger.LogWarning("invalid phone number {PhoneNumber} req: {Request}",model.PhoneNumber ,JsonConvert.SerializeObject(model));
                return StatusCode(StatusCodes.Status400BadRequest, new ApiResponse<EmptyDto>(AppResponses.InvalidMobileNumber, null)
                {
                    Code = $"{StatusCodes.Status400BadRequest}"
                });
            }
            var accountDetails = await _accountLookupService.RetrieveCachedAccountInfo(model.AppId, model.PhoneNumber);
            if (accountDetails == null)
            {
                return StatusCode(StatusCodes.Status400BadRequest,
                    new ApiResponse<EmptyDto>(AppResponses.AppMayNotBeConfigured, null)
                    {
                        Code = $"{StatusCodes.Status400BadRequest}"
                    });
            }
            model.PhoneNumber = correctMobileNumber;
            var response = await _otpService.VerifyOtpAsync(model);
            if (response.Data != null)
            {
                var cookieOptions = new CookieOptions
                {
                    HttpOnly = true,
                    Secure = true,
                    SameSite = SameSiteMode.Unspecified,
                    Expires = DateTime.Parse(response.Data.Expiry, CultureInfo.CurrentCulture)
                };
                Response.Cookies.Append(AppResponses.AuthCookieKey, response.Data.Token, cookieOptions);
            }
            return StatusCode(int.Parse(response.Code), response);
        }

        [HttpGet("{appId}/lookup")]
        [Consumes(MediaTypeNames.Application.Json)]
        [Produces(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<EmailLookUpData>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<EmailLookUpData>))]
        [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<EmptyDto>))]
        [SwaggerOperation(nameof(CustomerInfoLookUpByEmail), OperationId = nameof(CustomerInfoLookUpByEmail))]
        public async Task<IActionResult> CustomerInfoLookUpByEmail([FromRoute] Guid appId, [FromQuery] string email, [FromQuery] ExternalAuthenticationType authenticationType)
        {
            
            string emailPattern = @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$";
            var regex = new Regex(emailPattern,RegexOptions.None,TimeSpan.FromSeconds(2));
            if (!regex.IsMatch(email))
            {
                return StatusCode(StatusCodes.Status400BadRequest,
                    new ApiResponse<EmptyDto>(AppResponses.EmailIsInvalid, null)
                    {
                        Code = $"{StatusCodes.Status400BadRequest}"
                    });
            }
            var appConfig = await _accountLookupService.ClientAppDetails(appId);
            if (appConfig == null || (string.IsNullOrWhiteSpace(appConfig.AccountLookUpUrl) && string.IsNullOrEmpty(appConfig.EmailLookUpUrl)))
            {
                return StatusCode(StatusCodes.Status400BadRequest,
                    new ApiResponse<EmptyDto>(AppResponses.AppMayNotBeConfigured, null)
                    {
                        Code = $"{StatusCodes.Status400BadRequest}"
                    });
            }
            var customerInformation =
                await _accountLookupService.LookUpCustomerInfoByEmail(appConfig.EmailLookUpUrl, email);
            if (customerInformation == null)
            {
                return StatusCode(StatusCodes.Status404NotFound ,
                    new ApiResponse<EmptyDto>(AppResponses.EmailMayNotExist, null)
                    {
                        Code = $"{StatusCodes.Status404NotFound}"
                    });
            }
            if(customerInformation.Code == $"{StatusCodes.Status403Forbidden}")
            {
                return StatusCode(StatusCodes.Status403Forbidden,
                    new ApiResponse<EmailLookUpData>(customerInformation.Message, customerInformation.Data)
                    {
                        Code = $"{StatusCodes.Status403Forbidden}"
                    });
            }
            if(appConfig.ExternalChannels.Any(e=> e.Type == authenticationType && e.DisablePhoneLookup))
            {
                var accountLookUpData=new PhoneNumberLookupData
                {
                    Email =email,
                    MobileNumber = null,
                   TokenData=customerInformation.Data.TokenData
                };
                var authToken = _jwtService.GenerateAppToken(new TokenGenerationModel { AccountLookupData=accountLookUpData,AuthenticationConfiguration=appConfig});
                if (!authToken.IsSuccessful)
                {
                    _logger.LogError("Customer {Email}, An error occurred verifying otp. Error {ErrorMessage}",
                        email, authToken.Message);
                    return StatusCode(Convert.ToInt32(authToken.Status),new ApiResponse<EmailLookUpData>(authToken.Message, null)
                    {
                        Code=$"{Convert.ToInt32(authToken.Status)}"
                    });
                }
                return Ok(new ApiResponse<Object>()
                {
                    Code=StatusCodes.Status200OK.ToString(),
                    Data= authToken.Data,
                    Message=authToken.Message
                });
            }
            return StatusCode(Convert.ToInt32(customerInformation.Code),
                new ApiResponse<EmailLookUpData>(customerInformation.Message, customerInformation.Data.FilterForEmailOnly())
                {
                    Code = customerInformation.Code
                });

        }
    }
}