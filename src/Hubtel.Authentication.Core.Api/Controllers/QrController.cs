using System;
using Hubtel.PhoneNumbers.Extensions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Swashbuckle.AspNetCore.Annotations;
using System.Net;
using System.Net.Mime;
using System.Threading.Tasks;
using Flurl;
using Flurl.Http;
using Hubtel.Authentication.Core.Api.Models;
using Hubtel.Authentication.Core.Api.Models.AppModels;
using Hubtel.Authentication.Core.Api.Models.Requests;
using Hubtel.Authentication.Core.Api.Models.Responses;
using Hubtel.Authentication.Core.Api.Options;
using Hubtel.Authentication.Core.Api.Services.Interface;
using Microsoft.Extensions.Options;
using Hubtel.Authentication.Core.Api.Extensions;

namespace Hubtel.Authentication.Core.Api.Controllers
{
    [Route("[controller]")]
    [ApiController]
    [Authorize(AuthenticationSchemes = "Basic")]
    public class QrController : HubtelApiControllerBase
    {
        private readonly IQrService _qrService;
        private readonly IPhoneNumberParser _phoneNumberParser;
        private readonly ILogger<QrController> _logger;
        private readonly IAccountLookupService _accountLookupService;
        private readonly QrOptions _qrOptions;

        public QrController(IQrService qrService,
            IPhoneNumberParser phoneNumberParser,
            ILogger<QrController> logger, IAccountLookupService accountLookupService, IOptions<QrOptions> qrOptions)
        {
            _qrService = qrService;
            _phoneNumberParser = phoneNumberParser;
            _logger = logger;
            _accountLookupService = accountLookupService;
            _qrOptions = qrOptions.Value;
        }

        /// <summary>
        /// Generate Qr code 
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("generate")]
        [Consumes(MediaTypeNames.Application.Json)]
        [Produces(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<QrChallengeResponse>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<EmptyDto>))]
        [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<EmptyDto>))]
        [SwaggerOperation(nameof(GenerateQr), OperationId = nameof(GenerateQr))]
        public async Task<IActionResult> GenerateQr([FromQuery] QrChallengeRequest model)
        {
            if (!_phoneNumberParser.IsPhoneNumberValid(model.PhoneNumber,
                    model.PhoneNumber.Length > 10 ? string.Empty : model.CountryCode, out var _))
            {
                _logger.LogWarning("invalid phone number {ModelPhoneNumber} req: {@RequestModel}",
                    model.PhoneNumber,
                    model);
                return StatusCode(StatusCodes.Status400BadRequest,
                    new ApiResponse<EmptyDto>(AppResponses.InvalidMobileNumber, null)
                    {
                        Code = $"{StatusCodes.Status400BadRequest}"
                    });
            }

            var accountDetails = await _accountLookupService.ClientAppDetails(model.AppId);
            if (accountDetails == null)
            {
                _logger.LogError("ClientAppDetails for AppId: {AppId} returned null", model.AppId);
                return StatusCode(StatusCodes.Status400BadRequest,
                    new ApiResponse<EmptyDto>(AppResponses.AppMayNotBeConfigured, null)
                    {
                        Code = $"{StatusCodes.Status400BadRequest}"
                    });
            }

            if (string.IsNullOrWhiteSpace(accountDetails.AccountLookUpUrl))
            {
                _logger.LogError("ClientAppDetails for AppId: {AppId} returned null", model.AppId);
                return StatusCode(StatusCodes.Status400BadRequest,
                    new ApiResponse<EmptyDto>(AppResponses.AppMayNotBeConfigured, null)
                    {
                        Code = $"{StatusCodes.Status400BadRequest}"
                    });
            }

            var lookupResponse =
                await _accountLookupService.LookUpCustomerInfoByPhoneNumber(accountDetails.AccountLookUpUrl,
                    model.PhoneNumber);
            if (lookupResponse == null)
            {
                _logger.LogError(
                    "Error getting AccountLookup information from: {AccountLookupUrl} and PhoneNumber: {PhoneNumber}",
                    accountDetails.AccountLookUpUrl, model.PhoneNumber);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    new ApiResponse<EmptyDto>(AppResponses.HttpRequestFailed, null)
                    {
                        Code = $"{StatusCodes.Status500InternalServerError}"
                    });
            }
            if (int.Parse(lookupResponse.Code)==StatusCodes.Status403Forbidden)
            {
                return StatusCode(Convert.ToInt32(lookupResponse.Code),
                    new ApiResponse<PhoneNumberLookupData>(lookupResponse.Message, lookupResponse.Data)
                    {
                        Code = lookupResponse.Code
                    });
            }
            if (int.Parse(lookupResponse.Code) == StatusCodes.Status500InternalServerError)
            {
                return StatusCode(StatusCodes.Status500InternalServerError,
                    new ApiResponse<PhoneNumberLookupData>(lookupResponse.Message, lookupResponse.Data)
                    {
                        Code=lookupResponse.Code
                    });
            }
            await _accountLookupService.SaveAccountInfoToCache(model.AppId, lookupResponse.Data);
            var response = await _qrService.GenerateQrTokenAsync(model);
            response.Data.Account = new
            {
                MobileNumber = lookupResponse.Data.MobileNumber.ToRedactedNumber(),
                Email = string.IsNullOrWhiteSpace(lookupResponse.Data.Email)
                        ? null
                        : lookupResponse.Data.Email.ToRedactedEmail()
            };
            if (response == null)
            {
                return StatusCode((int)HttpStatusCode.FailedDependency,
                    new ApiResponse<EmptyDto>(AppResponses.UnknownError, null)
                        { Code = $"{(int)HttpStatusCode.FailedDependency}" });
            }

            await _accountLookupService.SaveQrInfoToCache(model.AppId, response.Data.QrCodeData, model.PhoneNumber);

            return StatusCode(int.Parse(response.Code), response);
        }

        /// <summary>
        /// Verify Qr code 
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("confirm")]
        [Consumes(MediaTypeNames.Application.Json)]
        [Produces(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<VerifyQrResponse>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<EmptyDto>))]
        [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<EmptyDto>))]
        [SwaggerOperation(nameof(ConfirmQr), OperationId = nameof(ConfirmQr))]
        public async Task<IActionResult> ConfirmQr([FromBody] QrDataRequest model)
        {
            if (!_phoneNumberParser.IsPhoneNumberValid(model.PhoneNumber,
                    model.PhoneNumber.Length > 10 ? string.Empty : "GH", out var correctMobileNumber))
            {
                _logger.LogWarning("invalid phone number {ModelPhoneNumber} req: {SerializeObject}", model.PhoneNumber,
                    JsonConvert.SerializeObject(model));
                return StatusCode(StatusCodes.Status400BadRequest,
                    new ApiResponse<EmptyDto>(AppResponses.InvalidMobileNumber, null)
                    {
                        Code = $"{StatusCodes.Status400BadRequest}"
                    });
            }

            var cachedQrInfo =
                await _accountLookupService.RetrieveCachedQrInfo(model.AppId, model.QrData, model.PhoneNumber);
            if (cachedQrInfo == null || string.IsNullOrWhiteSpace(cachedQrInfo))
            {
                var errorResponse = new ApiResponse<dynamic>(AppResponses.LoginFailed, null)
                {
                    Code = $"{StatusCodes.Status400BadRequest}",
                    Data = new
                    {
                        MobileNumber = model.PhoneNumber,
                        model.AppId,
                        model.DeviceId
                    },
                    Message = "Login failed"
                };
                _ = await _qrOptions.Failure.AppendPathSegment(model.AppId).PostJsonAsync(errorResponse);
                _logger.LogError(
                    "sending login failure notification for phone number:{PhoneNumber} from request {@Request}",
                    model.PhoneNumber,  JsonConvert.SerializeObject(model));

                return StatusCode(StatusCodes.Status400BadRequest, errorResponse);
            }

            model.PhoneNumber = correctMobileNumber;
            var tokenResponse = await _qrService.VerifyQrTokenAsync(model);
            
          
            _ = await _qrOptions.Success.AppendPathSegment(model.AppId).PostJsonAsync(tokenResponse);
            _logger.LogInformation(
                "sending login success notification for phone number:{PhoneNumber} from request {@Request}",
                model.PhoneNumber, JsonConvert.SerializeObject(model));
            return StatusCode(int.Parse(tokenResponse.Code), tokenResponse);
        }
    }
}