using System;
using System.Net.Mime;
using System.Threading.Tasks;
using Hubtel.Authentication.Core.Api.Dtos;
using Hubtel.Authentication.Core.Api.Models;
using Hubtel.Authentication.Core.Api.Models.AppModels;
using Hubtel.Authentication.Core.Api.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace Hubtel.Authentication.Core.Api.Controllers;

[Route("[controller]")]
[ApiController]
[Authorize(AuthenticationSchemes = "Basic")]
public class AuthConfigsController : HubtelApiControllerBase
{
    private readonly IAuthenticationConfigStoreService _configStoreService;

    public AuthConfigsController(IAuthenticationConfigStoreService configStoreService)
    {
        _configStoreService = configStoreService;
    }

    [HttpGet]
    [Route("")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GetAuthConfigStoreResponse>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<EmptyDto>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<EmptyDto>))]
    [SwaggerOperation(nameof(GetAppConfigs), OperationId = nameof(GetAppConfigs))]
    public async Task<ApiResponse<PagedResult<GetAuthConfigStoreResponse>>> GetAppConfigs([FromQuery] BaseFilter filter)
    {
        return await _configStoreService.GetAsync<GetAuthConfigStoreResponse>(filter);
    }

    [HttpGet]
    [Route("{id:guid}")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GetAuthConfigStoreResponse>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<EmptyDto>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<EmptyDto>))]
    [SwaggerOperation(nameof(GetAppConfig), OperationId = nameof(GetAppConfig))]
    public async Task<ApiResponse<GetAuthConfigStoreResponse>> GetAppConfig(Guid id)
    {
        var response = await _configStoreService.GetCachedClientAsync<GetAuthConfigStoreResponse>(id);
        if (response == null)
        {
            return
                new ApiResponse<GetAuthConfigStoreResponse>(AppResponses.AppMayNotBeConfigured, null)
                {
                    Code = $"{StatusCodes.Status400BadRequest}"
                };
        }

        return new ApiResponse<GetAuthConfigStoreResponse>("application details retrieved", response)
        {
            Code = $"{StatusCodes.Status200OK}"
        };
    }
}