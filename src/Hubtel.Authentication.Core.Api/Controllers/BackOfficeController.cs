using System;
using System.Collections.Generic;
using System.Net.Mime;
using System.Threading;
using System.Threading.Tasks;
using Hubtel.Authentication.Commons.Models;
using Hubtel.Authentication.Core.Api.Data.Core;
using Hubtel.Authentication.Core.Api.Data.Entities;
using Hubtel.Authentication.Core.Api.Dtos;
using Hubtel.Authentication.Core.Api.Models;
using Hubtel.Authentication.Core.Api.Models.AppModels;
using Hubtel.Authentication.Core.Api.Services;
using Mapster;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Swashbuckle.AspNetCore.Annotations;

namespace Hubtel.Authentication.Core.Api.Controllers;

[Route("[controller]")]
[ApiController]
[Authorize(AuthenticationSchemes = "Basic")]
public class BackOfficeController : HubtelApiControllerBase
{
    private readonly IAuthenticationConfigStoreService _configStoreService;


    public BackOfficeController(IAuthenticationConfigStoreService configStoreService, ILogger<BackOfficeController> logger)
    {
        _configStoreService = configStoreService;
    }

    [HttpGet]
    [Route("")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GetBackOfficeAuthConfigStoreResponse>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<EmptyDto>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<EmptyDto>))]
    [SwaggerOperation(nameof(GetAppConfigs), OperationId = nameof(GetAppConfigs))]
    public async Task<ApiResponse<PagedResult<GetBackOfficeAuthConfigStoreResponse>>> GetAppConfigs(
        [FromQuery] BaseFilter filter)
    {
        return await _configStoreService.GetAsync<GetBackOfficeAuthConfigStoreResponse>(filter);
    }
    [HttpGet]
    [Route("pending")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GetBackOfficeAuthConfigStoreResponse>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<EmptyDto>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<EmptyDto>))]
    [SwaggerOperation(nameof(GetAllPendingAppConfigs), OperationId = nameof(GetAllPendingAppConfigs))]
    public async Task<ApiResponse<PagedResult<GetBackOfficeAuthConfigStoreResponse>>> GetAllPendingAppConfigs(
        [FromQuery] BaseFilter filter, CancellationToken cancellationToken)
    {
        return await _configStoreService.GetAllPendingAsync(filter, cancellationToken);
    }

    [HttpGet]
    [Route("channels")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<string[]>))]
    [SwaggerOperation(nameof(GetAllChannels), OperationId = nameof(GetAllChannels))]
    public async Task<ApiResponse<string[]>> GetAllChannels()
    {
        var channels = await Task.FromResult(Enum.GetNames(typeof(AuthenticationChannel)));
        return new ApiResponse<string[]>("channels retrieved", channels)
        {
            Code = $"{StatusCodes.Status200OK}"
        };
    }

    [HttpGet]
    [Route("externallogintypes")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<string[]>))]
    [SwaggerOperation(nameof(GetAllExternalLoginConfigurationTypes), OperationId = nameof(GetAllExternalLoginConfigurationTypes))]
    public async Task<ApiResponse<string[]>> GetAllExternalLoginConfigurationTypes()
    {
        var externalLogins = await Task.FromResult(Enum.GetNames(typeof(ExternalAuthenticationType)));
        return new ApiResponse<string[]>("external login types retrieved", externalLogins)
        {
            Code = $"{StatusCodes.Status200OK}"
        };
    }

    [HttpGet]
    [Route("{id:guid}")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GetBackOfficeAuthConfigStoreResponse>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<EmptyDto>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<EmptyDto>))]
    [SwaggerOperation(nameof(GetAppConfig), OperationId = nameof(GetAppConfig))]
    public async Task<IActionResult> GetAppConfig(Guid id)
    {
        var response = await _configStoreService.GetAsync<GetBackOfficeAuthConfigStoreResponse>(id);
        if (response == null)
        {
            var badResult = HelperApi.CreateApiResponse<GetBackOfficeAuthConfigStoreResponse>(StatusCodes.Status404NotFound,
                AppResponses.AppMayNotBeConfigured, null);

            return NotFound(badResult);
        }

        var result = HelperApi.CreateApiResponse(StatusCodes.Status200OK,
            "application details retrieved", response);

        return Ok(result);
    }

    [HttpGet]
    [Route("{id:guid}/channels")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<List<AuthenticationChannel>>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<EmptyDto>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<EmptyDto>))]
    [SwaggerOperation(nameof(GetAuthenticationChannels), OperationId = nameof(GetAuthenticationChannels))]
    public async Task<ApiResponse<List<AuthenticationChannel>>> GetAuthenticationChannels(Guid id, CancellationToken cancellationToken)
    {
        var response = await _configStoreService.GetAllAuthenticationChannelsAsync(id, cancellationToken);
        if (response == null)
        {
            return
                new ApiResponse<List<AuthenticationChannel>>(AppResponses.ChannelsMayNotBeConfigured, null)
                {
                    Code = $"{StatusCodes.Status400BadRequest}"
                };
        }

        return new ApiResponse<List<AuthenticationChannel>>("application channels retrieved", response.Data)
        {
            Code = $"{StatusCodes.Status200OK}"
        };
    }
    [HttpGet]
    [Route("{id:guid}/externallogins")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<List<ExternalLoginConfiguration>>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<EmptyDto>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<EmptyDto>))]
    [SwaggerOperation(nameof(GetExternalLoginConfigs), OperationId = nameof(GetExternalLoginConfigs))]
    public async Task<ApiResponse<List<ExternalLoginConfiguration>>> GetExternalLoginConfigs(Guid id, CancellationToken cancellationToken)
    {
        var response = await _configStoreService.GetExternalLoginConfigurationAsync(id, cancellationToken);
        if (response == null)
        {
            return
                new ApiResponse<List<ExternalLoginConfiguration>>(AppResponses.ChannelsMayNotBeConfigured, null)
                {
                    Code = $"{StatusCodes.Status400BadRequest}"
                };
        }

        return new ApiResponse<List<ExternalLoginConfiguration>>("application channels retrieved", response.Data)
        {
            Code = $"{StatusCodes.Status200OK}"
        };
    }


    [HttpGet]
    [Route("{id:guid}/whitelists")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<List<WhiteListedNumber>>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<EmptyDto>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<EmptyDto>))]
    [SwaggerOperation(nameof(GetAuthenticationWhitelists), OperationId = nameof(GetAuthenticationWhitelists))]
    public async Task<ApiResponse<List<WhiteListedNumber>>> GetAuthenticationWhitelists(Guid id, CancellationToken cancellationToken)
    {

        var response = await _configStoreService.GetAllAuthenticationWhitelistsAsync(id, cancellationToken);
        if (response == null)
        {
            return
                new ApiResponse<List<WhiteListedNumber>>(AppResponses.CouldNotRetrieveWhiteList, null)
                {
                    Code = $"{StatusCodes.Status400BadRequest}"
                };
        }

        return new ApiResponse<List<WhiteListedNumber>>("application whitelists retrieved", response.Data)
        {
            Code = $"{StatusCodes.Status200OK}"
        };
    }

    [HttpPut]
    [Route("{id:guid}")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GetBackOfficeAuthConfigStoreResponse>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<EmptyDto>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<EmptyDto>))]
    [SwaggerOperation(nameof(UpdateAppConfig), OperationId = nameof(UpdateAppConfig))]
    public async Task<IActionResult>
        UpdateAppConfig([FromRoute] Guid id,
            [FromBody] UpdateAuthConfigStoreRequest appConfig) // Changed Placeholder to AuthenticationConfig
    {
        var response = await _configStoreService.UpdateAsync<GetBackOfficeAuthConfigStoreResponse>(id, appConfig);
        if (!response.IsSaved)
        {
            return BadRequest(new ApiResponse<GetBackOfficeAuthConfigStoreResponse>(AppResponses.AppConfigNotUpdated, null)
            {
                Code = $"{StatusCodes.Status400BadRequest}"
            });
        }

        return Ok(new ApiResponse<GetBackOfficeAuthConfigStoreResponse>(AppResponses.AppConfigUpdated, null)
        {
            Code = $"{StatusCodes.Status200OK}",
            Data = response.Data
        });
    }

    [HttpPut]
    [Route("{id:guid}/approve")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<bool>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<EmptyDto>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<EmptyDto>))]
    [SwaggerOperation(nameof(ApproveApp), OperationId = nameof(ApproveApp))]
    public async Task<ApiResponse<bool>> ApproveApp([FromRoute] Guid id)
    {
        var response = await _configStoreService.ApproveAuthConfiguration(id);
        if (!response.Data)
        {
            return
                new ApiResponse<bool>(AppResponses.CouldNotUpdateAuthConfig, false)
                {
                    Code = $"{StatusCodes.Status400BadRequest}"
                };
        }

        return new ApiResponse<bool>(AppResponses.AppConfigApproved, response.Data)
        {
            Code = $"{StatusCodes.Status200OK}",
            Data = response.Data
        };
    }

    [HttpPut]
    [Route("{id:guid}/AddChannel")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<List<AuthenticationChannel>>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<EmptyDto>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<EmptyDto>))]
    [SwaggerOperation(nameof(AddChannel), OperationId = nameof(AddChannel))]
    public async Task<ApiResponse<List<AuthenticationChannel>>> AddChannel([FromRoute] Guid id,
        List<AuthenticationChannel> channels, CancellationToken cancellationToken)
    {


        var response = await _configStoreService.AddAuthenticationChannelsAsync(id, channels, cancellationToken);
        if (response == null)
        {
            return
                new ApiResponse<List<AuthenticationChannel>>(AppResponses.CouldNotUpdateAuthConfig,
                    new List<AuthenticationChannel>())
                {
                    Code = $"{StatusCodes.Status400BadRequest}"
                };
        }

        return new ApiResponse<List<AuthenticationChannel>>(AppResponses.SuccessfullyAddedChannels, response.Data)
        {
            Code = $"{StatusCodes.Status200OK}",
            Data = response.Data
        };
    }

    [HttpPut]
    [Route("{id:guid}/ExternalLogins")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<List<ExternalLoginConfiguration>>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<EmptyDto>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<EmptyDto>))]
    [SwaggerOperation(nameof(AddExternalLogin), OperationId = nameof(AddExternalLogin))]
    public async Task<ApiResponse<List<ExternalLoginConfiguration>>> AddExternalLogin([FromRoute] Guid id, List<ExternalLoginConfigurationRequest> externalLogins, CancellationToken cancellationToken)
    {

        var externalLoginsConfig = externalLogins.Adapt<List<ExternalLoginConfiguration>>();
        var response = await _configStoreService.AddExternalLoginConfiguration(id, externalLoginsConfig, cancellationToken);
        if (response == null)
        {
            return
                new ApiResponse<List<ExternalLoginConfiguration>>(AppResponses.CouldNotUpdateAuthConfig,
                    new List<ExternalLoginConfiguration>())
                {
                    Code = $"{StatusCodes.Status400BadRequest}"
                };

        }
        else if (response.Code == $"{StatusCodes.Status400BadRequest}")
        {
            return new ApiResponse<List<ExternalLoginConfiguration>>(response.Message, new List<ExternalLoginConfiguration>())
            {
                Code = response.Code
            };
        }
        return new ApiResponse<List<ExternalLoginConfiguration>>(AppResponses.SuccessfullyAddedExternalLogins, response.Data)
        {
            Code = $"{StatusCodes.Status200OK}",
            Data = response.Data
        };
    }


    [HttpPut]
    [Route("{id:guid}/RemoveChannels")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<bool>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<EmptyDto>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<EmptyDto>))]
    [SwaggerOperation(nameof(RemoveChannels), OperationId = nameof(RemoveChannels))]
    public async Task<ApiResponse<bool>> RemoveChannels([FromRoute] Guid id,
        List<AuthenticationChannel> channels, CancellationToken cancellationToken)
    {

        var response = await _configStoreService.RemoveAuthenticationChannelsAsync(id, channels, cancellationToken);
        if (response == null)
        {
            return
                new ApiResponse<bool>(AppResponses.CouldNotUpdateAuthConfig, false)
                {
                    Code = $"{StatusCodes.Status400BadRequest}"
                };
        }

        return new ApiResponse<bool>(AppResponses.SuccessfullyRemovedChannels, response.Data)
        {
            Code = $"{StatusCodes.Status200OK}",
        };

    }
    [HttpDelete]
    [Route("{id:guid}/ExternalLogins")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<bool>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<EmptyDto>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<EmptyDto>))]
    [SwaggerOperation(nameof(RemoveExternalLogins), OperationId = nameof(RemoveExternalLogins))]
    public async Task<ApiResponse<bool>> RemoveExternalLogins([FromRoute] Guid id,
        List<ExternalLoginConfiguration> externalLogins, CancellationToken cancellationToken)
    {

        var response = await _configStoreService.RemoveExternalLoginsAsync(id, externalLogins, cancellationToken);
        if (response == null || !response.Data)
        {
            return
                new ApiResponse<bool>(AppResponses.CouldNotUpdateAuthConfig, false)
                {
                    Code = $"{StatusCodes.Status400BadRequest}"
                };
        }
        return new ApiResponse<bool>(AppResponses.SuccessfullyRemovedAuthenticationTypes, response.Data)
        {
            Code = $"{StatusCodes.Status200OK}",
            Data = response.Data
        };
    }

    [HttpPut]
    [Route("{id:guid}/whitelists")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<List<WhiteListedNumber>>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<EmptyDto>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<EmptyDto>))]
    [SwaggerOperation(nameof(AddWhiteListedNumbersToAuthConfig), OperationId = nameof(AddWhiteListedNumbersToAuthConfig))]
    public async Task<ApiResponse<List<WhiteListedNumber>>> AddWhiteListedNumbersToAuthConfig([FromRoute] Guid id,
        List<WhiteListedNumber> whiteListedNumbers, CancellationToken cancellationToken)
    {

        var response = await _configStoreService.AddWhiteListedNumbersToAuthConfigAsync(id, whiteListedNumbers, cancellationToken);
        if (response == null)
        {
            return
                new ApiResponse<List<WhiteListedNumber>>(AppResponses.CouldNotUpdateWhiteList,
                    new List<WhiteListedNumber>())
                {
                    Code = $"{StatusCodes.Status400BadRequest}"
                };
        }

        return new ApiResponse<List<WhiteListedNumber>>(AppResponses.SuccessfullyAddedWhiteListedNumbers, response.Data)
        {
            Code = $"{StatusCodes.Status200OK}",
            Data = response.Data
        };
    }


    [HttpPut]
    [Route("whitelists/{id:guid}/remove")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<bool>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<EmptyDto>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<EmptyDto>))]
    [SwaggerOperation(nameof(RemoveWhiteListedNumbers), OperationId = nameof(RemoveWhiteListedNumbers))]
    public async Task<ApiResponse<bool>> RemoveWhiteListedNumbers([FromRoute] Guid id, List<string> removals, CancellationToken cancellationToken)
    {

        var response = await _configStoreService.RemoveWhiteListedNumbersFromAuthConfigAsync(id, removals, cancellationToken);
        if (response == null)
        {
            return
                new ApiResponse<bool>(AppResponses.CouldNotUpdateWhiteList, false)
                {
                    Code = $"{StatusCodes.Status400BadRequest}"
                };
        }

        return new ApiResponse<bool>(AppResponses.SuccessfullyRemovedWhiteListedNumbers, response.Data)
        {
            Code = $"{StatusCodes.Status200OK}",
            Data = response.Data
        };
    }

    [HttpPost]
    [Route("")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GetBackOfficeAuthConfigStoreResponse>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<EmptyDto>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<EmptyDto>))]
    [SwaggerOperation(nameof(CreateAppConfig), OperationId = nameof(CreateAppConfig))]
    public async Task<ApiResponse<GetBackOfficeAuthConfigStoreResponse>>
        CreateAppConfig(CreateAuthConfigStoreRequest appConfig)
    {
        return await _configStoreService.AddAsync<GetBackOfficeAuthConfigStoreResponse>(appConfig);
    }

    [HttpDelete]
    [Route("{id:guid}")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<ApiResponse<bool>>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<EmptyDto>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<EmptyDto>))]
    [SwaggerOperation(nameof(DeleteAppConfig), OperationId = nameof(DeleteAppConfig))]
    public async Task<ApiResponse<bool>> DeleteAppConfig(Guid id)
    {
        return await _configStoreService.DeleteAsync(id);
    }

    [HttpPut]
    [Route("{id:guid}/UpdateAllowedUrls")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<bool>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<EmptyDto>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<EmptyDto>))]
    [SwaggerOperation(nameof(UpdateAllowedUrls), OperationId = nameof(UpdateAllowedUrls))]
    public async Task<IActionResult> UpdateAllowedUrls([FromRoute] Guid id,
       List<string> allowedUrls, CancellationToken cancellationToken)
    {

        var response = await _configStoreService.UpdateAllowedUrls(id, allowedUrls, cancellationToken);
        return StatusCode(int.Parse(response.Code), HelperApi.CreateApiResponse(int.Parse(response.Code),response.Message,response.Data));  
    }
}