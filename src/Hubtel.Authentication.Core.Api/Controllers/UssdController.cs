using System;
using Hubtel.PhoneNumbers.Extensions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Swashbuckle.AspNetCore.Annotations;
using System.Net;
using System.Net.Mime;
using System.Threading.Tasks;
using Flurl;
using Flurl.Http;
using Hubtel.Authentication.Commons.Extensions;
using Hubtel.Authentication.Core.Api.Models;
using Hubtel.Authentication.Core.Api.Models.AppModels;
using Hubtel.Authentication.Core.Api.Models.Requests;
using Hubtel.Authentication.Core.Api.Models.Responses;
using Hubtel.Authentication.Core.Api.Options;
using Hubtel.Authentication.Core.Api.Services.Interface;
using Hubtel.Authentication.Core.Api.Services.Provider;
using Hubtel.Redis.Sdk.Services;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using StackExchange.Redis;
using System.Globalization;
using Hubtel.Otel.Instrumentation.Extensions;

namespace Hubtel.Authentication.Core.Api.Controllers
{
    [Route("[controller]")]
    [ApiController]
    [Authorize(AuthenticationSchemes = "Basic")]
    public class UssdController : HubtelApiControllerBase
    {
        private readonly IUssdService _otpService;
        private readonly IPhoneNumberParser _phoneNumberParser;
        private readonly ILogger<UssdController> _logger;
        private readonly IJwtService _jwtService;
        private readonly IAccountLookupService _accountLookupService;
        private readonly SocketOptions _socketOptions;
        private readonly IDatabase _otpCacheRepository;
        private readonly IDatabase _authConfigRedisStore;

        public UssdController(IUssdService otpService,
            IPhoneNumberParser phoneNumberParser,
            ILogger<UssdController> logger,
            IJwtService jwtService,
            IAccountLookupService accountLookupService,
            IMultiRedisHostCacheRepository multiRedisHostCacheRepository, IOptions<SocketOptions> socketOptions)
        {
            _otpService = otpService;
            _phoneNumberParser = phoneNumberParser;
            _logger = logger;
            _jwtService = jwtService;
            _accountLookupService = accountLookupService;
            _otpCacheRepository = multiRedisHostCacheRepository.GetDb(RedisConstants.MainRedisHostName, RedisConstants.DefaultOtpCacheDb);

            _authConfigRedisStore = multiRedisHostCacheRepository.GetDb(RedisConstants.MainRedisHostName, RedisConstants.AuthConfigurationDb);
            _socketOptions = socketOptions.Value;
        }

        /// <summary>
        /// Sends Consumer Otp
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("generate")]
        [Consumes(MediaTypeNames.Application.Json)]
        [Produces(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GenerateUssdOtpResponse>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<EmptyDto>))]
        [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<EmptyDto>))]
        [SwaggerOperation(nameof(Generate), OperationId = nameof(Generate))]
        public async Task<IActionResult> Generate([FromBody] GenerateUssdOtpDto model)
        {
            try
            {
                _logger.LogDebug("Starting method Generate");
                if (!_phoneNumberParser.IsPhoneNumberValid(model.PhoneNumber,
                        model.PhoneNumber.Length > 10 ? string.Empty : model.CountryCode, out var correctMobileNumber))
                {
                    _logger.LogWarning("invalid phone number {ModelPhoneNumber} req: {@RequestModel}",
                        model.PhoneNumber,
                        JsonConvert.SerializeObject(model));
                    return StatusCode(StatusCodes.Status400BadRequest,
                        new ApiResponse<EmptyDto>(AppResponses.InvalidMobileNumber, null)
                        {
                            Code = $"{StatusCodes.Status400BadRequest}"
                        });
                }


                var accountDetails = await _accountLookupService.ClientAppDetails(model.AppId);
                if (accountDetails == null)
                {
                    _logger.LogError("ClientAppDetails for AppId: {AppId} returned null", model.AppId);
                    return StatusCode(StatusCodes.Status400BadRequest,
                        new ApiResponse<EmptyDto>(AppResponses.AppMayNotBeConfigured, null)
                        {
                            Code = $"{StatusCodes.Status400BadRequest}"
                        });
                } 
                if (string.IsNullOrWhiteSpace(accountDetails.AccountLookUpUrl))
                {
                    _logger.LogError("ClientAppDetails for AppId: {AppId} returned null", model.AppId);
                    return StatusCode(StatusCodes.Status400BadRequest,
                        new ApiResponse<EmptyDto>(AppResponses.AppMayNotBeConfigured, null)
                        {
                            Code = $"{StatusCodes.Status400BadRequest}"
                        });
                }

                var accountLookupResponse =
                    await _accountLookupService.LookUpCustomerInfoByPhoneNumber(accountDetails.AccountLookUpUrl,
                        model.PhoneNumber);
                if (accountLookupResponse == null)
                {
                    _logger.LogError(
                        "Error getting AccountLookup information from: {AccountLookupUrl} and PhoneNumber: {PhoneNumber}",
                        accountDetails.AccountLookUpUrl, model.PhoneNumber);
                    return StatusCode(StatusCodes.Status500InternalServerError,
                        new ApiResponse<EmptyDto>(AppResponses.HttpRequestFailed, null)
                        {
                            Code = $"{StatusCodes.Status500InternalServerError}"
                        });
                }

                if (int.Parse(accountLookupResponse.Code)==StatusCodes.Status403Forbidden)
                {
                    return StatusCode(Convert.ToInt32(accountLookupResponse.Code),
                        new ApiResponse<PhoneNumberLookupData>(accountLookupResponse.Message, accountLookupResponse.Data)
                        {
                            Code = accountLookupResponse.Code
                        });
                }
                if (int.Parse(accountLookupResponse.Code) == StatusCodes.Status404NotFound)
                {
                    return NotFound(
                        new ApiResponse<PhoneNumberLookupData>(accountLookupResponse.Message, accountLookupResponse.Data)
                        {
                            Code = accountLookupResponse.Code
                        });
                }
                if (int.Parse(accountLookupResponse.Code) == StatusCodes.Status500InternalServerError)
                {
                    return StatusCode(StatusCodes.Status500InternalServerError,
                        new ApiResponse<PhoneNumberLookupData>(accountLookupResponse.Message, accountLookupResponse.Data));
                }
                await _accountLookupService.SaveAccountInfoToCache(model.AppId, accountLookupResponse.Data);
                model.PhoneNumber = correctMobileNumber;
                var response = await _otpService.GenerateOtpAsync(model, accountLookupResponse.Data, accountDetails);
                return StatusCode(int.Parse(response.Code), response);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "An error occurred while running method Generate");

                return StatusCode((int)HttpStatusCode.InternalServerError,
                    new ApiResponse<EmptyDto>(AppResponses.UnknownError, null)
                    {
                        Code = $"{(int)HttpStatusCode.InternalServerError}"
                    });
            }
        }

        /// <summary>
        /// Sends Consumer Otp
        /// </summary>
        /// <param name="model">The UssdNotifyClientStatusDto object containing the necessary information to send the OTP.</param>
        /// <returns>An IActionResult representing the result of the operation.</returns>
        [HttpPost]
        [Route("OnSuccess")]
        [Consumes(MediaTypeNames.Application.Json)]
        [Produces(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<EmptyDto>))]
        [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<EmptyDto>))]
        [SwaggerOperation(nameof(OnSuccess), OperationId = nameof(OnSuccess))]
        public async Task<IActionResult> OnSuccess([FromBody] UssdNotifyClientStatusDto model)
        {
            var result = await _otpCacheRepository.StringGetAsync($"{CommonConstants.UssdVerified}:{model.PhoneNumber}:{model.RequestId}");

            var accountDetails =
                await _accountLookupService.RetrieveCachedAccountInfo(model.AppId, model.PhoneNumber);
            if (accountDetails == null || !IsValidUssdRequest(result, model.PhoneNumber))
            {
                var errorResponse = new ApiResponse<dynamic>(AppResponses.LoginFailed, null)
                {
                    Code = $"{StatusCodes.Status400BadRequest}",
                    Data = new
                    {
                        MobileNumber = model.PhoneNumber,
                        model.RequestId
                    },
                    Message = "Login failed"
                };

                _ = await _socketOptions.Failure.AppendPathSegment(model.AppId).PostJsonAsync(errorResponse);

                _logger.LogError(
                        "sending login failure notification for phone number:{PhoneNumber} from request {@Request}",
                        model.PhoneNumber, model);

                return StatusCode(StatusCodes.Status400BadRequest, errorResponse);
            }

            var clientApp = await _accountLookupService.ClientAppDetails(model.AppId);
            if (clientApp == null || string.IsNullOrWhiteSpace(clientApp.AccountLookUpUrl))
            {
                return StatusCode(StatusCodes.Status400BadRequest, AppResponses.AppMayNotBeConfigured);
            }

            var payload = _jwtService.GenerateAppToken(new TokenGenerationModel { AccountLookupData = accountDetails,
                AuthenticationConfiguration = clientApp, RequestId= model.RequestId });
            if (!payload.IsSuccessful)
            {
                _logger.LogError("Customer {MobileNumber}, An error occured verifying otp. Error {ErrorMessage}",
                model.PhoneNumber, payload.Message);
                return StatusCode(StatusCodes.Status424FailedDependency, AppResponses.LoginFailed);
            }
            _logger.LogInformation("generated token for phone number:{PhoneNumber} from request {@Request}",
                    model.PhoneNumber, model.ToFormattedJsonString());
                var response =
                    new ApiResponse<dynamic>("Login successful", StatusCodes.Status200OK.ToString(), payload.Data);
            var socketResponse = await _socketOptions.Success.AppendPathSegment(model.AppId).AllowAnyHttpStatus().PostJsonAsync(response);
            if (socketResponse.StatusCode == StatusCodes.Status200OK)
            {
                _logger.LogInformation(
                   "sending login success notification for phone number:{PhoneNumber} from request {@Request}",
                   model.PhoneNumber, model.ToFormattedJsonString());
                await _authConfigRedisStore.StringSetAsync(
                  $"{CommonConstants.RedisUssdSuccessKey}:{model.AppId}:{model.PhoneNumber}",
                  $"{model.AppId}:{model.PhoneNumber}", TimeSpan.FromDays(7));
            }
            else
            {
                _logger.LogError(
                   "Failure in sending login success notification for phone number:{PhoneNumber} from request {@Request} and {SocketResponseCode}",
                   model.PhoneNumber, model.ToFormattedJsonString(),socketResponse.StatusCode);
            }
            return Ok();
        }

        /// <summary>
        /// Sends Consumer Otp
        /// </summary>
        /// <param name="model">The UssdNotifyClientStatusDto object containing the necessary information to send the OTP.</param>
        /// <returns>An IActionResult representing the result of the operation.</returns>
        [HttpPost]
        [Route("Validate")]
        [Consumes(MediaTypeNames.Application.Json)]
        [Produces(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<EmptyDto>))]
        [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<EmptyDto>))]
        [SwaggerOperation(nameof(ValidateUssdCompletion), OperationId = nameof(ValidateUssdCompletion))]
        public async Task<IActionResult> ValidateUssdCompletion([FromBody] UssdVerifyDto model)
        {
            if (!_phoneNumberParser.IsPhoneNumberValid(model.PhoneNumber,
                    model.PhoneNumber.Length > 10 ? string.Empty : model.CountryCode, out var correctMobileNumber))
            {
                _logger.LogWarning("invalid phone number {ModelPhoneNumber} req: {@RequestModel}",
                    model.PhoneNumber,
                    model);
                return StatusCode(StatusCodes.Status400BadRequest,
                    new ApiResponse<EmptyDto>(AppResponses.InvalidMobileNumber, null)
                    {
                        Code = $"{StatusCodes.Status400BadRequest}"
                    });
            }
            
            var result = await _otpCacheRepository.StringGetAsync($"{CommonConstants.UssdVerified}:{correctMobileNumber}:{model.RequestId}");
            
            var accountDetails = await _accountLookupService.RetrieveCachedAccountInfo(model.AppId, correctMobileNumber);
            if (accountDetails == null || !IsValidUssdRequest(result, correctMobileNumber))
            {
                var errorResponse = new ApiResponse<dynamic>(AppResponses.LoginFailed, null)
                {
                    Code = $"{StatusCodes.Status400BadRequest}",
                    Data =new
                    {
                        MobileNumber=model.PhoneNumber
                    },
                    Message = "Login failed"
                };
                
                _logger.LogError(
                    "sending login failure notification for phone number:{PhoneNumber} from request {@Request}",
                    model.PhoneNumber, model);

                return StatusCode(StatusCodes.Status400BadRequest, errorResponse);
            }

            _ = await _otpCacheRepository.KeyDeleteAsync($"{CommonConstants.UssdVerified}:{correctMobileNumber}");
            
            var clientApp = await _accountLookupService.ClientAppDetails(model.AppId);
            if (clientApp == null || string.IsNullOrWhiteSpace(clientApp.AccountLookUpUrl))
            {
                return StatusCode(StatusCodes.Status400BadRequest, AppResponses.AppMayNotBeConfigured);
            }
            
            var payload = _jwtService.GenerateAppToken(new TokenGenerationModel
            {
                AccountLookupData = accountDetails,
                AuthenticationConfiguration = clientApp
            });
            if (!payload.IsSuccessful)
            {
                _logger.LogError("Customer {MobileNumber}, An error occured verifying otp. Error {ErrorMessage}",
                model.PhoneNumber, payload.Message);
                return StatusCode(StatusCodes.Status424FailedDependency, AppResponses.LoginFailed);
            }
            _logger.LogInformation("generated token for phone number:{PhoneNumber} from request {@Request}",
                model.PhoneNumber, model);
            var response =
                new ApiResponse<dynamic>("Login successful", StatusCodes.Status200OK.ToString(), payload.Data);
          
            _logger.LogInformation(
                "sending login success notification for phone number:{PhoneNumber} from request {@Request}",
                model.PhoneNumber, model);
            if (response.Data != null)
            {
                var cookieOptions = new CookieOptions
                {
                    HttpOnly = true,
                    Secure = true,
                    SameSite = SameSiteMode.Unspecified,
                    Expires = DateTime.Parse(response.Data.Expiry, CultureInfo.CurrentCulture)
                };
                Response.Cookies.Append(AppResponses.AuthCookieKey, response.Data.Token, cookieOptions);
            }
            return StatusCode(StatusCodes.Status200OK, response);
        }
        
        private static bool IsValidUssdRequest(RedisValue result, string phoneNumber) => result.HasValue &&
            !string.IsNullOrWhiteSpace(result.ToString()) && string.Equals(result.ToString(), phoneNumber);

        /// <summary>
        /// Recieves OnFailure Consumer Otp callbacks
        /// </summary>
        /// <param name="model">The UssdNotifyClientStatusDto object containing the necessary information to send the OTP.</param>
        /// <returns>An IActionResult representing the result of the operation.</returns>
        [HttpPost]
        [Route("OnFailure")]
        [AllowAnonymous]
        [Consumes(MediaTypeNames.Application.Json)]
        [Produces(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<EmptyDto>))]
        [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<EmptyDto>))]
        [SwaggerOperation(nameof(OnFailure), OperationId = nameof(OnFailure))]
        public async Task<IActionResult> OnFailure([FromBody] UssdNotifyClientStatusDto model)
        {
            var response = new ApiResponse<dynamic>(AppResponses.LoginFailed, null)
            {
                Code = $"{StatusCodes.Status400BadRequest}",
                Data = new
                {
                    MobileNumber = model.PhoneNumber,
                    model.RequestId
                },
                Message = "Login failed"
            };
            _ = await _socketOptions.Success.AppendPathSegment(model.AppId).PostJsonAsync(response);

            _logger.LogError(
                "sending login failure notification for phone number:{PhoneNumber} from request {@Request}",
                model.PhoneNumber, model);
            return StatusCode(StatusCodes.Status400BadRequest, response);
        }
    }
}