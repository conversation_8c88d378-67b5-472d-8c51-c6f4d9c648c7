using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using Microsoft.AspNetCore.Mvc;

namespace Hubtel.Authentication.Core.Api.Controllers
{
    public class HubtelApiControllerBase : ControllerBase
    {
        protected Expression<Func<T, bool>> FilterData<T>(object filterObject) where T : class
        {
            ParameterExpression item = Expression.Parameter(typeof(T), "item");
            Expression previousExpression = null;

            Expression finalExpression = null;


            var searchFields = GetProperties(filterObject);

            var dateExpressions = new List<Tuple<string, Expression>>();

            foreach (var column in searchFields)
            {
                if (column.Value == null)
                {
                    continue;
                }

                if (column.Value.Item1 == null)
                {
                    continue;
                }


                if (column.Value.Item2 == typeof(DateTime) || column.Value.Item2 == typeof(DateTime?))
                {
                    Expression dateExpression = null;
                    if (column.Key.StartsWith("Start"))
                    {
                        var field = column.Key.Replace("Start", string.Empty, StringComparison.OrdinalIgnoreCase);
                        var startDate = Expression.Property(item, field);
                        dateExpression =
                            Expression.GreaterThanOrEqual(startDate, Expression.Constant(column.Value.Item1));
                        dateExpressions.Add(Tuple.Create(field, dateExpression));
                    }

                    if (column.Key.StartsWith("End"))
                    {
                        var field = column.Key.Replace("End", string.Empty, StringComparison.OrdinalIgnoreCase);
                        var endDate = Expression.Property(item, field);
                        dateExpression =
                            Expression.LessThanOrEqual(endDate, Expression.Constant(column.Value.Item1));

                        dateExpressions.Add(Tuple.Create(field, dateExpression));
                    }

                    continue;
                }

                if (string.IsNullOrEmpty(column.Value.Item1.ToString()))
                {
                    continue;
                }

                var property = Expression.Property(item,
                    column.Key);


                Expression leftExpression = Expression.Property(item,
                    column.Key);


                //should not be null because we're going to do a contains..
                bool typeIsString = false;
                Expression likeneqResult = leftExpression;
                if (property.Type == typeof(string))
                {
                    Expression likeneqleft = leftExpression;
                    Expression likeneqright = Expression.Constant(null);
                    likeneqResult = Expression.NotEqual(likeneqleft,
                        likeneqright);

                    typeIsString = true;
                }


                //convert to string
                var toString = typeof(object).GetMethod("ToString");
                var toStringValue = Expression.Call(leftExpression, toString);

                //then we do a contains with IndexOf

                var toLower = Expression.Call(typeIsString ? leftExpression : toStringValue, "ToLower", null
                );

                var contains = Expression.Call(typeIsString ? toLower : toStringValue, "Contains", null,
                    Expression.Call(Expression.Constant(column.Value.Item1, typeof(string)), "ToLower", null)
                );

                var like = contains;

                //we check that the field is not null AND it contains
                if (!typeIsString)
                {
                    Expression likeneqright = Expression.Constant(null);
                    likeneqResult = Expression.NotEqual(toStringValue,
                        likeneqright);
                }


                Expression likeFinalExpress = Expression.AndAlso(likeneqResult,
                    like);
                if (previousExpression == null)
                {
                    finalExpression = likeFinalExpress;
                }
                else
                {
                    finalExpression = Expression.And(finalExpression,
                        likeFinalExpress);
                }

                previousExpression = likeFinalExpress;
            }

            if (dateExpressions.Any())
            {
                var dateParts = dateExpressions.GroupBy(d => d.Item1);

                foreach (var expressions in dateParts)
                {
                    Expression de = null;
                    foreach (var expression in expressions)
                    {
                        if (de == null)
                        {
                            de = expression.Item2;
                        }
                        else
                        {
                            de = Expression.AndAlso(de,
                                expression.Item2);
                        }
                    }

                    if (de != null)
                    {
                        if (finalExpression != null)
                        {
                            finalExpression = Expression.And(finalExpression, de);
                        }
                        else
                        {
                            finalExpression = de;
                        }
                    }
                }
            }

            if (finalExpression != null)
            {
                return Expression.Lambda<Func<T, bool>>(finalExpression,
                    new ParameterExpression[]
                    {
                        item
                    });
            }

            return arg => true;
        }


        private Dictionary<string, Tuple<object, Type>> GetProperties(object item)
        {
            var result = new Dictionary<string, Tuple<object, Type>>();
            if (item != null)
            {
                var type = item.GetType();
                var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);
                foreach (var pi in properties)
                {
                    var selfValue = type.GetProperty(pi.Name).GetValue(item, null);
                    result[pi.Name] = new Tuple<object, Type>(selfValue, pi.PropertyType);
                }
            }

            return result;
        }


        protected IOrderedQueryable<T> SortData<T>(IQueryable<T> query, string sortColumn, string sortDir = "asc")
            where T : class
        {
            try
            {
                if (!string.IsNullOrEmpty(sortColumn))
                {
                    var type = typeof(T);
                    var property = type.GetProperty(sortColumn,
                        BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);

                    if (property == null)
                    {
                        return query.OrderBy(a => a);
                    }

                    var parameter = Expression.Parameter(type, "p");
                    var propertyAccess = Expression.MakeMemberAccess(parameter, property);
                    var orderByExp = Expression.Lambda(propertyAccess, parameter);
                    var typeArguments = new Type[] { type, property.PropertyType };
                    var methodName = ("asc".Equals(sortDir, StringComparison.OrdinalIgnoreCase))
                        ? "OrderBy"
                        : "OrderByDescending";
                    var resultExp = Expression.Call(typeof(Queryable), methodName, typeArguments, query.Expression,
                        Expression.Quote(orderByExp));
                    query = query.Provider.CreateQuery<T>(resultExp);
                    return (IOrderedQueryable<T>)query;
                }

                return query.OrderBy(a => a);
            }
            catch (Exception e)
            {
                return query.OrderBy(a => a);
            }
        }
    }
}