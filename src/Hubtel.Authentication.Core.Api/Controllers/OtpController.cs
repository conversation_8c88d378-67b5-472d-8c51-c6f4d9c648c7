using Hubtel.PhoneNumbers.Extensions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Swashbuckle.AspNetCore.Annotations;
using System.Net.Mime;
using System.Threading.Tasks;
using Hubtel.Authentication.Core.Api.Models;
using Hubtel.Authentication.Core.Api.Models.AppModels;
using Hubtel.Authentication.Core.Api.Models.Requests;
using Hubtel.Authentication.Core.Api.Models.Responses;
using Hubtel.Authentication.Core.Api.Services.Interface;
using System;
using Hubtel.Authentication.Core.Api.Extensions;
using Hubtel.Authentication.Commons.Models;
using System.Globalization;
using Microsoft.AspNetCore.RateLimiting;
using Hubtel.Authentication.Core.Api.Constants;

namespace Hubtel.Authentication.Core.Api.Controllers
{
    [Route("[controller]")]
    [ApiController]
    [Authorize(AuthenticationSchemes = "Basic")]
    public class OtpController : HubtelApiControllerBase
    {
        private readonly IOtpService _otpService;
        private readonly IPhoneNumberParser _phoneNumberParser;
        private readonly ILogger<OtpController> _logger;
        private readonly IAccountLookupService _accountLookupService;
        public OtpController(IOtpService otpService,
            IPhoneNumberParser phoneNumberParser,
            ILogger<OtpController> logger,
            IAccountLookupService accountLookupService)
        {
            _otpService = otpService;
            _phoneNumberParser = phoneNumberParser;
            _logger = logger;
            _accountLookupService = accountLookupService;
        }

        /// <summary>
        /// Sends Consumer Otp
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("send")]
        [Consumes(MediaTypeNames.Application.Json)]
        [Produces(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GenerateOtpResponse>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<EmptyDto>))]
        [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<EmptyDto>))]
        [SwaggerOperation(nameof(SendOtp), OperationId = nameof(SendOtp))]
        [EnableRateLimiting(CommonStringConstants.OtpRateLimitPolicy)]
        public async Task<IActionResult> SendOtp([FromBody] GenerateOtpDto model)
        {
            if (!_phoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, model.PhoneNumber.Length > 10 ? string.Empty : model.CountryCode, out var correctMobileNumber))
            {
                _logger.LogError("invalid phone number {ModelPhoneNumber} req: {SerializeObject}", model.PhoneNumber, JsonConvert.SerializeObject(model,Formatting.Indented));
                return StatusCode(StatusCodes.Status400BadRequest, new ApiResponse<EmptyDto>(AppResponses.InvalidMobileNumber, null)
                {
                    Code = $"{StatusCodes.Status400BadRequest}"
                });
            }
            var accountDetails = await _accountLookupService.ClientAppDetails(model.AppId);
            if (accountDetails == null)
            {
                _logger.LogError("ClientAppDetails for AppId: {AppId} returned null", model.AppId);
                return BadRequest(
                    new ApiResponse<EmptyDto>(AppResponses.HttpRequestFailed, null)
                    {
                        Code = $"{StatusCodes.Status400BadRequest}"
                    });
            }
            if (string.IsNullOrWhiteSpace(accountDetails.AccountLookUpUrl))
            {
                _logger.LogError("ClientAppDetails for AppId: {AppId} returned null", model.AppId);
                return StatusCode(StatusCodes.Status400BadRequest,
                    new ApiResponse<EmptyDto>(AppResponses.AppMayNotBeConfigured, null)
                    {
                        Code = $"{StatusCodes.Status400BadRequest}"
                    });
            }
            var accountLookupResponse =
                    await _accountLookupService.LookUpCustomerInfoByPhoneNumber(accountDetails.AccountLookUpUrl,
                        model.PhoneNumber,model.LinkedEmail);
            if (accountLookupResponse == null)
            {
                _logger.LogError(
                    "Error getting AccountLookup information from: {AccountLookupUrl} and PhoneNumber: {PhoneNumber}",
                    accountDetails.AccountLookUpUrl, model.PhoneNumber);
                return BadRequest(
                    new ApiResponse<EmptyDto>(AppResponses.HttpRequestFailed, null)
                    {
                        Code = $"{StatusCodes.Status400BadRequest}"
                    });
            }
            if (int.Parse(accountLookupResponse.Code) == StatusCodes.Status403Forbidden)
            {
                return StatusCode(Convert.ToInt32(accountLookupResponse.Code),
                    new ApiResponse<PhoneNumberLookupData>(accountLookupResponse.Message, accountLookupResponse.Data)
                    {
                        Code = accountLookupResponse.Code
                    });
            }
            if (int.Parse(accountLookupResponse.Code) == StatusCodes.Status404NotFound)
            {
                return NotFound(
                    new ApiResponse<PhoneNumberLookupData>(accountLookupResponse.Message, accountLookupResponse.Data)
                    {
                        Code = accountLookupResponse.Code
                    });
            }
            if(int.Parse(accountLookupResponse.Code) == StatusCodes.Status500InternalServerError)
            {
                return StatusCode(StatusCodes.Status500InternalServerError,
                    new ApiResponse<PhoneNumberLookupData>(accountLookupResponse.Message, accountLookupResponse.Data)
                    {
                        Code= accountLookupResponse.Code
                    });
            }
            await _accountLookupService.SaveAccountInfoToCache(model.AppId, accountLookupResponse.Data);
            model.PhoneNumber = correctMobileNumber;
            var otpRequest = new CreateOtpRequest
            {
                AuthenticationChannel = AuthenticationChannel.Sms,
                AppId = model.AppId,
                CountryCode = model.CountryCode,
                PhoneNumber = model.PhoneNumber
            };
            var response = await _otpService.SendOtpAsync(otpRequest);
            if (int.Parse(response.Code) == StatusCodes.Status200OK)
            {
                response.Data.Account = new
                {
                    MobileNumber = accountLookupResponse.Data.MobileNumber.ToRedactedNumber(),
                    Email = string.IsNullOrWhiteSpace(accountLookupResponse.Data.Email)
                    ? null
                    : accountLookupResponse.Data.Email.ToRedactedEmail()
                };
                return Ok(response);
            }
            return StatusCode(int.Parse(response.Code), response);
        }

        /// <summary>
        /// Verify Consumer Otp
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("verify")]
        [Consumes(MediaTypeNames.Application.Json)]
        [Produces(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<VerifyOtpResponse>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<EmptyDto>))]
        [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<EmptyDto>))]
        [SwaggerOperation(nameof(VerifyOtp), OperationId = nameof(VerifyOtp))]
        public async Task<IActionResult> VerifyOtp([FromBody] VerifyOtpDto model)
        {
            
            if (!_phoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, model.PhoneNumber.Length > 10 ? string.Empty : "GH", out var correctMobileNumber))
            {
                _logger.LogWarning("invalid phone number {ModelPhoneNumber} req: {SerializeObject}", model.PhoneNumber, JsonConvert.SerializeObject(model));
                return StatusCode(StatusCodes.Status400BadRequest, new ApiResponse<EmptyDto>(AppResponses.InvalidMobileNumber, null)
                {
                    Code = $"{StatusCodes.Status400BadRequest}"
                });
            }

            model.PhoneNumber = correctMobileNumber;
            var response = await _otpService.VerifyOtpAsync(model);
            if (response.Data != null)
            {
                var cookieOptions = new CookieOptions
                {
                    HttpOnly = true,
                    Secure = true,
                    SameSite = SameSiteMode.Unspecified,
                    Expires = DateTime.Parse(response.Data.Expiry, CultureInfo.CurrentCulture)
                };
                Response.Cookies.Append(AppResponses.AuthCookieKey, response.Data.Token, cookieOptions);
                
            }
            return StatusCode(int.Parse(response.Code), response);
        }

      

        /// <summary>
        /// Verify Consumer Otp
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("verify/checkout")]
        [Consumes(MediaTypeNames.Application.Json)]
        [Produces(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<VerifyOtpResponse>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<EmptyDto>))]
        [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<EmptyDto>))]
        [SwaggerOperation(nameof(VerifyOtp), OperationId = nameof(VerifyOtp))]
        public async Task<IActionResult> VerifyOtpCheckout([FromBody] VerifyOtpDtoWebCheckout model)
        {
            if (!_phoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, model.PhoneNumber.Length > 10 ? string.Empty : "GH", out var correctMobileNumber))
            {
                _logger.LogWarning("invalid phone number {ModelPhoneNumber} req: {VerifyOtpDtoWebCheckout}", model.PhoneNumber, JsonConvert.SerializeObject(model));
                return StatusCode(StatusCodes.Status400BadRequest, new ApiResponse<EmptyDto>(AppResponses.InvalidMobileNumber, null)
                {
                    Code = $"{StatusCodes.Status400BadRequest}"
                });
            }

            model.PhoneNumber = correctMobileNumber;
            var response = await _otpService.VerifyOtp2Async(model);
            return StatusCode(int.Parse(response.Code), response);
        }

        /// <summary>
        /// Resends Consumer Otp
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("resend")]
        [Consumes(MediaTypeNames.Application.Json)]
        [Produces(MediaTypeNames.Application.Json)]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GenerateOtpResponse>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<EmptyDto>))]
        [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<EmptyDto>))]
        [SwaggerOperation(nameof(ResendOtp), OperationId = nameof(ResendOtp))]
        public async Task<IActionResult> ResendOtp([FromBody] ResendOtpDto model)
        {
            if (!_phoneNumberParser.IsPhoneNumberValid(model.PhoneNumber, model.PhoneNumber.Length > 10 ? string.Empty : "GH", out var correctMobileNumber))
            {
                _logger.LogWarning("invalid phone number {ModelPhoneNumber} req: {SerializeObject}", model.PhoneNumber, JsonConvert.SerializeObject(model));
                return StatusCode(StatusCodes.Status400BadRequest, new ApiResponse<EmptyDto>(AppResponses.InvalidMobileNumber, null)
                {
                    Code = $"{StatusCodes.Status400BadRequest}"
                });
            }

            model.PhoneNumber = correctMobileNumber;
            var response = await _otpService.ResendOtpAsync(model);
            return StatusCode(int.Parse(response.Code), response);
        }
    }
}
