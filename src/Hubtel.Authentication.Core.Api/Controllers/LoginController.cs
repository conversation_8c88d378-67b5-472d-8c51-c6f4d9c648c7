using System;
using System.Net.Mime;
using System.Threading.Tasks;
using Hubtel.Authentication.Core.Api.Dtos;
using Hubtel.Authentication.Core.Api.Models;
using Hubtel.Authentication.Core.Api.Models.AppModels;
using Hubtel.Authentication.Core.Api.Models.Responses;
using Hubtel.Authentication.Core.Api.Services;
using Hubtel.Authentication.Core.Api.Services.Interface;
using Mapster;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace Hubtel.Authentication.Core.Api.Controllers;

/// <summary>
/// Login Controller for handling login functionality.
/// </summary>
[Route("[controller]")]
[ApiController]
[Authorize(AuthenticationSchemes = "Basic")]
public class LoginController : HubtelApiControllerBase
{
    
    private readonly IAccountLookupService _accountLookupService;
    private readonly IAuthenticationConfigStoreService _configStoreService;

    /// <summary>
    /// Represents a controller for handling login functionality.
    /// </summary>
    public LoginController(IAccountLookupService accountLookupService, IAuthenticationConfigStoreService configStoreService)
    {
        _accountLookupService = accountLookupService;
        _configStoreService = configStoreService;
    }


    /// <summary>
    /// Initiates the account store configuration retrieval process.
    /// </summary>
    /// <param name="appId">The ID of the application to retrieve the configuration for.</param>
    /// <returns>An IActionResult representing the result of the operation.</returns>
    [HttpGet]
    [Route("Initiate")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GetAuthConfigStoreResponse>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<EmptyDto>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<EmptyDto>))]
    [SwaggerOperation(nameof(Initiate), OperationId = nameof(Initiate))]
    public async Task<IActionResult> Initiate(Guid appId)
    {
       
            var appConfig  = await _configStoreService.GetAsync<GetAuthConfigStoreResponse>(appId);
        if (appConfig == null)
        {
            return StatusCode(StatusCodes.Status400BadRequest,
                new ApiResponse<EmptyDto>(AppResponses.AppMayNotBeConfigured, null)
                {
                    Code = $"{StatusCodes.Status400BadRequest}"
                });
        }
        if (string.IsNullOrWhiteSpace(appConfig.AccountLookUpUrl) && string.IsNullOrWhiteSpace(appConfig.EmailLookUpUrl))
        {
            return StatusCode(StatusCodes.Status400BadRequest,
                new ApiResponse<EmptyDto>(AppResponses.AppMayNotBeConfigured, null)
                {
                    Code = $"{StatusCodes.Status400BadRequest}"
                });
        }

        var response =  appConfig.Adapt<GetAuthConfigStoreResponse>();
        return StatusCode(StatusCodes.Status200OK,
            new ApiResponse<GetAuthConfigStoreResponse>("details retrieved", response)
            {
                Code = $"{StatusCodes.Status200OK}"
            });


    }

    /// <summary>
    /// Initiates the account store configuration retrieval process.
    /// </summary>
    /// <param name="appId">The ID of the application to retrieve the configuration for.</param>
    /// <param name="phoneNumber">The phone number.</param>
    /// <returns>An IActionResult representing the result of the operation.</returns>
    [HttpGet]
    [Route("account")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<PhoneNumberLookupResponse>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<EmptyDto>))]
    [ProducesResponseType(StatusCodes.Status424FailedDependency, Type = typeof(ApiResponse<EmptyDto>))]
    [SwaggerOperation(nameof(AccountDetails), OperationId = nameof(AccountDetails))]
    public async Task<IActionResult> AccountDetails(Guid appId,string phoneNumber)
    {
       
        var appConfig =await _configStoreService.GetAsync<GetAuthConfigStoreResponse>(appId);
        if (appConfig == null||string.IsNullOrWhiteSpace(appConfig.AccountLookUpUrl))
        {
            return BadRequest(new ApiResponse<EmptyDto>(AppResponses.AppMayNotBeConfigured, null)
                {
                    Code = $"{StatusCodes.Status400BadRequest}"
                });
        }
        var lookupResponse =
            await _accountLookupService.LookUpCustomerInfoByPhoneNumber(appConfig.AccountLookUpUrl, phoneNumber);
        if (lookupResponse == null)
        {
            return BadRequest(new ApiResponse<EmptyDto>(AppResponses.HttpRequestFailed, null)
                {
                    Code = $"{StatusCodes.Status500InternalServerError}"
                });
        }
        if (lookupResponse.Code == $"{StatusCodes.Status404NotFound}")
        {
            return NotFound(new ApiResponse<PhoneNumberLookupData>(AppResponses.PhoneNumberNotAssociatedWithAccount, lookupResponse.Data)
                {
                    Code = $"{StatusCodes.Status404NotFound}"
                });
        }
        if (int.Parse(lookupResponse.Code) == StatusCodes.Status500InternalServerError)
        {
            return StatusCode(StatusCodes.Status500InternalServerError,
                new ApiResponse<PhoneNumberLookupData>(lookupResponse.Message, lookupResponse.Data)
                {
                    Code = $"{StatusCodes.Status500InternalServerError}"
                });
        }
        return Ok(new ApiResponse<PhoneNumberLookupData>(lookupResponse.Message, lookupResponse.Data)
            {
                Code = lookupResponse.Code
            });


    }
    
}