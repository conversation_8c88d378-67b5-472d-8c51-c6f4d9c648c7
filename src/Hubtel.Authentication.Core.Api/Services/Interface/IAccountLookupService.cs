using System;
using System.Threading.Tasks;
using Hubtel.Authentication.Core.Api.Dtos;
using Hubtel.Authentication.Core.Api.Models.Responses;

namespace Hubtel.Authentication.Core.Api.Services.Interface;

public interface IAccountLookupService
{
    /// <summary>
    /// Stores the provided account information in Redis Cache.
    /// </summary>
    /// <param name="applicationId">the id of the client application.</param>
    /// <param name="accountInfo">The account information to store.</param>
    /// <param name="expiry">The amount of time data should be stored in cache</param>
    /// <returns>A <see cref="Task"/> representing the result of the asynchronous operation. True if the operation was successful, false otherwise.</returns>
    Task<bool> SaveAccountInfoToCache(Guid applicationId, PhoneNumberLookupData accountInfo, int expiry = 10);

    /// <summary>
    /// Saves QR information to cache for a specified application.
    /// </summary>
    /// <param name="applicationId">The ID of the application.</param>
    /// <param name="qrData">The QR data to be saved.</param>
    /// <param name="phoneNumber"></param>
    /// <param name="expiry">The expiry duration in minutes (default is 10 minutes).</param>
    /// <returns>A task representing the asynchronous save operation. Returns true if the QR information is successfully saved to cache; otherwise, false.</returns>
    Task<bool> SaveQrInfoToCache(Guid applicationId, string qrData, string phoneNumber, int expiry = 10);

    /// <summary>
    /// Retrieves the account information for the provided mobile number from Redis Cache.
    /// </summary>
    /// <param name="applicationId">the unique Identifier of the client application.</param>
    /// <param name="mobileNumber">The mobile number.</param>
    /// <returns>A <see cref="Task"/> representing the result of the asynchronous operation. The <see cref="PhoneNumberLookupData"/> if found, null otherwise.</returns>
    Task<PhoneNumberLookupData> RetrieveCachedAccountInfo(Guid applicationId, string mobileNumber);

    /// <summary>
    /// Retrieves the cached QR information for the specified application and QR data.
    /// </summary>
    /// <param name="applicationId">The unique identifier of the application.</param>
    /// <param name="qrData">The QR data to retrieve cached information for.</param>
    /// <param name="phoneNumber"></param>
    /// <returns>
    /// A task that represents the asynchronous operation and contains the cached QR information.
    /// The task result will be an instance of the AccountLookupData class.
    /// </returns>
    Task<string> RetrieveCachedQrInfo(Guid applicationId, string qrData, string phoneNumber);

    /// <summary>
    /// Retrieves account details with specified App ID.
    /// </summary>
    /// <param name="lookUpUrl">The lookUp Url of the client application.</param>
    /// <param name="phoneNumber">the phone number of the user.</param>
    /// /// <param name="linkedEmail">Optional external email for downstream lookup</param>
    /// <returns>A <see cref="Task"/> representing the result of the asynchronous operation.</returns>
    /// 
    Task<PhoneNumberLookupResponse> LookUpCustomerInfoByPhoneNumber(string lookUpUrl, string phoneNumber, string linkedEmail = "");

    Task<GetBackOfficeAuthConfigStoreResponse> ClientAppDetails(Guid appId);

    /// <summary>
    /// Retrieves account details when given the email.
    /// </summary>
    /// <param name="lookUpUrl">The lookUp Url of the client application.</param>
    /// <param name="email">the email of the user.</param>
    /// <returns>A <see cref="Task"/> representing the result of the asynchronous operation.</returns>
    Task<EmailLookUpResponse> LookUpCustomerInfoByEmail(string lookUpUrl, string email);
}