using System.Threading.Tasks;
using Hubtel.Authentication.Core.Api.Models;
using Hubtel.Authentication.Core.Api.Models.Requests;
using Hubtel.Authentication.Core.Api.Models.Responses;

namespace Hubtel.Authentication.Core.Api.Services.Interface
{
    public interface IOtpService
    {
        Task<ApiResponse<GenerateOtpResponse>> SendOtpAsync(CreateOtpRequest model);
        Task<ApiResponse<GenerateOtpResponse>> SendWhatsAppOtpAsync(GenerateOtpDto model, string senderId);
        Task<ApiResponse<VerifyOtpResponse>> VerifyOtpAsync(VerifyOtpDto model);
        Task<ApiResponse<GenerateOtpResponse>> ResendOtpAsync(ResendOtpDto model);
        Task<ApiResponse<GenerateOtpResponse>> ResendWhatsAppOtpAsync(ResendOtpDto model);
        Task<ApiResponse<VerifyOtpResponse>> VerifyOtp2Async(VerifyOtpDtoWebCheckout model);
        Task<ApiResponse<VerifyOtpResponse>> VerifyWhatsAppOtpAsync(VerifyOtpDto model);
    }
}
