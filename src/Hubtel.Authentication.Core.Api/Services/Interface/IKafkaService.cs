using System.Threading.Tasks;
using Hubtel.Authentication.Core.Api.Models.AppModels;

namespace Hubtel.Authentication.Core.Api.Services.Interface
{
    public interface IKafkaService
    {
        Task ProduceMessageAsync(object model, string topic);
        Task ProduceMessageAsync(NotificationVM model, NotificationTypes type);
        Task NotifyOptionsAsync(NotificationData notification, string[] options);
    }
}
