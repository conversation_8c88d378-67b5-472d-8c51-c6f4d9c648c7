using System.Threading.Tasks;
using Hubtel.Authentication.Core.Api.Models;
using Hubtel.Authentication.Core.Api.Models.Requests;
using Hubtel.Authentication.Core.Api.Models.Responses;

namespace Hubtel.Authentication.Core.Api.Services.Interface
{
    public interface IQrService
    {
        Task<ApiResponse<QrChallengeResponse>> GenerateQrTokenAsync(QrData qrDataRequest);
        Task<ApiResponse<VerifyQrResponse>> VerifyQrTokenAsync(QrDataRequest qrDataRequest);
    }
}
