using System.Threading.Tasks;
using Hubtel.Authentication.Core.Api.Dtos;
using Hubtel.Authentication.Core.Api.Models;
using Hubtel.Authentication.Core.Api.Models.Requests;
using Hubtel.Authentication.Core.Api.Models.Responses;

namespace Hubtel.Authentication.Core.Api.Services.Interface;

public interface IUssdService
{
    Task<ApiResponse<GenerateUssdOtpResponse>> GenerateOtpAsync(GenerateUssdOtpDto model,
        PhoneNumberLookupData accountDetailsAccountLookupData, GetBackOfficeAuthConfigStoreResponse accountDetails);

    Task<bool> ValidateRequestId(string phoneNumber, string requestId);
}