using System.Threading.Tasks;
using Hubtel.Authentication.Core.Api.Dtos;
using Hubtel.Authentication.Core.Api.Models;
using Hubtel.Authentication.Core.Api.Models.Requests;
using Hubtel.Authentication.Core.Api.Models.Responses;

namespace Hubtel.Authentication.Core.Api.Services.Interface;

public interface IEmailService
{
    Task<ApiResponse<GenerateOtpResponse>> SendOtpAsync(GenerateEmailOtpDto model,
        GetBackOfficeAuthConfigStoreResponse appConfig);
    Task<ApiResponse<VerifyOtpResponse>> VerifyOtpAsync(VerifyEmailOtpDto model);
}