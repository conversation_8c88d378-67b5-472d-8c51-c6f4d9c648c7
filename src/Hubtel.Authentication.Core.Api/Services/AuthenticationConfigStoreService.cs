using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Hubtel.Authentication.Commons.Extensions;
using Hubtel.Authentication.Commons.Models;
using Hubtel.Authentication.Core.Api.Data;
using Hubtel.Authentication.Core.Api.Data.Entities;
using Hubtel.Authentication.Core.Api.Dtos;
using Hubtel.Authentication.Core.Api.Extensions;
using Hubtel.Authentication.Core.Api.Models;
using Hubtel.Authentication.Core.Api.Models.AppModels;
using Hubtel.Authentication.Core.Api.Repositories.Interfaces;
using Hubtel.Authentication.Core.Api.Services.Interface;
using Hubtel.Redis.Sdk.Services;
using Mapster;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace Hubtel.Authentication.Core.Api.Services;

/// <summary>
/// Service class for managing authentication configuration data.
/// </summary>
public class AuthenticationConfigStoreService : IAuthenticationConfigStoreService
{
    /// various database operations related to authentication configuration.
    private readonly IAuthConfigRepository<AuthenticationConfiguration> _dbRepo;

    /// <summary>
    /// The private readonly ILogger instance used for logging.
    /// </summary>
    private readonly ILogger<AuthenticationConfigStoreService> _logger;

    /// <summary>
    /// Represents the memory cache service used for storing and retrieving data.
    /// </summary>
    private readonly IDatabase _memoryCacheService;

    /// <summary>
    /// Represents a service for the authentication configuration store.
    /// </summary>
    public AuthenticationConfigStoreService(ILogger<AuthenticationConfigStoreService> logger, IAuthConfigRepository<AuthenticationConfiguration> dbRepo, IMultiRedisHostCacheRepository cacheRepository)
    {
        _logger = logger;
        _dbRepo = dbRepo;
        _memoryCacheService = cacheRepository.GetDb(RedisConstants.MainRedisHostName, RedisConstants.AuthConfigurationDb);
    }

    /// <summary>
    /// Adds a new authentication configuration to the database.
    /// </summary>
    /// <typeparam name="T">The type of the authentication configuration response.</typeparam>
    /// <param name="request">The <see cref="CreateAuthConfigStoreRequest"/> containing the authentication configuration details.</param>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    public async Task<ApiResponse<T>> AddAsync<T>(CreateAuthConfigStoreRequest request)
        where T : AuthConfigStoreResponse
    {
        try
        {
            
            _logger.LogInformation("Attempting to add a new authentication configuration...{@Payload}", request);
            var authenticationConfiguration = request.Adapt<AuthenticationConfiguration>();
            var selectedDefaultChannel = new SelectedAuthChannel(authenticationConfiguration.DefaultChannel);
            authenticationConfiguration.AuthenticationChannels.Add(selectedDefaultChannel);
            

            var savedCount = await _dbRepo.AddConfigAsync(authenticationConfiguration);

            if (savedCount <= 0)
            {
                _logger.LogWarning("Could not create  authentication configuration! Please try again");
                return CommonResponses.ErrorResponse.BadRequestResponse<T>(
                    "Could not create authentication configuration! Please try again");
            }

            var configStoreResponse = authenticationConfiguration.Adapt<T>();
            _logger.LogInformation("Authentication configuration was successfully added");
            return CommonResponses.SuccessResponse.OkResponse(configStoreResponse);
        }
        catch(Exception e)
        {
            _logger.LogError(e, "An error occurred while adding a new authentication configuration");
            return CommonResponses.ErrorResponse.BadRequestResponse<T>("An error occurred while adding a new authentication configuration");
        }
    }

    /// <summary>
    /// Deletes an authentication configuration asynchronously.
    /// </summary>
    /// <param name="id">The ID of the authentication configuration to delete.</param>
    /// <returns>
    /// An ApiResponse containing a boolean value indicating whether the operation was successful or not.
    /// - If the authentication configuration is successfully deleted, the ApiResponse will indicate success.
    /// - If the authentication configuration is not found, the ApiResponse will indicate an error with a "Not Found" message.
    /// </returns>
    public async Task<ApiResponse<bool>> DeleteAsync(Guid id)
    {
        _logger.LogInformation("Attempting to delete authentication configuration with ID: {Id}", id);
        var configToDelete = await _dbRepo.FindFirstOneAsync(x => x.Id == id);
        if (configToDelete == null)
        {
            _logger.LogWarning("Could not find authentication configuration with ID: {Id}", id);
            return CommonResponses.SuccessResponse.NoContentResponse<bool>(true, "Operation Succeeded");
        }
        
        var isDeleted = await _dbRepo.DeleteAsync(configToDelete);
        if (isDeleted > 0)
        {
            _ = _memoryCacheService.KeyDeleteAsync($"{CommonConstants.RedisAuthConfigKey}:{id}");
            _logger.LogInformation("Authentication configuration with ID: {Id} was successfully deleted", id);
            return CommonResponses.SuccessResponse.OkResponse(true);
        }

        _logger.LogWarning("Could not find specified config for delete");
        return CommonResponses.ErrorResponse.NotFoundResponse<bool>("could not find specified config for delete");
    }

    /// <summary>
    /// Retrieves a paged result of authentication configuration store responses based on the given filter.
    /// </summary>
    /// <typeparam name="T">The type of the authentication configuration store response.</typeparam>
    /// <param name="filter">The filter to apply to the results.</param>
    /// <param name="ct">The cancellation token (optional).</param>
    /// <returns>An ApiResponse object containing a paged result of GetAuthConfigStoreResponse objects.</returns>
    public async Task<ApiResponse<PagedResult<T>>> GetAsync<T>(BaseFilter filter,
        CancellationToken ct = default) where T : AuthConfigStoreResponse
    {
        var queryBuilder = _dbRepo.GetQueryableAsNoTracking();

        if (!string.IsNullOrEmpty(filter.SearchTerm))
        {
            queryBuilder = queryBuilder
                .Where(x => EF.Functions.ILike(x.AppName, $"%{filter.SearchTerm}%"));
        }

        var configStoreResponses =await queryBuilder
            .IgnoreActiveFlagForBackOffice(typeof(T))
            .Skip((filter.Page - 1) * filter.PageSize)
            .Take(filter.PageSize)
            .ProjectToType<T>()
            .ToListAsync(ct);


        var totalCount = await _dbRepo.GetQueryableAsNoTracking()
            .IgnoreActiveFlagForBackOffice(typeof(T))
            .LongCountAsync(ct);

        return
            CommonResponses.SuccessResponse.OkResponse(configStoreResponses.ToPagedResult(filter.Page, filter.PageSize, totalCount));
    }


    /// <summary>
    /// Retrieves the authentication configuration store with the specified ID.
    /// </summary>
    /// <param name="id">The ID of the authentication configuration store to retrieve.</param>
    /// <param name="ct">Cancellation token (optional).</param>
    /// <returns>An ApiResponse object containing the result of the operation.</returns>
    public async Task<T> GetAsync<T>(Guid id, CancellationToken ct = default) where T : AuthConfigStoreResponse
    {
        var configStoreResponse = await _dbRepo.GetQueryableAsNoTracking()
            .Include(x => x.AuthenticationChannels)
            .IgnoreActiveFlagForBackOffice(typeof(T))
            .ProjectToType<T>()
            .FirstOrDefaultAsync(c => c.Id == id, cancellationToken: ct);
        return configStoreResponse;
    }

    /// <summary>
    /// Retrieves a list of all pending Authentication Configurations.
    /// </summary>
    /// <param name="filter">The filter for pagination.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>An ApiResponse containing a PagedResult of GetBackOfficeAuthConfigStoreResponse.</returns>
    public async Task<ApiResponse<PagedResult<GetBackOfficeAuthConfigStoreResponse>>> GetAllPendingAsync(
        BaseFilter filter, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting the list of all inactive Authentication Configurations");

            var configStoreResponses = await _dbRepo.GetQueryableAsNoTracking()
                .Include(x => x.AuthenticationChannels)
                .Where(x => !x.IsActive)
                .Skip((filter.Page - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ProjectToType<GetBackOfficeAuthConfigStoreResponse>()
                .ToListAsync(cancellationToken);

            _logger.LogInformation("Getting the total count of the Authentication Configurations");

            var totalCount = await _dbRepo.GetQueryableAsNoTracking()
                .LongCountAsync(cancellationToken);


            return CommonResponses.SuccessResponse.OkResponse(configStoreResponses
                .ToPagedResult(filter.Page, filter.PageSize, totalCount));
        }
        catch (Exception e)
        {
            _logger.LogError(e, "An error occurred while retrieving the records");
            return CommonResponses.ErrorResponse.BadRequestResponse<PagedResult<GetBackOfficeAuthConfigStoreResponse>>(
                "Could not retrieve pending records");
        }
    }

    /// <summary>
    /// Adds white listed numbers to the authentication configuration.
    /// </summary>
    /// <param name="id">The ID of the configuration.</param>
    /// <param name="additions">The list of white listed numbers to be added.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>An ApiResponse object containing the result of the operation.</returns>
    public async Task<ApiResponse<List<WhiteListedNumber>>> AddWhiteListedNumbersToAuthConfigAsync(Guid id,
        List<WhiteListedNumber> additions, CancellationToken cancellationToken)
    {
        if (additions.Count == 0)
        {
            _logger.LogInformation("List did not contain list to be added to white list to app wit config id : {Config}",
                id);
            return CommonResponses.ErrorResponse.BadRequestResponse<List<WhiteListedNumber>>(AppResponses
                .YouNeedToProvideWhiteList);
        }

        _logger.LogInformation(
            "Attempt to add {WhiteListedNumbersCount} new whitelisted numbers for configuration with id {Id}",
            additions.Count, id);
        var whiteListedNumbers = additions.Adapt<List<WhiteList>>();
        var authenticationConfiguration = await _dbRepo.GetQueryable()
            .Include(x => x.WhiteLists)
            .FirstOrDefaultAsync(x => x.Id == id, cancellationToken);
        if (authenticationConfiguration == null)
        {
            _logger.LogWarning("Could not find authentication configuration with id {Id}", id);
            return
                CommonResponses.ErrorResponse.NotFoundResponse<List<WhiteListedNumber>>(AppResponses
                    .CouldNotFindAuthConfig);
        }

        _logger.LogInformation("Found authentication configuration with id {Id}", id);
        authenticationConfiguration.WhiteLists.AddRange(whiteListedNumbers);

        var saved = await _dbRepo.UpdateAsync(authenticationConfiguration,cancellationToken) > 0;
        if (saved)
        {
            _logger.LogInformation("Configuration with id: {Id} successfully updated with {WhiteLists}", id,
                additions.Count);
            return CommonResponses.SuccessResponse.OkResponse(additions);
        }

        _logger.LogWarning("Failed to add new whitelisted numbers to configuration with id {Id}", id);
        return CommonResponses.ErrorResponse.NotFoundResponse<List<WhiteListedNumber>>(AppResponses
            .CouldNotUpdateAuthConfig);
    }

    /// <summary>
    /// Remove white listed numbers from authentication configuration asynchronously.
    /// </summary>
    /// <param name="id">The ID of the authentication configuration.</param>
    /// <param name="removals">The list of white listed numbers to be removed.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>An <see cref="ApiResponse{T}"/> indicating the success of the operation.</returns>
    public async Task<ApiResponse<bool>> RemoveWhiteListedNumbersFromAuthConfigAsync(Guid id,
        List<string> removals,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Initiated removal of white listed numbers from auth config {ConfigId}", id);
            if (removals.Count == 0)
            {
                _logger.LogWarning(
                    "Cannot remove white listed numbers from auth config {ConfigId} because removals list is empty",
                    id);
                return CommonResponses.ErrorResponse.BadRequestResponse<bool>(AppResponses
                    .YouNeedToProvideWhiteListToRemove);
            }

            var configuration =
                await _dbRepo.GetQueryable().Where(x => x.Id == id)
                    .FirstOrDefaultAsync(cancellationToken: cancellationToken);
            if (configuration?.WhiteLists == null || configuration.WhiteLists.Count == 0)

            {
                _logger.LogError("Cannot find auth config with Id: {ConfigId} or  white listed numbers to remove", id);
                return CommonResponses.ErrorResponse.NotFoundResponse<bool>(AppResponses.CouldNotUpdateWhiteList);
            }

            var whiteListsToRemove = configuration.WhiteLists.Where(x => removals.Contains(x.PhoneNumber)).ToList();
            foreach (var whiteList in whiteListsToRemove)
            {
                configuration.WhiteLists.Remove(whiteList);
            }
            var saved = await _dbRepo.UpdateAsync(configuration,cancellationToken) > 0;
            if (saved)
            {
                _ = _memoryCacheService.KeyDeleteAsync($"{CommonConstants.RedisAuthConfigKey}:{id}");
                _logger.LogInformation("white list channels with id: {Id} successfully updated", id);
            }
            else
            {
                _logger.LogInformation("Failed to remove white list from auth channels with id {Id}", id);
            }

            return
                CommonResponses.SuccessResponse.OkResponse(saved);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error Occured.Failed to remove white list from application with id {Id}", id);
            return CommonResponses.ErrorResponse.InternalServerErrorResponse<bool>();
        }
    }

    /// <summary>
    /// Retrieves all the authentication whitelists for a given ID asynchronously
    /// </summary>
    /// <param name="id">The ID used to retrieve the authentication whitelists</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>An ApiResponse containing the list of retrieved whitelisted numbers</returns>
    public async Task<ApiResponse<List<WhiteListedNumber>>> GetAllAuthenticationWhitelistsAsync(Guid id,
        CancellationToken cancellationToken)
    {
        var response =
            await _memoryCacheService.GetKey<AuthenticationConfiguration>(
                $"{CommonConstants.RedisAuthConfigKey}:{id}");

        if (response != null && response.WhiteLists.Count > 0)
        {
            var whiteListedNumbers = response.WhiteLists.Adapt<List<WhiteListedNumber>>().ToList();
            return CommonResponses.SuccessResponse.OkResponse(whiteListedNumbers);
        }

        var whiteLists = await _dbRepo.GetQueryableAsNoTracking()
            .Where(x => x.Id == id)
            .AsNoTracking()
            .SelectMany(x => x.WhiteLists).ToListAsync(cancellationToken);

        var data = whiteLists.Adapt<List<WhiteListedNumber>>().ToList();
        return
            CommonResponses.SuccessResponse.OkResponse(data);
    }

    /// <summary>
    /// Retrieves the cached authentication configuration store with the specified ID.
    /// </summary>
    /// <typeparam name="T">The type of authentication configuration store response.</typeparam>
    /// <param name="id">The ID of the authentication configuration store to retrieve.</param>
    /// <param name="ct">Cancellation token (optional).</param>
    /// <returns>An <see cref="Task{T}"/> representing the asynchronous operation. The task result contains the authentication configuration store response.</returns>
    public async Task<T> GetCachedClientAsync<T>(Guid id, CancellationToken ct = default)
        where T : AuthConfigStoreResponse
    {
        try
        {
            var response =
                await _memoryCacheService.GetKey<T>(
                    $"{CommonConstants.RedisAuthConfigKey}:{id}");
            if (response is { IsActive: true })
            {
                return response;
            }

            var configStoreResponse = await _dbRepo.GetQueryableAsNoTracking()
                .Include(x => x.AuthenticationChannels)
                .AsNoTracking()
                .IgnoreActiveFlagForBackOffice(typeof(T))
                .ProjectToType<T>()
                .FirstOrDefaultAsync(c => c.Id == id, cancellationToken: ct);

            if (configStoreResponse == null) return default!;
            await _memoryCacheService.StringSetAsync($"{CommonConstants.RedisAuthConfigKey}:{id}",
                JsonSerializer.Serialize(configStoreResponse), TimeSpan.FromMinutes(5));

            return configStoreResponse;
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error Occured.Failed to get cache data from application with id {Id}", id);
            return null!;
        }
    }

    /// <summary>
    /// Updates the authentication configuration asynchronously.
    /// </summary>
    /// <typeparam name="T">The type of AuthConfigStoreResponse.</typeparam>
    /// <param name="id">The identifier of the authentication configuration.</param>
    /// <param name="authConfigStore">The updated authentication configuration.</param>
    /// <returns>
    /// An asynchronous operation that represents the update operation.
    /// The task result contains a tuple of bool indicating the success status of the update operation and T representing the updated authentication configuration.
    /// </returns>
    public async Task<(bool IsSaved, T Data)> UpdateAsync<T>(Guid id, UpdateAuthConfigStoreRequest authConfigStore)
        where T : AuthConfigStoreResponse
    {
        try
        {
            _logger.LogInformation("UpdateAsync called with id: {Id}", id);

            var configuration = await _dbRepo.GetByIdAsync(id);

            if (configuration == null)
            {
                _logger.LogWarning("Configuration with id: {Id} not found", id);
                return (false, default);
            }

            _dbRepo.DbSet.Entry(configuration).CurrentValues.SetValues(authConfigStore);

            var saved = await _dbRepo.SaveChangesAsync() > 0;
            switch (saved)
            {
                case true:
                    _ = _memoryCacheService.KeyDeleteAsync($"{CommonConstants.RedisAuthConfigKey}:{id}");

                    _logger.LogInformation("Configuration with id: {Id} successfully updated", id);
                    break;
                default:

                    _logger.LogInformation(AppResponses.FailedToUpdateConfigWithId, id);
                    break;
            }

            var response = configuration.Adapt<T>();


            return (saved, response);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception Occured Configuration with id {Id} not updated", id);
            return (false, null);
        }
    }

    /// <summary>
    /// Gets all authentication channels for a specific ID asynchronously.
    /// </summary>
    /// <param name="id">The ID for which to retrieve authentication channels.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>A task representing the asynchronous operation. The task result contains the list of authentication channels.</returns>
    public async Task<ApiResponse<List<AuthenticationChannel>>> GetAllAuthenticationChannelsAsync(Guid id,
        CancellationToken cancellationToken)
    {
        var response =
            await _memoryCacheService.GetKey<AuthenticationConfiguration>(
                $"{CommonConstants.RedisAuthConfigKey}:{id}");

        if (response != null && response.AuthenticationChannels.Count > 0)
        {
            var channels = response.AuthenticationChannels.Select(x => x.AuthenticationChannel).ToList();
            return CommonResponses.SuccessResponse.OkResponse(channels);
        }

        var authenticationChannels = await _dbRepo.GetQueryableAsNoTracking()
            .Include(x => x.AuthenticationChannels)
            .Where(x => x.Id == id)
            .AsNoTracking()
            .SelectMany(x => x.AuthenticationChannels).ToListAsync(cancellationToken);

        var data = authenticationChannels.Select(x => x.AuthenticationChannel).ToList();
        return
            CommonResponses.SuccessResponse.OkResponse(data);
    }

    /// <summary>
    /// Gets all external logins for a specific ID asynchronously.
    /// </summary>
    /// <param name="id">The ID for which to retrieve external logins.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>A task representing the asynchronous operation. The task result contains the list of external logins.</returns>
    public async Task<ApiResponse<List<ExternalLoginConfiguration>>> GetExternalLoginConfigurationAsync(Guid id,
        CancellationToken cancellationToken)
    {
        var response =
            await _memoryCacheService.GetKey<AuthenticationConfiguration>(
                $"{CommonConstants.RedisAuthConfigKey}:{id}");

        if (response != null && response.AuthenticationChannels.Count > 0)
        {
            var channels = response.ExternalLoginConfigurations.ToList();
            return CommonResponses.SuccessResponse.OkResponse(channels);
        }

        var authenticationChannels = await _dbRepo.GetQueryableAsNoTracking()
            .Include(x => x.AuthenticationChannels)
            .Where(x => x.Id == id)
            .AsNoTracking()
            .SelectMany(x => x.ExternalLoginConfigurations).ToListAsync(cancellationToken);

        
        return
            CommonResponses.SuccessResponse.OkResponse(authenticationChannels);
    }

    /// <summary>
    /// Adds authentication channels to the specified authentication configuration.
    /// </summary>
    /// <param name="id">The ID of the authentication configuration.</param>
    /// <param name="selectedAuthChannels">The list of authentication channels to add.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>An API response containing the updated list of authentication channels.</returns>
    public async Task<ApiResponse<List<AuthenticationChannel>>> AddAuthenticationChannelsAsync(Guid id,
        List<AuthenticationChannel> selectedAuthChannels, CancellationToken cancellationToken)
    {
        var authenticationConfiguration = await _dbRepo.GetQueryable()
            .Include(x => x.AuthenticationChannels)
            .FirstOrDefaultAsync(x => x.Id == id, cancellationToken);

        if (authenticationConfiguration == null)
        {
            return
                CommonResponses.ErrorResponse.NotFoundResponse<List<AuthenticationChannel>>(AppResponses
                    .CouldNotFindAuthConfig);
        }
        
        var addedChannels = selectedAuthChannels.Select(x => new SelectedAuthChannel(x)).ToList();
        authenticationConfiguration.AddAuthenticationChannels(addedChannels);
        var saved = await _dbRepo.UpdateAsync(authenticationConfiguration ,cancellationToken) > 0;
        if (saved)
        {
            _ = _memoryCacheService.KeyDeleteAsync($"{CommonConstants.RedisAuthConfigKey}:{id}");
            _logger.LogInformation("Configuration with id: {Id} successfully updated with {SelectedAuthChannels}", id,
                selectedAuthChannels);
        }
        else
        {
            _logger.LogInformation(AppResponses.FailedToUpdateConfigWithId, id);
            return
                CommonResponses.ErrorResponse.NotFoundResponse<List<AuthenticationChannel>>(AppResponses
                    .CouldNotUpdateAuthConfig);
        }

        return
            CommonResponses.SuccessResponse.OkResponse(selectedAuthChannels);
    }
    /// <summary>
    /// Adds authentication channels to the specified authentication configuration.
    /// </summary>
    /// <param name="id">The ID of the authentication configuration.</param>
    /// <param name="externalLogins">The list of external logins channels to add.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>An API response containing the updated list of external logins.</returns>
    public async Task<ApiResponse<List<ExternalLoginConfiguration>>> AddExternalLoginConfiguration(Guid id, List<ExternalLoginConfiguration> externalLogins,CancellationToken cancellationToken)
    {
        try
        {
            var authenticationConfiguration = await _dbRepo.GetQueryable()
                .Include(x => x.AuthenticationChannels)
                .FirstOrDefaultAsync(x => x.Id == id, cancellationToken);

            if (authenticationConfiguration == null)
            {
                return
                    CommonResponses.ErrorResponse.NotFoundResponse<List<ExternalLoginConfiguration>>(AppResponses
                        .CouldNotFindAuthConfig);
            }
            authenticationConfiguration.AddExternalLoginConfigurations(externalLogins);
            var saved = await _dbRepo.UpdateAsync(authenticationConfiguration,cancellationToken) > 0;
            if (saved)
            {
                _logger.LogInformation("Configuration with id: {Id} successfully updated with {ExternalLoginConfiguration}", id,
                    externalLogins);
            }
            else
            {
                _logger.LogInformation(AppResponses.FailedToUpdateConfigWithId, id);
                return
                    CommonResponses.ErrorResponse.NotFoundResponse<List<ExternalLoginConfiguration>>(AppResponses
                        .CouldNotUpdateAuthConfig);
            }

            return
                CommonResponses.SuccessResponse.OkResponse(externalLogins);
        }
        catch(Exception e)
        {
            _logger.LogError(e, "An error occurred while adding a new external login configuration");
            return CommonResponses.ErrorResponse.BadRequestResponse<List<ExternalLoginConfiguration>>("An error occurred while adding a new external login configuration");
        }
    }

    /// <summary>
    /// Approves the authentication configuration for the specified app ID.
    /// </summary>
    /// <param name="appId">The app ID of the authentication configuration to be approved.</param>
    /// <returns>Returns an ApiResponse object representing the result of the approval operation.
    /// The ApiResponse object contains a Boolean value indicating whether the approval was successful.</returns>
    public async Task<ApiResponse<bool>> ApproveAuthConfiguration(Guid appId)
    {
        var configuration = await _dbRepo.GetByIdAsync(appId);
        if (configuration == null)
        {
            return
                CommonResponses.ErrorResponse.NotFoundResponse<bool>(AppResponses.CouldNotUpdateAuthConfig);
        }

        configuration.IsActive = true;
        var saved = await _dbRepo.SaveChangesAsync() > 0;

        if (saved)
        {
            _ = _memoryCacheService.KeyDeleteAsync($"{CommonConstants.RedisAuthConfigKey}:{appId}");

            _logger.LogInformation("Configuration with id: {Id} successfully approved", appId);
        }
        else
        {
            _logger.LogInformation("Failed to approve configuration with id {Id}", appId);
        }

        return
            CommonResponses.SuccessResponse.OkResponse(saved);
    }

    /// <summary>
    /// Removes the specified authentication channels from the authentication configuration with the given ID.
    /// </summary>
    /// <param name="id">The ID of the authentication configuration.</param>
    /// <param name="channels">The list of authentication channels to remove.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>An ApiResponse representing the result of the operation.</returns>
    public async Task<ApiResponse<bool>> RemoveAuthenticationChannelsAsync(Guid id,
        List<AuthenticationChannel> channels, CancellationToken cancellationToken)
    {
        var configuration =
            await _dbRepo.GetByIdAsync( id, cancellationToken);
        if (configuration == null)
        {
            return
                CommonResponses.ErrorResponse.NotFoundResponse<bool>(AppResponses.CouldNotUpdateAuthConfig);
        }

        var channelsToRemove = channels.Select(x => new SelectedAuthChannel(x)).ToList();
        foreach (var channel in channelsToRemove)
        {
            configuration.AuthenticationChannels.Remove(channel);
        }


        var saved = await _dbRepo.SaveChangesAsync(cancellationToken) > 0;
        if (saved)
        {
            _ = _memoryCacheService.KeyDeleteAsync($"{CommonConstants.RedisAuthConfigKey}:{id}");
            _logger.LogInformation("auth channels with id: {Id} successfully updated", id);
        }
        else
        {
            _logger.LogInformation(AppResponses.FailedToUpdateConfigWithId, id);
        }

        return
            CommonResponses.SuccessResponse.OkResponse(saved);
    }
    /// <summary>
    /// Removes the specified external logins from the authentication configuration with the given ID.
    /// </summary>
    /// <param name="id">The ID of the authentication configuration.</param>
    /// <param name="externalLogins">The list of external logins to remove.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>An ApiResponse representing the result of the operation.</returns>
    public async Task<ApiResponse<bool>> RemoveExternalLoginsAsync(Guid id,
        List<ExternalLoginConfiguration> externalLogins, CancellationToken cancellationToken)
    {
        var configuration =
            await _dbRepo.GetQueryable().Include(e => e.ExternalLoginConfigurations).Where(p => p.Id == id)
                            .SingleOrDefaultAsync(cancellationToken);
        if (configuration == null)
        {
            return
                CommonResponses.ErrorResponse.NotFoundResponse<bool>(AppResponses.CouldNotUpdateAuthConfig);
        }

        foreach (var exLogin in externalLogins)
        {
            configuration.RemoveExternalLoginConfigurations(exLogin);
        }

        var saved = await _dbRepo.UpdateAsync(configuration, cancellationToken) > 0;
        if (saved)
        {
            _ = _memoryCacheService.KeyDeleteAsync($"{CommonConstants.RedisAuthConfigKey}:{id}");
            _logger.LogInformation("external logins with id: {Id} successfully updated", id);
        }
        else
        {
            _logger.LogInformation("Failed to remove external logins with id {Id}", id);
            return
            CommonResponses.ErrorResponse.FailedDependencyErrorResponse<bool>();
        }

        return
            CommonResponses.SuccessResponse.OkResponse(true);
    }

    public async Task<(bool IsSaved, T Data)> PartialUpdateAsync<T>(Guid id, JsonPatchDocument<AuthenticationConfiguration> patchConfig) where T : AuthConfigStoreResponse
    {
        try
        {
            _logger.LogInformation("UpdateAsync called with id: {Id}", id);

            var configuration = await _dbRepo.GetByIdAsync(id);

            if (configuration == null)
            {
                _logger.LogWarning("Configuration with id: {Id} not found", id);
                return (false, default);
            }
            patchConfig.ApplyTo(configuration);

            var saved = await _dbRepo.UpdateAsync(configuration) > 0;
            switch (saved)
            {
                case true:
                    _ = _memoryCacheService.KeyDeleteAsync($"{CommonConstants.RedisAuthConfigKey}:{id}");

                    _logger.LogInformation("Configuration with id: {Id} successfully updated", id);
                    break;
                default:

                    _logger.LogInformation(AppResponses.FailedToUpdateConfigWithId, id);
                    break;
            }

            var response = configuration.Adapt<T>();


            return (saved, response);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception Occured.Configuration with id {Id} not updated", id);
            return (false, null);
        }
    }
    public async Task<ApiResponse<bool>> UpdateAllowedUrls(Guid id, List<string> allowedUrls,
       CancellationToken cancellationToken)
    {
        var configuration = await _dbRepo.GetByIdAsync(id, cancellationToken);
        if (configuration == null)
        {
            return
                CommonResponses.ErrorResponse.NotFoundResponse<bool>(AppResponses.CouldNotUpdateAuthConfig);
        }
        configuration.AllowedRedirectUrls = allowedUrls;
        var saved = await _dbRepo.UpdateAsync(configuration, cancellationToken) > 0;
        if (saved)
        {
            _ = _memoryCacheService.KeyDeleteAsync($"{CommonConstants.RedisAuthConfigKey}:{id}");
            _logger.LogInformation("Allowed urls with id: {Id} successfully updated", id);
        }
        else
        {
            _logger.LogInformation(AppResponses.FailedToUpdateConfigWithId, id);
            return
                CommonResponses.ErrorResponse.NotFoundResponse<bool>(AppResponses.CouldNotUpdateAuthConfig);
        }
        return
            CommonResponses.SuccessResponse.OkResponse(saved);
    }

}