using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using StackExchange.Redis;
using Order = StackExchange.Redis.Order;

namespace Hubtel.Authentication.Core.Api.Services
{
    public class RedisCacheRepository : IRedisCacheRepository
    {
        private readonly ILogger<RedisCacheRepository> _logger;

        public static readonly string RedisKey = $"hubtel:redis-api";

        /// <summary>
        /// Represents a repository for interacting with Redis cache.
        /// </summary>
        /// <param name="database">The Redis database.</param>
        /// <param name="logger">The logger used for logging.</param>
        public RedisCacheRepository(IDatabase database, ILogger<RedisCacheRepository> logger)
        {
            _logger = logger;
            Database = database;
        }


        /// <summary>
        /// Gets or sets the Database property.
        /// </summary>
        /// <value>
        /// The Database property of type <see cref="IDatabase"/>.
        /// </value>
        public IDatabase Database { get; set; }


        /// <summary>
        /// Adds an item to a sorted set in the database.
        /// </summary>
        /// <typeparam name="T">The type of the model.</typeparam>
        /// <param name="key">The key of the sorted set.</param>
        /// <param name="score">The score of the item.</param>
        /// <param name="model">The model to be added.</param>
        /// <returns>A boolean value indicating whether the item was added successfully.</returns>
        public async Task<bool> AddToSortedSet<T>(string key, long score, T model)
        {
            var result = await Database.SortedSetAddAsync(
                key, JsonConvert.SerializeObject(model), score
            );

            _logger.LogDebug($"Added to sorted set => {key}");
            return result;
        }


        /// <summary>
        /// Removes elements from a sorted set by the specified score.
        /// </summary>
        /// <param name="key">The key of the sorted set.</param>
        /// <param name="score">The score used to identify the elements to be removed.</param>
        /// <returns>The number of elements removed from the sorted set.</returns>
        public async Task<long> RemoveFromSortedSet(string key, double score)
        {
            var result = await Database.SortedSetRemoveRangeByScoreAsync(
                key, score, score
            );

            _logger.LogDebug($"Removed from sorted set => {key}");
            return result;
        }

        /// <summary>
        /// Finds items in a sorted set based on the score range.
        /// </summary>
        /// <typeparam name="T">The type of items in the set.</typeparam>
        /// <param name="key">The key of the sorted set.</param>
        /// <param name="startScore">The starting score of the range.</param>
        /// <param name="endScore">The ending score of the range.</param>
        /// <returns>
        /// An <see cref="IEnumerable{T}"/> containing the items in the sorted set that match the score range.
        /// </returns>
        public async Task<IEnumerable<T>> FindInSortedSet<T>(string key, long startScore, long endScore)
        {
            var results = (
                await Database.SortedSetRangeByScoreAsync(key,
                    order: Order.Descending, start: startScore, stop: endScore)
            ).Select(value => JsonConvert.DeserializeObject<T>(value));

            _logger.LogDebug($"Retrieve items for key => {key}");

            return results;
        }

        /// <summary>
        /// Fetches items of type T from a sorted set stored in a Redis database.
        /// </summary>
        /// <typeparam name="T">The type of items to fetch</typeparam>
        /// <param name="key">The key of the sorted set</param>
        /// <param name="order">The order in which the items should be fetched (default is descending order)</param>
        /// <returns>An enumerable collection of items of type T</returns>
        public async Task<IEnumerable<T>> FetchFromSortedSet<T>(string key, Order order = Order.Descending)
        {
            var results = (
                await Database.SortedSetRangeByRankAsync(key,
                    order: order)
            ).Select(value => JsonConvert.DeserializeObject<T>(value));

            _logger.LogDebug($"Retrieve items for key => {key}");

            return results;
        }

        /// <summary>
        /// Fetches a subset of items from a sorted set in a Redis database.
        /// </summary>
        /// <typeparam name="T">The type of items to fetch.</typeparam>
        /// <param name="key">The key of the sorted set.</param>
        /// <param name="page">The page number of items to retrieve (starting from 1).</param>
        /// <param name="size">The number of items to retrieve per page.</param>
        /// <param name="order">The order in which to retrieve the items (default is descending).</param>
        /// <returns>An <see cref="IEnumerable{T}"/> of the fetched items.</returns>
        public async Task<IEnumerable<T>> FetchFromSortedSet<T>(string key, int page, int size,
            Order order = Order.Descending)
        {
            var start = size * Math.Max(page - 1, 0);

            var results = (
                await Database.SortedSetRangeByRankAsync(
                    key,
                    order: order,
                    start: start,
                    stop: start + size - 1
                )
            ).Select(value => JsonConvert.DeserializeObject<T>(value));

            _logger.LogDebug($"Retrieve items for key => {key}");

            return results;
        }

        /// <summary>
        /// Sets a value in the database using the specified key and model.
        /// </summary>
        /// <typeparam name="T">The type of the model.</typeparam>
        /// <param name="key">The key for the value.</param>
        /// <param name="model">The model to be serialized and stored.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task<bool> SetKey<T>(string key, T model)
        {
            return await Database.StringSetAsync(key, JsonConvert.SerializeObject(model));
        }

        /// <summary>
        /// Retrieves a value from the cache stored under the specified key and deserializes it into the specified type.
        /// </summary>
        /// <typeparam name="T">The type of the value to deserialize.</typeparam>
        /// <param name="key">The key used to store the value in the cache.</param>
        /// <returns>
        /// The deserialized value retrieved from the cache, or the default value of type T if the key does not exist in the cache.
        /// </returns>
        public async Task<T> GetKey<T>(string key)
        {
            return JsonConvert.DeserializeObject<T>(await Database.StringGetAsync(key));
        }

        /// <summary>
        /// Retrieves the cardinality (number of elements) of a sorted set.
        /// </summary>
        /// <param name="key">The key of the sorted set.</param>
        /// <returns>The cardinality of the sorted set.</returns>
        public async Task<long> SortedSetCardinality(string key)
        {
            return await Database.SortedSetLengthAsync(key);
        }
    }
}