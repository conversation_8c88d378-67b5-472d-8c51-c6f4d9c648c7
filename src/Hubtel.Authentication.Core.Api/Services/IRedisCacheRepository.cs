using System.Collections.Generic;
using System.Threading.Tasks;
using StackExchange.Redis;
using Order = StackExchange.Redis.Order;

namespace Hubtel.Authentication.Core.Api.Services
{
    public interface IRedisCacheRepository
    {
        public IDatabase Database { get; }

        public Task<bool> SetKey<T>(string key, T model);

        public Task<T> GetKey<T>(string key);

        public Task<bool> AddToSortedSet<T>(string key, long score, T model);

        public Task<long> RemoveFromSortedSet(string key, double score);

        public Task<IEnumerable<T>> FindInSortedSet<T>(string key, long startScore, long endScore);

        public Task<IEnumerable<T>> FetchFromSortedSet<T>(string key, Order order = Order.Descending);

        public Task<IEnumerable<T>> FetchFromSortedSet<T>(string key, int page, int size,
            Order order = Order.Descending);

        public Task<long> SortedSetCardinality(string key);
    }
}