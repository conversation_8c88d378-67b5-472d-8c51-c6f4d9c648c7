using System;
using System.Linq;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Hubtel.Authentication.Commons.Extensions;
using Hubtel.Authentication.Commons.Models;
using Hubtel.Authentication.Core.Api.Components.Utils;
using Hubtel.Authentication.Core.Api.Dtos;
using Hubtel.Authentication.Core.Api.Extensions;
using Hubtel.Authentication.Core.Api.Models;
using Hubtel.Authentication.Core.Api.Models.AppModels;
using Hubtel.Authentication.Core.Api.Models.Requests;
using Hubtel.Authentication.Core.Api.Models.Responses;
using Hubtel.Authentication.Core.Api.Services.Interface;
using Hubtel.Redis.Sdk.Services;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;


namespace Hubtel.Authentication.Core.Api.Services.Provider;

public class UssdService : IUssdService
{
    private readonly ILogger<UssdService> _logger;
    private readonly IDatabase _otpCacheRepository;
    private readonly IDatabase _memoryCacheService;
    private readonly IDatabase _authConfigRedisStore;
    private readonly IAuthenticationConfigStoreService _authenticationConfigStore;


    /// <summary>
    /// UssdService class is responsible for handling USSD requests and managing related services.
    /// </summary>
    /// <param name="multiRedisHostCacheRepository">An instance of IMultiRedisHostCacheRepository used for interacting with Redis cache</param>
    /// <param name="logger">An instance of ILogger&lt;UssdService&gt; for logging purposes</param>
    /// <param name="authenticationConfigStore">the authentication configuration store.</param>
    public UssdService(IMultiRedisHostCacheRepository multiRedisHostCacheRepository,
        ILogger<UssdService> logger,
        IAuthenticationConfigStoreService authenticationConfigStore)
    {
        _logger = logger;
        _authenticationConfigStore = authenticationConfigStore;
        _authConfigRedisStore =
            multiRedisHostCacheRepository.GetDb(RedisConstants.MainRedisHostName, RedisConstants.AuthConfigurationDb);
        _otpCacheRepository =
            multiRedisHostCacheRepository.GetDb(RedisConstants.MainRedisHostName, RedisConstants.DefaultOtpCacheDb);
        _memoryCacheService = multiRedisHostCacheRepository.GetDb(RedisConstants.MainCustomerDataRedisHostName,
            RedisConstants.DefaultConsumerBlacklistDb);
    }

    /// <summary>
    /// Generates a one-time password (OTP) asynchronously.
    /// </summary>
    /// <param name="model">The GenerateUssdOtpDto object containing the necessary information to generate the OTP.</param>
    /// <param name="accountDetailsAccountLookupData"></param>
    /// <param name="accountDetails"></param>
    /// <returns>An ApiResponse object with the generated OTP information.</returns>
    public async Task<ApiResponse<GenerateUssdOtpResponse>> GenerateOtpAsync(GenerateUssdOtpDto model,
        PhoneNumberLookupData accountDetailsAccountLookupData, GetBackOfficeAuthConfigStoreResponse accountDetails)
    {
        try
        {
            var isBlacklisted = await _memoryCacheService.SetContainsAsync("ConsumerBlackListQS", model.PhoneNumber);
            if (isBlacklisted)
            {
                _logger.LogWarning("phone number {ModelPhoneNumber} is blacklisted", model.PhoneNumber);
                return CommonResponses.ErrorResponse.BadRequestResponse<GenerateUssdOtpResponse>(
                    "There seem to be a problem with this account. Contact <EMAIL> for help");
            }

            // generate code and prefix
            var code = PinGenerator.Generate(4, PinGenerator.PasswordCharsNumeric);

            var prefix = PinGenerator.Generate(4, PinGenerator.PasswordCharsAlpha).ToUpper();

            var otp = new ExtendedOtp
            {
                OtpCode = $"{prefix}-{code}",
                Code = code,
                Prefix = prefix,
                CreatedAt = DateTime.UtcNow,
                RequestId = Guid.NewGuid().ToString("N"),
                Msisdn = model.PhoneNumber,
                SenderId = accountDetails.SenderId,
                CountryCode = model.CountryCode,
                OtpType = "normal",
                Location = model.Location,
                DeviceName = model.DeviceName,
                LoginAppName = accountDetails.AppName,
                AppId = model.AppId,
                AuthenticationChannel=AuthenticationChannel.Ussd
            };


            var score = otp.Msisdn.StringToInt(true);

            await _otpCacheRepository.SortedSetRemoveRangeByScoreAsync(CommonConstants.RedisOtpKey, score, score);

            var res = await _otpCacheRepository.AddToSortedSet(CommonConstants.RedisOtpKey, score, otp);

            if (!res)
            {
                _logger.LogWarning("Failed to store otp {ModelPhoneNumber}", model.PhoneNumber);
                return CommonResponses.ErrorResponse.FailedDependencyErrorResponse<GenerateUssdOtpResponse>();
            }


            var channels = new ApiResponse<List<AuthenticationChannel>>
            {
               
                Data = new List<AuthenticationChannel>()
            };
            var firstTimeLoggedIn = await _authConfigRedisStore.StringGetAsync(
                $"{CommonConstants.RedisUssdSuccessKey}:{accountDetails.Id}:{model.PhoneNumber}");
            var whiteList = accountDetails.WhiteLists.FirstOrDefault(x => x.PhoneNumber == model.PhoneNumber);
            if (model.PhoneNumber.IsInternationalNumber()||!string.IsNullOrWhiteSpace(firstTimeLoggedIn))
            {
                channels = await _authenticationConfigStore.GetAllAuthenticationChannelsAsync(accountDetails.Id,
                    CancellationToken.None);
            }

            if (whiteList!=null)
            {
                
                channels.Data.Add(AuthenticationChannel.Sms);
            }


            var data = new GenerateUssdOtpResponse
            {
                VerificationUssd = "*713*2#", //configure from appsettings
                OtpCode = code,
                RequestId = otp.RequestId,
                Account = new
                {
                    MobileNumber = accountDetailsAccountLookupData.MobileNumber.ToRedactedNumber(),
                    Email = string.IsNullOrWhiteSpace(accountDetailsAccountLookupData.Email)
                        ? null
                        : accountDetailsAccountLookupData.Email.ToRedactedEmail()
                },
                Channels = channels.Data ?? new List<AuthenticationChannel>()
            };

            return CommonResponses.SuccessResponse.OkResponse(data);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Customer {MobileNumber}, An error occured generating otp. Error {ErrorMessage}",
                model.PhoneNumber, e.Message);
            return CommonResponses.ErrorResponse.FailedDependencyErrorResponse<GenerateUssdOtpResponse>();
        }
    }

    public async Task<bool> ValidateRequestId(string phoneNumber, string requestId)
    {
        var otp = await FindLoginAttempt(phoneNumber);
        return otp != null && string.Equals(otp.RequestId, requestId);
    }
    
    private async Task<ExtendedOtp> FindLoginAttempt(string phoneNumber)
    {
        var score = phoneNumber.StringToInt(true);
        
        var otplist = (await _otpCacheRepository.FindInSortedSet<ExtendedOtp>(CommonConstants.RedisOtpKey, score, score)).ToList();

        var otp = otplist.Find(t => (DateTime.UtcNow - t.CreatedAt).TotalMinutes < 5);
        
        return otp;
    }
}