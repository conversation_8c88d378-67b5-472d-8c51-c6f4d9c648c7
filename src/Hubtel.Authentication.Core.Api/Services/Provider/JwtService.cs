using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Globalization;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using Hubtel.Authentication.Core.Api.Models.AppModels;
using Hubtel.Authentication.Core.Api.Models.Responses;
using Hubtel.Authentication.Core.Api.Options;
using Hubtel.Authentication.Core.Api.Models;

namespace Hubtel.Authentication.Core.Api.Services.Provider
{
    public interface IJwtService
    {
        /// <summary>
        /// Generates the app token for the given mobile number.
        /// </summary>
        /// <returns>
        /// The app result containing the generated token, mobile number, and additional data if provided.
        /// If an error occurs during token generation, an error app result with the error message is returned.
        /// </returns>
        AppResult GenerateAppToken(TokenGenerationModel generationModel);
    }

    /// <summary>
    /// This class provides functionality related to JWT (JSON Web Tokens).
    /// </summary>
    public class JwtService : IJwtService
    {
        private readonly JwtConfig _jwtOptions;
        public JwtService(IOptions<JwtConfig> jwtOptions)
        {
            _jwtOptions = jwtOptions.Value;
        }

        /// <summary>
        /// Generates the app token for the given mobile number.
        /// </summary>
        /// <param name="generationModel"></param>
        /// The app result containing the generated token, mobile number, and additional data if provided.
        /// If an error occurs during token generation, an error app result with the error message is returned.
        public AppResult GenerateAppToken(TokenGenerationModel generationModel)
        {
            var accountInformation = generationModel.AccountLookupData;
            var signingKey = generationModel.AuthenticationConfiguration.SigningKey;
            var validityPeriod = generationModel.AuthenticationConfiguration.ValidityPeriod;
            var qrDataRequest = generationModel.QrDataRequest;
            var requestId = generationModel.RequestId;
            var issuer=string.IsNullOrEmpty(generationModel.AuthenticationConfiguration.Issuer) ? _jwtOptions.Issuer : generationModel.AuthenticationConfiguration.Issuer;
            var audience=string.IsNullOrEmpty(generationModel.AuthenticationConfiguration.Audience) ? _jwtOptions.Audience : generationModel.AuthenticationConfiguration.Audience;
            try
            {
                if (accountInformation==null)
                {
                    return AppResult.Error(AppResponses.AccountInformationIsNull);
                }
                var claims = accountInformation.TokenData
                    .Where(x => x.Value != null)
                    .Select(x => new Claim(x.Key, x.Value))
                    .ToArray();
                
                var expiryDate = DateTime.UtcNow.AddDays(validityPeriod);

                var jwtToken = new JwtSecurityToken
                (
                    issuer,
                    audience,
                    claims,
                    expires: expiryDate,
                    notBefore: DateTime.UtcNow,
                    signingCredentials: new SigningCredentials(
                        new SymmetricSecurityKey(Encoding.UTF8.GetBytes(signingKey)),
                        SecurityAlgorithms.HmacSha256)
                );
                var token = new JwtSecurityTokenHandler().WriteToken(jwtToken);
                
                var tokenData = new VerifyOtpResponse
                {
                    Email=accountInformation.Email,
                    MobileNumber = accountInformation.MobileNumber,
                    RequestId = requestId
                };
                
                if (qrDataRequest != null)
                {
                   
                    tokenData.Token = token;
                    tokenData.PlayerId = qrDataRequest.PlayerId;
                    tokenData.Expiry = expiryDate.ToString(CultureInfo.CurrentCulture);
                }
                else
                {
                    tokenData.Token = token;
                    tokenData.Expiry = expiryDate.ToString(CultureInfo.CurrentCulture);
                }

                return BaseAppResult.Success(data: tokenData);
            }
            catch (Exception ex)
            {
                return BaseAppResult.Error(ex.ToString());
            }

        }
    }
}
