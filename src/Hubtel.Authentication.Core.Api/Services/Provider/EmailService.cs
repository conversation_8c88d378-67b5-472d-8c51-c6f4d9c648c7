using System;
using System.Linq;
using System.Threading.Tasks;
using Akka.Actor;
using Akka.Hosting;
using Hubtel.Authentication.Commons.Extensions;
using Hubtel.Authentication.Commons.Models;
using Hubtel.Authentication.Core.Api.Components.Actors;
using Hubtel.Authentication.Core.Api.Components.Actors.ActorMessages;
using Hubtel.Authentication.Core.Api.Components.Utils;
using Hubtel.Authentication.Core.Api.Dtos;
using Hubtel.Authentication.Core.Api.Models;
using Hubtel.Authentication.Core.Api.Models.AppModels;
using Hubtel.Authentication.Core.Api.Models.Requests;
using Hubtel.Authentication.Core.Api.Models.Responses;
using Hubtel.Authentication.Core.Api.Services.Interface;
using Hubtel.Redis.Sdk.Services;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using StackExchange.Redis;

namespace Hubtel.Authentication.Core.Api.Services.Provider;

public class EmailService : IEmailService
{
    private readonly IDatabase _otpCacheRepository;
    private readonly IDatabase _memoryCacheService;
    private readonly IDistributedCache _cache;
    private readonly IJwtService _jwtService;
    private readonly ILogger<EmailService> _logger;
    private readonly IAccountLookupService _accountLookupService;
    private readonly IActorRef _mainActor;

    public EmailService(IMultiRedisHostCacheRepository multiRedisHostCacheRepository,
        IDistributedCache cache,
        IJwtService jwtService,
        ILogger<EmailService> logger, IAccountLookupService accountLookupService,IRequiredActor<MainActor> mainActor)
    {
        _otpCacheRepository =
            multiRedisHostCacheRepository.GetDb(RedisConstants.MainRedisHostName, RedisConstants.DefaultOtpCacheDb);
        _memoryCacheService = multiRedisHostCacheRepository.GetDb(RedisConstants.MainCustomerDataRedisHostName,
            RedisConstants.DefaultConsumerBlacklistDb);
        _cache = cache;
        _jwtService = jwtService;
        _logger = logger;
        _accountLookupService = accountLookupService;
        _mainActor = mainActor.ActorRef;
    }


    public async Task<ApiResponse<GenerateOtpResponse>> SendOtpAsync(GenerateEmailOtpDto model,
        GetBackOfficeAuthConfigStoreResponse appConfig)
    {
        try
        {
            if (await _memoryCacheService.SetContainsAsync("ConsumerBlackListQS", model.PhoneNumber))
            {
                _logger.LogWarning("phone number {ModelPhoneNumber} is blacklisted", model.PhoneNumber);
                return CommonResponses.ErrorResponse.BadRequestResponse<GenerateOtpResponse>(
                    "There seem to be a problem with this account. Contact <EMAIL> for help");
            }


            // generate code and prefix
            var code = PinGenerator.Generate(4, PinGenerator.PasswordCharsNumeric);

            var prefix = PinGenerator.Generate(4, PinGenerator.PasswordCharsAlpha).ToUpper();

            var otp = new ExtendedOtp
            {
                OtpCode = $"{prefix}-{code}",
                Code = code,
                Prefix = prefix,
                CreatedAt = DateTime.UtcNow,
                RequestId = Guid.NewGuid().ToString("N"),
                Msisdn = model.PhoneNumber,
                SenderId = appConfig.SenderId,
                CountryCode = model.CountryCode,
                OtpType = "normal",
                Email = model.LinkedEmail,
                AuthenticationChannel = AuthenticationChannel.Email
            };


            _mainActor.Tell(new ProcessEmailOtpRequest(otp, appConfig));

            await Task.CompletedTask;

            var data = new GenerateOtpResponse
            {
                OtpPrefix = otp.Prefix,
                RequestId = otp.RequestId,
            };

            return CommonResponses.SuccessResponse.OkResponse(data);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Customer {MobileNumber}, An error occured generating otp. Error {ErrorMessage}",
                model.PhoneNumber, e.Message);
            return CommonResponses.ErrorResponse.FailedDependencyErrorResponse<GenerateOtpResponse>();
        }
    }

    public async Task<ApiResponse<VerifyOtpResponse>> VerifyOtpAsync(VerifyEmailOtpDto model)
    {
        try
        {
            model.OtpCode = $"{model.Prefix}-{model.OtpCode}";

            var score = model.PhoneNumber.StringToInt(true);

            var otplist = (await _otpCacheRepository.FindInSortedSet<Otp>(CommonConstants.RedisOtpKey, score, score))
                .ToList();

            var otp = otplist.Find(t => (DateTime.UtcNow - t.CreatedAt).TotalMinutes < 5);
            if (otp == null)
            {
                _logger.LogWarning("OtpExpired for {ModelPhoneNumber}", model.PhoneNumber);

                return CommonResponses.ErrorResponse.BadRequestResponse<VerifyOtpResponse>(
                    AppResponses.InvalidRequestId);
            }

            if (otp.IsVerified)
            {
                _logger.LogWarning("Verified for {ModelPhoneNumber}", model.PhoneNumber);

                return CommonResponses.ErrorResponse.ForbiddenResponse<VerifyOtpResponse>("OTP already verified");
            }

            if (otp.RequestId != model.RequestId)
            {
                otp.OTPUseCount++;
                await _otpCacheRepository.RemoveFromSortedSet(CommonConstants.RedisOtpKey, score);
                await _otpCacheRepository.AddToSortedSet(CommonConstants.RedisOtpKey, score, otp);
                return CommonResponses.ErrorResponse.BadRequestResponse<VerifyOtpResponse>(
                    AppResponses.InvalidRequestId);
            }

            if (otp.OTPUseCount >= 3)
            {
                await _cache.RemoveAsync(model.PhoneNumber);
                return CommonResponses.ErrorResponse.ForbiddenResponse<VerifyOtpResponse>(
                    AppResponses.OtpAttemptsExceed);
            }

            if (!otp.Msisdn.Equals(model.PhoneNumber))
            {
                return CommonResponses.ErrorResponse.BadRequestResponse<VerifyOtpResponse>(AppResponses
                    .InvalidMobileNumber);
            }

            if (!otp.OtpCode.Equals(model.OtpCode))
            {
                otp.OTPUseCount++;

                await _otpCacheRepository.RemoveFromSortedSet(CommonConstants.RedisOtpKey, score);

                await _otpCacheRepository.AddToSortedSet(CommonConstants.RedisOtpKey, score, otp);

                return CommonResponses.ErrorResponse.BadRequestResponse<VerifyOtpResponse>(AppResponses.InvalidOtp);
            }


            await _otpCacheRepository.RemoveFromSortedSet(CommonConstants.RedisOtpKey, score);
            otp.VerifiedAt = DateTime.UtcNow;
            otp.IsVerified = true;
            await _otpCacheRepository.AddToSortedSet(CommonConstants.RedisOtpKey, score, otp);
            var accountDetails = await _accountLookupService.RetrieveCachedAccountInfo(model.AppId, model.PhoneNumber);
            if (accountDetails == null)
            {
                return CommonResponses.ErrorResponse.BadRequestResponse<VerifyOtpResponse>(AppResponses
                    .HttpRequestFailed);
            }
            var clientApp = await _accountLookupService.ClientAppDetails(model.AppId);
            if (clientApp == null || string.IsNullOrWhiteSpace(clientApp.AccountLookUpUrl))
            {
                return CommonResponses.ErrorResponse.BadRequestResponse<VerifyOtpResponse>(AppResponses
                    .AppMayNotBeConfigured);
            }
            var auth = _jwtService.GenerateAppToken(new TokenGenerationModel { AccountLookupData = accountDetails, AuthenticationConfiguration = clientApp });
            if (!auth.IsSuccessful)
            {
                _logger.LogError("Customer {MobileNumber}, An error occured verifying otp. Error {ErrorMessage}",
                model.PhoneNumber, auth.Message);
                return CommonResponses.ErrorResponse.FailedDependencyErrorResponse<VerifyOtpResponse>();
            }
            var verifyOtpResponse = JsonConvert.DeserializeObject<VerifyOtpResponse>(JsonConvert.SerializeObject(auth.Data));
            verifyOtpResponse!.MobileNumber = model.PhoneNumber;
            return CommonResponses.SuccessResponse.OkResponse(verifyOtpResponse);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Customer {MobileNumber}, An error occured verifying otp. Error {ErrorMessage}",
                model.PhoneNumber, e.Message);
            return CommonResponses.ErrorResponse.FailedDependencyErrorResponse<VerifyOtpResponse>();
        }
    }
}