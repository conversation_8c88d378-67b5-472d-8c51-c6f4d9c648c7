using JustEat.StatsD;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using QRCoder;
using System;
using System.IO;
using System.Threading.Tasks;
using Flurl;
using Flurl.Http;
using Hubtel.Authentication.Commons.Services.QrCode;
using Hubtel.Authentication.Core.Api.Models;
using Hubtel.Authentication.Core.Api.Models.AppModels;
using Hubtel.Authentication.Core.Api.Models.Requests;
using Hubtel.Authentication.Core.Api.Models.Responses;
using Hubtel.Authentication.Core.Api.Options;
using Hubtel.Authentication.Core.Api.Services.Interface;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using SixLabors.ImageSharp;
using Color = SixLabors.ImageSharp.Color;
using Image = SixLabors.ImageSharp.Image;

namespace Hubtel.Authentication.Core.Api.Services.Provider
{
    /// <summary>
    /// Implementation of the IQrService interface.
    /// Provides methods for generating and verifying QR codes.
    /// </summary>
    public class QrService : IQrService
    {
        private const string InvalidQrCodeMessage = "Invalid Qr Code";
        private readonly IJwtService _jwtService;
        private readonly ICipherService _cipherService;
        private readonly IStatsDPublisher _statsD;
        private readonly ILogger<QrService> _logger;

        private readonly string qrEscapeChars = "^";
        private readonly IAccountLookupService _accountLookupService;
        private readonly QrOptions _qrOptions;


        /// <summary>
        /// Initializes a new instance of the QrService class with the specified dependencies.
        /// </summary>
        /// <param name="jwtService">The JwtService to use for generating and validating JSON Web Tokens.</param>
        /// <param name="cipherService">The ICipherService to use for encrypting and decrypting data.</param>
        /// <param name="statsD">The IStatsDPublisher to use for publishing statistics to StatsD.</param>
        /// <param name="logger">The ILogger to use for logging debug information.</param>
        /// <param name="accountLookupService">the account lookup service.</param>
        /// <param name="qrOptions">the qr code settings.</param>
        public QrService(IJwtService jwtService,
            ICipherService cipherService,
            IStatsDPublisher statsD,
            ILogger<QrService> logger, IAccountLookupService accountLookupService, IOptions<QrOptions> qrOptions)
        {
            _jwtService = jwtService;
            _cipherService = cipherService;
            _statsD = statsD;
            _logger = logger;
            _accountLookupService = accountLookupService;
            _qrOptions = qrOptions.Value;
        }

        /// <summary>
        /// Method to generate a QR token asynchronously.
        /// </summary>
        /// <param name="qrDataRequest">The QR data request.</param>
        /// <returns>An API response containing a QR challenge response.</returns>
        public async Task<ApiResponse<QrChallengeResponse>> GenerateQrTokenAsync(QrData qrDataRequest)
        {
            try
            {
                var qrSalt = Guid.NewGuid().ToString("N").Substring(0, 8);
                var qrDataString =
                    $"{qrDataRequest.PlayerId}{qrEscapeChars}{qrDataRequest.AppId}{qrEscapeChars}{qrDataRequest.PhoneNumber}{qrEscapeChars}{qrSalt}";
                var qrDataEncoded = _cipherService.Encrypt(qrDataString);

                var qrLogo = Path.Combine(Environment.CurrentDirectory, @"wwwroot/images/qr_logo.png");

                // https://github.com/codebude/QRCoder/wiki/Advanced-usage---QR-Code-renderers
                var qrGenerator = new QRCodeGenerator();
                var qrCodeData = qrGenerator.CreateQrCode(qrDataEncoded, QRCodeGenerator.ECCLevel.H);
                var qrCode = new ImageSharpQrCode(qrCodeData);
                var icon = await Image.LoadAsync(qrLogo);
                var qrCodeImage = qrCode.GetGraphic(5, Color.Black, Color.White, Color.Transparent, icon,
                    LogoLocation.Center);

                using var ms = new MemoryStream();
                await qrCodeImage.SaveAsPngAsync(ms); // Change image format as needed
                var imageBytes = ms.ToArray();
                var qrCodeImageAsBase64 = Convert.ToBase64String(imageBytes);
                var qrCodeResponse = new QrChallengeResponse
                {
                    QrCodeDataUrl = $"data:image/png;base64,{qrCodeImageAsBase64}",
                    QrCodeData = qrDataEncoded,
                    QrId = Guid.NewGuid().ToString("n")
                };
                return CommonResponses.SuccessResponse.OkResponse(qrCodeResponse, "Qr code");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occured generating QR code. Error {ErrorMessage}", ex.Message);
                return CommonResponses.ErrorResponse.FailedDependencyErrorResponse<QrChallengeResponse>();
            }
        }

        /// <summary>
        /// Verifies the QR token asynchronously.
        /// </summary>
        /// <param name="qrDataRequest">The QR data request.</param>
        /// <returns>The ApiResponse containing the verification result.</returns>
        public async Task<ApiResponse<VerifyQrResponse>> VerifyQrTokenAsync(QrDataRequest qrDataRequest)
        {
            try
            {
                _logger.LogWarning("Qr Scanned By : {PhoneNumber} {QrData}", qrDataRequest.PhoneNumber,
                    JsonConvert.SerializeObject(qrDataRequest.QrData));

                var qrDataDecoded = _cipherService.DecryptQrData(qrDataRequest.QrData);


                if (!qrDataDecoded.Success)
                {
                    var client = await _accountLookupService.ClientAppDetails(qrDataRequest.AppId);

                    var errorResponse = new ApiResponse<dynamic>(AppResponses.LoginFailed, null)
                    {
                        Code = $"{StatusCodes.Status400BadRequest}",
                        Data = new
                        {
                            MobileNumber = qrDataRequest.PhoneNumber,
                            qrDataRequest.DeviceId
                        },
                        Message = "Login failed"
                    };
                    if (client == null)
                    {
                        return CommonResponses.ErrorResponse.BadRequestResponse<VerifyQrResponse>(InvalidQrCodeMessage);
                    }

                    _ = NotifyFailure(client.Id.ToString(), errorResponse);
                    return CommonResponses.ErrorResponse.BadRequestResponse<VerifyQrResponse>(InvalidQrCodeMessage);
                }

                if (qrDataRequest.AppId != qrDataDecoded.qrData.AppId)
                {
                    return CommonResponses.ErrorResponse.BadRequestResponse<VerifyQrResponse>(InvalidQrCodeMessage);
                }

                if (qrDataRequest.PhoneNumber != qrDataDecoded.qrData.PhoneNumber)
                {
                    return CommonResponses.ErrorResponse.BadRequestResponse<VerifyQrResponse>(InvalidQrCodeMessage);
                }

                var accountDetails =
                    await _accountLookupService.RetrieveCachedAccountInfo(qrDataRequest.AppId,
                        qrDataRequest.PhoneNumber);
                if (accountDetails == null)
                {
                    return CommonResponses.ErrorResponse.BadRequestResponse<VerifyQrResponse>(AppResponses
                        .HttpRequestFailed);
                }

                var clientApp = await _accountLookupService.ClientAppDetails(qrDataRequest.AppId);
                if (clientApp == null || string.IsNullOrWhiteSpace(clientApp.AccountLookUpUrl))
                {
                    return CommonResponses.ErrorResponse.BadRequestResponse<VerifyQrResponse>(AppResponses
                        .AppMayNotBeConfigured);
                }

                var payload = _jwtService.GenerateAppToken(new TokenGenerationModel { 
                    QrDataRequest= qrDataDecoded.qrData,
                    AccountLookupData = accountDetails,
                    AuthenticationConfiguration = clientApp,
                });


                if (!payload.IsSuccessful)
                {
                    _logger.LogError("Customer {MobileNumber}, An error occured verifying otp. Error {ErrorMessage}",
                    qrDataRequest.PhoneNumber, payload.Message);
                    return CommonResponses.ErrorResponse.FailedDependencyErrorResponse<VerifyQrResponse>();
                }
                _statsD.Increment("QRLogin");
                var notifyPayload =
                    new ApiResponse<dynamic>("Login successful", StatusCodes.Status200OK.ToString(), payload.Data);
                _ = NotifySuccess(clientApp.Id.ToString(), notifyPayload);

                var response =
                    JsonConvert.DeserializeObject<VerifyQrResponse>(JsonConvert.SerializeObject(payload.Data));

                return CommonResponses.SuccessResponse.OkResponse(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occured verifying QR code. Error {ErrorMessage}", ex.Message);
                return CommonResponses.ErrorResponse.FailedDependencyErrorResponse<VerifyQrResponse>();
            }
        }

        private async Task NotifySuccess(string appId, ApiResponse<dynamic> payload)
        {
            _ = await _qrOptions.Success.AppendPathSegment(appId).PostJsonAsync(payload);
        }

        private async Task NotifyFailure(string appId, ApiResponse<dynamic> payload)
        {
            _ = await _qrOptions.Failure.AppendPathSegment(appId).PostJsonAsync(payload);
        }
    }
}