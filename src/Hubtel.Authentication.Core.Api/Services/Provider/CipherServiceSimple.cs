using System;
using System.Text;
using Hubtel.Authentication.Core.Api.Models.Requests;
using Hubtel.Authentication.Core.Api.Services.Interface;

namespace Hubtel.Authentication.Core.Api.Services.Provider
{
    /// <summary>
    /// Represents a simple cipher service that can encrypt and decrypt strings.
    /// </summary>
    public class CipherServiceSimple : ICipherService
    {
        /// <summary>
        /// Initializes a new instance of the CipherServiceSimple class.
        /// </summary>
        public CipherServiceSimple()
        {
        }

        /// <summary>
        /// Decrypts the given cipher text using UTF8 encoding and Base64 decoding. </summary> <param name="cipherText">The cipher text to be decrypted.</param> <returns>The decrypted plain text.</returns>
        /// /
        public string Decrypt(string cipherText)
        {
            return Encoding.UTF8.GetString(Convert.FromBase64String(cipherText));
        }

        /// <summary>
        /// Decrypts the given cipher text and retrieves the QR data.
        /// </summary>
        /// <param name="cipherText">The cipher text to decrypt.</param>
        /// <returns>A tuple containing the decrypted QR data and a boolean indicating whether the decryption was successful.</returns>
        public (QrData qrData, bool Success) DecryptQrData(string cipherText)
        {
            var decodedString = Decrypt(cipherText);
            var qrData = decodedString.Split('^');
            if (qrData.Length < 4)
            {
                return (null, false);
            }

            if (qrData[3].Length < 8)
            {
                return (null, false);
            }

            return (new QrData
            {
                PlayerId = qrData[0],
                AppId = Guid.Parse(qrData[1]),
                PhoneNumber = qrData[2],
            }, true);
        }

        /// <summary>
        /// Encrypts the given text using Base64 encoding.
        /// </summary>
        /// <param name="cipherText">The text to be encrypted.</param>
        /// <returns>The encrypted string.</returns>
        public string Encrypt(string cipherText)
        {
            return Convert.ToBase64String(Encoding.UTF8.GetBytes(cipherText));
        }
    }
}