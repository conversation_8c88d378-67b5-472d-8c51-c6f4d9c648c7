using Hubtel.Kafka.Host.Core;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;
using Hubtel.Authentication.Core.Api.Models.AppModels;
using Hubtel.Authentication.Core.Api.Options;
using Hubtel.Authentication.Core.Api.Services.Interface;

namespace Hubtel.Authentication.Core.Api.Services.Provider
{
    /// <summary>
    /// Kafka service for producing messages.
    /// </summary>
    public class KafkaService : IKafkaService
    {
        private readonly IKafkaRawProducer _producer;
        private readonly KafkaExtra _config;
        private readonly ILogger<KafkaService> _logger;

        /// Class: KafkaService
        /// Responsible for interacting with Kafka using a raw producer.
        /// This class provides methods and properties to initialize and configure
        /// the Kafka service.
        /// /
        public KafkaService(IKafkaRawProducer producer,
            IOptions<KafkaExtra> config,
            ILogger<KafkaService> logger)
        {
            _producer = producer;
            _config = config.Value;
            _logger = logger;
        }

        /// <summary>
        /// Asynchronously produces a message to the given topic.
        /// </summary>
        /// <param name="model">The object representing the message model.</param>
        /// <param name="topic">The topic to which the message should be produced.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task ProduceMessageAsync(object model, string topic)
        {
            try
            {
                var json = JsonConvert.SerializeObject(model);
                await _producer.Produce(topic, json, dr =>
                {
                    _logger.LogDebug("topic => {Topic} model => {Model} deliveryReport => {Status}", topic, json, dr.Status);
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error while producing to {Topic} error -> {Message}", topic, ex.Message);
            }
        }

        public async Task ProduceMessageAsync(NotificationVM model, NotificationTypes type)
        {
            await ProduceMessageAsync(model, $"{_config.TopicPrefix}{type.ToString()}");
        }

        /// <summary>
        /// Notifies the specified options with the given notification data.
        /// </summary>
        /// <param name="notification">The notification data.</param>
        /// <param name="options">The options to notify.</param>
        /// <returns>A task that represents the asynchronous notify operation.</returns>
        public async Task NotifyOptionsAsync(NotificationData notification, string[] options)
        {
            try
            {
                foreach (var option in options)
                {
                    notification.Type = option;
                    await ProduceMessageAsync(notification, _config.NotificationTopic);
                    _logger.LogInformation("Login OTP sent to {Destination}", notification.Destination);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error while sending OTP Notification error -> {Message}", ex.Message);
            }
        }
    }
}
