using System;
using System.Net;
using System.Threading.Tasks;
using Flurl;
using Flurl.Http;
using Hubtel.Authentication.Commons.Extensions;
using Hubtel.Authentication.Core.Api.Dtos;
using Hubtel.Authentication.Core.Api.Models.Responses;
using Hubtel.Authentication.Core.Api.Services.Interface;
using Hubtel.Redis.Sdk.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using StackExchange.Redis;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace Hubtel.Authentication.Core.Api.Services.Provider
{
    /// <summary>
    /// Service to lookup and manage account details.
    /// </summary>
    public class AccountLookupService : IAccountLookupService
    {
        private readonly IDatabase _redisCacheRepository;
        private readonly IAuthenticationConfigStoreService _authenticationConfigStore;
        private readonly ILogger<AccountLookupService> _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="AccountLookupService"/> class.
        /// </summary>
        /// <param name="authenticationConfigStore">The Auth Store configuration.</param>
        /// <param name="redisCacheRepository">The Redis Cache Repository.</param>
        /// <param name="logger">The ILogger instance</param>
        public AccountLookupService(
            IAuthenticationConfigStoreService authenticationConfigStore,
            IMultiRedisHostCacheRepository redisCacheRepository,
            ILogger<AccountLookupService> logger
        )
        {
            _redisCacheRepository =
                redisCacheRepository.GetDb(RedisConstants.MainRedisHostName, RedisConstants.DefaultOtpCacheDb);
            _authenticationConfigStore = authenticationConfigStore;
            _logger = logger;
        }

        public async Task<string> RetrieveCachedQrInfo(Guid applicationId, string qrData, string phoneNumber)
        {
            var key = $"{nameof(qrData)}:{applicationId}:{phoneNumber}:{qrData}";

            _logger.LogInformation(
                "Attempting to retrieve qr information for ApplicationId {ApplicationIdd}from Redis cache",
                applicationId);

            RedisValue lookupData;
            try
            {
                lookupData = await _redisCacheRepository.StringGetAsync(key);

                if (lookupData.HasValue)
                {
                    _logger.LogInformation(
                        "Successfully retrieved account information for ApplicationId {ApplicationId} from Redis cache",
                        applicationId);
                }
                else
                {
                    _logger.LogWarning(
                        "No account information was found in Redis cache for ApplicationId {ApplicationId}",
                        applicationId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex,
                    "Error occurred while retrieving account information for ApplicationId ->{ApplicationId} from Redis cache {Message}",
                    applicationId, ex.Message);
                return null;

            }

            return lookupData.ToString();
        }

        /// <summary>
        /// Retrieves account details with specified lookup url.
        /// </summary>
        /// <param name="lookUpUrl">The lookup url of the client application.</param>
        /// <param name="phoneNumber">the phone number of the user.</param>
        /// <param name="linkedEmail">Optional external email for downstream lookup</param>
        /// <returns>A <see cref="Task"/> representing the result of the asynchronous operation.</returns>
        public async Task<PhoneNumberLookupResponse> LookUpCustomerInfoByPhoneNumber(string lookUpUrl, string phoneNumber, string linkedEmail = "")
        {
            if (string.IsNullOrWhiteSpace(lookUpUrl))
            {
                _logger.LogWarning(
                    "lookUpUrl is null, empty, or consists only of white-space characters. Returning null");

                return null;
            }

            var requestBuilder = lookUpUrl
               .AppendPathSegment(phoneNumber)
               .AllowHttpStatus(StatusCodes.Status400BadRequest)
               .AllowHttpStatus(StatusCodes.Status404NotFound)
               .AllowHttpStatus(StatusCodes.Status424FailedDependency)
               .AllowHttpStatus(StatusCodes.Status500InternalServerError)
               .AllowHttpStatus(StatusCodes.Status403Forbidden)
               .AllowAnyHttpStatus();

            if (!string.IsNullOrEmpty(linkedEmail))
            {
                requestBuilder = requestBuilder.SetQueryParam("linkedEmail", linkedEmail);
            }

            var lookupResponse = await requestBuilder.GetAsync();

            var content = await lookupResponse.ResponseMessage.Content.ReadAsStringAsync();
            if (lookupResponse.ResponseMessage.IsSuccessStatusCode)
            {
                _logger.LogInformation(
                    "Successfully retrieved PhoneNumberLookupCustomerInformation with lookUpUrl: {Url}, phoneNumber: {PhoneNumber} and optional linkedEmail: {LinkedEmail}",
                    lookUpUrl, phoneNumber,linkedEmail);
                var apiResponse = JsonSerializer.Deserialize<PhoneNumberLookupResponse>(content);
                return apiResponse;
            }

            switch (lookupResponse.ResponseMessage.StatusCode)
            {
                case HttpStatusCode.BadRequest:
                    {
                        LogAccountLookupFail(phoneNumber, lookUpUrl, HttpStatusCode.BadRequest, content);
                        return null;
                    }
                case HttpStatusCode.NotFound:
                    var (showCustomMessage, customMessage) = ShowCustomMessage(content);
                    LogAccountLookupFail(phoneNumber, lookUpUrl, HttpStatusCode.NotFound, content);
                    return new PhoneNumberLookupResponse
                    {
                        Code = "404",
                        Message = showCustomMessage ? customMessage : "This number is not associated with any business"
                    };
                case HttpStatusCode.Forbidden:
                    {
                        LogAccountLookupFail(phoneNumber, lookUpUrl, HttpStatusCode.Forbidden, content);

                        return new PhoneNumberLookupResponse
                        {
                            Code = "403",
                            Message = "Unable to log in at this time. Please contact customer support for assistance."
                        };
                    }
                case HttpStatusCode.InternalServerError:
                    {
                        LogAccountLookupFail(phoneNumber, lookUpUrl, HttpStatusCode.InternalServerError, content);

                        return new PhoneNumberLookupResponse
                        {
                            Code = "500",
                            Message = "Sorry, we encountered an unexpected error while processing your request. Please contact support if the problem persists."
                        };
                    }
                case HttpStatusCode.FailedDependency:
                    {
                        LogAccountLookupFail(phoneNumber, lookUpUrl, HttpStatusCode.FailedDependency, content);
                        return new PhoneNumberLookupResponse
                        {
                            Code = "424",
                            Message = "Sorry, we encountered an unexpected error while processing your request. Please contact support if the problem persists."
                        };
                    }
                default:
                    LogAccountLookupFail(phoneNumber, lookUpUrl, (HttpStatusCode)lookupResponse.StatusCode, content);
                    return null;
            }
        }

        public async Task<GetBackOfficeAuthConfigStoreResponse> ClientAppDetails(Guid appId)
        {
            var appConfig =
                await _authenticationConfigStore.GetAsync<GetBackOfficeAuthConfigStoreResponse>(appId);
            return appConfig;
        }


        /// <summary>
        /// Stores the provided account information in Redis Cache.
        /// </summary>
        /// <param name="applicationId"></param>
        /// <param name="accountInfo">The account information to store.</param>
        /// <param name="expiry">The amount of time data should be stored in cache</param>
        /// <returns>A <see cref="Task"/> representing the result of the asynchronous operation. True if the operation was successful, false otherwise.</returns>
        public async Task<bool> SaveAccountInfoToCache(Guid applicationId, PhoneNumberLookupData accountInfo,
            int expiry = 10)
        {
            return await _redisCacheRepository.StringSetAsync(
                $"{nameof(PhoneNumberLookupData)}:{applicationId}:{accountInfo.MobileNumber}",
                JsonConvert.SerializeObject(accountInfo), expiry: TimeSpan.FromMinutes(expiry));
        }

        public async Task<bool> SaveQrInfoToCache(Guid applicationId, string qrData, string phoneNumber,
            int expiry = 10)
        {
            return await _redisCacheRepository.StringSetAsync(
                $"{nameof(qrData)}:{applicationId}:{phoneNumber}:{qrData}", qrData,
                expiry: TimeSpan.FromMinutes(expiry));
        }

        /// <summary>
        /// Retrieves the account information for the provided mobile number from Redis Cache.
        /// </summary>
        /// <param name="applicationId"></param>
        /// <param name="mobileNumber">The mobile number.</param>
        /// <returns>A <see cref="Task"/> representing the result of the asynchronous operation. The <see cref="PhoneNumberLookupData"/> if found, null otherwise.</returns>
        public async Task<PhoneNumberLookupData> RetrieveCachedAccountInfo(Guid applicationId, string mobileNumber)
        {
            PhoneNumberLookupData accountLookupData;
            var key = $"{nameof(PhoneNumberLookupData)}:{applicationId}:{mobileNumber}";

            _logger.LogInformation(
                "Attempting to retrieve account information for ApplicationId {ApplicationIdd} and mobile number {MobileNumber} from Redis cache",
                applicationId, mobileNumber);

            try
            {
                accountLookupData = await _redisCacheRepository.GetKey<PhoneNumberLookupData>(key);

                if (accountLookupData != null)
                {
                    _logger.LogInformation(
                        "Successfully retrieved account information for ApplicationId {ApplicationId} and mobile number {MobileNumber} from Redis cache",
                        applicationId, mobileNumber);
                }
                else
                {
                    _logger.LogWarning(
                        "No account information was found in Redis cache for ApplicationId {ApplicationId} and mobile number {MobileNumber}",
                        applicationId, mobileNumber);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex,
                    "Error occurred while retrieving account information for ApplicationId {ApplicationId} and mobile number {MobileNumber} from Redis cache",
                    applicationId, mobileNumber);
                throw;
            }

            return accountLookupData;
        }
        public async Task<EmailLookUpResponse> LookUpCustomerInfoByEmail(string lookUpUrl, string email)
        {

            if (string.IsNullOrWhiteSpace(lookUpUrl))
            {
                _logger.LogWarning(
                    "lookUpUrl is null, empty, or consists only of white-space characters. Returning null");
                return null;
            }

            var lookupResponse = await lookUpUrl
                .AppendPathSegment(email)
                .AllowHttpStatus(StatusCodes.Status400BadRequest)
                .AllowHttpStatus(StatusCodes.Status404NotFound)
                .AllowHttpStatus(StatusCodes.Status403Forbidden)
                .AllowHttpStatus(StatusCodes.Status424FailedDependency)
                .AllowHttpStatus(StatusCodes.Status500InternalServerError)
                .AllowAnyHttpStatus()
                .GetAsync();
            var content = await lookupResponse.ResponseMessage.Content.ReadAsStringAsync();

            try
            {
                switch (lookupResponse.ResponseMessage.StatusCode)
                {
                    case HttpStatusCode.OK:
                        var apiResponse = JsonSerializer.Deserialize<EmailLookUpResponse>(content);
                        _logger.LogInformation(
                       "Successfully retrieved EmailLookupCustomerInformation with lookUpUrl: {Url} and email: {ProductEmail} with Payload: {@Payload}",
                       lookUpUrl, email, content);
                        return apiResponse;
                    case HttpStatusCode.BadRequest:
                        {
                            LogAccountLookupFail(email, lookUpUrl, HttpStatusCode.BadRequest, content);
                            return null;
                        }
                    case HttpStatusCode.NotFound:
                        LogAccountLookupFail(email, lookUpUrl, HttpStatusCode.NotFound, content);
                        return null;
                    case HttpStatusCode.Forbidden:
                        {
                            var forbiddenResponse = JsonConvert.DeserializeObject<EmailLookUpResponse>(content);
                            forbiddenResponse!.Code = $"{StatusCodes.Status403Forbidden}";
                            if (string.IsNullOrEmpty(forbiddenResponse.Message))
                            {
                                forbiddenResponse.Message = "Sorry, You do not have permission to access this resource.";
                            }

                            LogAccountLookupFail(email, lookUpUrl, HttpStatusCode.Forbidden, content);
                            return forbiddenResponse;
                        }
                    case HttpStatusCode.InternalServerError:
                        {
                            LogAccountLookupFail(email, lookUpUrl, HttpStatusCode.InternalServerError, content);
                            var internalServerResponse = JsonConvert.DeserializeObject<EmailLookUpResponse>(content);
                            internalServerResponse!.Code = $"{StatusCodes.Status500InternalServerError}";
                            return internalServerResponse;
                        }
                    case HttpStatusCode.FailedDependency:
                        {
                            LogAccountLookupFail(email, lookUpUrl, HttpStatusCode.FailedDependency, content);
                            var failedDependencyResponse = JsonConvert.DeserializeObject<EmailLookUpResponse>(content);
                            failedDependencyResponse!.Code = $"{StatusCodes.Status424FailedDependency}";
                            return failedDependencyResponse;
                        }
                    default:
                        _logger.LogError("Error retrieving account details from lookup url: {Url} and Email: {ProductEmail} with response {FullResponse}",
                    lookUpUrl,
                    email, content);
                        return null;
                }
            }
            catch(System.Text.Json.JsonException e)
            {
                _logger.LogError(e, "LookUpCustomerInfoByEmail Could not deserialize JSON response: {JsonResponse} ,EmailLookupUrl: {EmailLookUpUrl} ,UserEmail: {UserEmail}", content,lookUpUrl,email);
                return new EmailLookUpResponse { Code=$"{StatusCodes.Status500InternalServerError}",
                                Message="Something went wrong. Try again later or reach out to our customer service"};
            }
            
        }

        private void LogAccountLookupFail(string userId, string accountLookupurl, HttpStatusCode httpStatusCode, string errorDetails)
        {
            _logger.LogError("Account lookup for {UserId} to {Url} failed with status code {StatusCode} and Error Details: {ErrorDetails}",
                userId, accountLookupurl, httpStatusCode, errorDetails);
        }

        private (bool ShowMessage, string CustomMessage) ShowCustomMessage(string httpResponseContent)
        {
            if (string.IsNullOrEmpty(httpResponseContent))
            {
                return (false, string.Empty);
            }
            try
            {
                var apiResponse = JsonSerializer.Deserialize<PhoneNumberLookupResponse>(httpResponseContent);
                if(string.IsNullOrEmpty(apiResponse!.Message.Trim()))
                {
                    return (false, string.Empty);
                }
                return (true, apiResponse!.Message);
            }
            catch (Exception e)
            {
                _logger.LogWarning(e, "Error deserializing JSON response: {JsonResponse}", httpResponseContent);
                return (false, string.Empty);
            }

        }
    }
}