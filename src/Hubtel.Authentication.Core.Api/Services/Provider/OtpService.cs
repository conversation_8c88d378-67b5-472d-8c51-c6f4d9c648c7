using Akka.Actor;
using Hubtel.Redis.Sdk.Services;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Akka.Hosting;
using Hubtel.Authentication.Commons.Extensions;
using Hubtel.Authentication.Commons.Models;
using Hubtel.Authentication.Core.Api.Components.Actors;
using Hubtel.Authentication.Core.Api.Components.Actors.ActorMessages;
using Hubtel.Authentication.Core.Api.Components.Utils;
using Hubtel.Authentication.Core.Api.Models;
using Hubtel.Authentication.Core.Api.Models.AppModels;
using Hubtel.Authentication.Core.Api.Models.Requests;
using Hubtel.Authentication.Core.Api.Models.Responses;
using Hubtel.Authentication.Core.Api.Services.Interface;
using Hubtel.Authentication.Whatsapp.Sdk.Client;
using Hubtel.Authentication.Whatsapp.Sdk.Models;
using Microsoft.AspNetCore.Http;

namespace Hubtel.Authentication.Core.Api.Services.Provider
{
    /// <summary>
    /// OtpService provides functionality related to generating and verifying OTPs (One-Time Passwords).
    /// </summary>
    public class OtpService : IOtpService
    {
        private const string CustomerErrorLogMessage = "Customer {MobileNumber}, An error occured verifying otp. Error {ErrorMessage}";
        private readonly IDatabase _otpCacheRepository;
        private readonly IDatabase _memoryCacheService;

        private readonly IDistributedCache _cache;
        private readonly IJwtService _jwtService;
        private readonly ILogger<OtpService> _logger;
        private readonly IWhatsAppProxyApi _whatsAppApi;
        private readonly IAccountLookupService _accountLookupService;
        private readonly IActorRef _mainActor;

        /// <summary>
        /// Provides methods for One-Time Password (OTP) generation and verification.
        /// </summary>
        /// <param name="multiRedisHostCacheRepository">The multiRedisHostCacheRepository used for accessing Redis cache.</param>
        /// <param name="cache">The distributed cache used for storing OTP data.</param>
        /// <param name="jwtService">The jwtService used for generating and validating JSON Web Tokens (JWT).</param>
        /// <param name="logger">The logger used for logging events.</param>
        /// <param name="whatsAppApi">the whatsapp api</param>
        /// <param name="accountLookupService">the account lookup service.</param>
        /// <param name="mainActor">a reference to the main actor.</param>
        public OtpService(IMultiRedisHostCacheRepository multiRedisHostCacheRepository,
            IDistributedCache cache,
            IJwtService jwtService,
            ILogger<OtpService> logger, IWhatsAppProxyApi whatsAppApi, IAccountLookupService accountLookupService,IRequiredActor<MainActor> mainActor)
        {
            _otpCacheRepository =
                multiRedisHostCacheRepository.GetDb(RedisConstants.MainRedisHostName, RedisConstants.DefaultOtpCacheDb);
            _memoryCacheService = multiRedisHostCacheRepository.GetDb(RedisConstants.MainCustomerDataRedisHostName,
                RedisConstants.DefaultConsumerBlacklistDb);
            _cache = cache;
            _jwtService = jwtService;
            _logger = logger;
            _whatsAppApi = whatsAppApi;
            _accountLookupService = accountLookupService;
            _mainActor = mainActor.ActorRef;
        }

        /// <summary>
        /// Sends an OTP (One-Time Password) asynchronously.
        /// </summary>
        /// <param name="model">The GenerateOtpDto model containing the necessary information to generate an OTP.</param>
        /// <returns>Returns an ApiResponse object containing the generated OTP information.</returns>
        public async Task<ApiResponse<GenerateOtpResponse>> SendOtpAsync(CreateOtpRequest model)
        {
            try
            {
                if (await _memoryCacheService.SetContainsAsync("ConsumerBlackListQS", model.PhoneNumber))
                {
                    _logger.LogWarning("phone number {ModelPhoneNumber} is blacklisted", model.PhoneNumber);
                    return CommonResponses.ErrorResponse.BadRequestResponse<GenerateOtpResponse>(
                        "There seem to be a problem with this account. Contact <EMAIL> for help");
                }

                var appDetails = await _accountLookupService.ClientAppDetails(model.AppId);
              var whiteList =  appDetails.WhiteLists.FirstOrDefault(x => x.PhoneNumber == model.PhoneNumber);
                // generate code and prefix and persist to cache
                var clientDetails=new OtpReceiverDetails
                {
                    SenderId = appDetails.SenderId,
                    OtpLiabilityPolicy = appDetails.OtpLiabilityPolicy
                };
                var otp = await GenerateOtp(model,clientDetails, code: whiteList?.Otp);

              
                _mainActor.Tell(new ProcessOtpRequest(otp));

                await Task.CompletedTask;

                var data = new GenerateOtpResponse
                {
                    OtpPrefix = otp.Prefix,
                    RequestId = otp.RequestId,
                };

                return CommonResponses.SuccessResponse.OkResponse(data);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Customer {MobileNumber}, An error occured generating otp. Error {ErrorMessage}",
                    model.PhoneNumber, e.Message);
                return CommonResponses.ErrorResponse.FailedDependencyErrorResponse<GenerateOtpResponse>();
            }
        }

        private async Task<Otp> GenerateOtp(CreateOtpRequest model, OtpReceiverDetails clientDetails, int otpLength = 4,
            string code = null)
        {
            var generatedCode = code ?? PinGenerator.Generate(otpLength, PinGenerator.PasswordCharsNumeric);
            var prefix = PinGenerator.Generate(otpLength, PinGenerator.PasswordCharsAlpha).ToUpper();
            
            var otp = new Otp
            {
                OtpCode = $"{prefix}-{generatedCode}",
                Code = generatedCode,
                Prefix = prefix,
                CreatedAt = DateTime.UtcNow,
                RequestId = Guid.NewGuid().ToString("N"),
                Msisdn = model.PhoneNumber,
                SenderId = clientDetails.SenderId,
                CountryCode = model.CountryCode,
                OtpType = "normal",
                OtpLiabilityPolicy=clientDetails.OtpLiabilityPolicy,
                AuthenticationChannel= model.AuthenticationChannel
            };

            var score = otp.Msisdn.StringToInt(true);
            _ = _otpCacheRepository.SortedSetRemoveRangeByScore(CommonConstants.RedisOtpKey, score, score);
            _ = await _otpCacheRepository.AddToSortedSet(CommonConstants.RedisOtpKey, score, otp);
            return otp;
        }

        public async Task<ApiResponse<GenerateOtpResponse>> SendWhatsAppOtpAsync(GenerateOtpDto model, string senderId)
        {
            try
            {
                if (await _memoryCacheService.SetContainsAsync("ConsumerBlackListQS", model.PhoneNumber))
                {
                    _logger.LogWarning("phone number {PhoneNumber} is blacklisted", model.PhoneNumber);
                    return CommonResponses.ErrorResponse.BadRequestResponse<GenerateOtpResponse>(
                        "There seem to be a problem with this account. Contact <EMAIL> for help");
                }
                var otpClientDetails = new OtpReceiverDetails
                {
                    SenderId = senderId,
                    OtpLiabilityPolicy = ""
                };
                var otpRequest = new CreateOtpRequest
                {
                    PhoneNumber = model.PhoneNumber,
                    AppId = model.AppId,
                    CountryCode = model.CountryCode,
                    AuthenticationChannel = AuthenticationChannel.WhatsApp
                };
                // generate code and prefix
                var otp = await GenerateOtp(otpRequest ,otpClientDetails);


                await _whatsAppApi.SendMessage(new WhatsAppMessage
                {
                    To = otp.Msisdn,
                    Content = otp.Code,
                    ExtraData = new Dictionary<string, string>
                    {
                        { WhatsAppExtraData.Properties.Type, WhatsAppExtraData.Values.Type.Otp },
                        { WhatsAppExtraData.Properties.SenderId, otp.SenderId }
                    }
                });

                var data = new GenerateOtpResponse
                {
                    OtpPrefix = otp.Prefix,
                    RequestId = otp.RequestId,
                    
                };

                return CommonResponses.SuccessResponse.OkResponse(data);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Customer {MobileNumber}, An error occured generating otp. Error {ErrorMessage}",
                    model.PhoneNumber, e.Message);
                return CommonResponses.ErrorResponse.FailedDependencyErrorResponse<GenerateOtpResponse>();
            }
        }

        public async Task<ApiResponse<GenerateOtpResponse>> ResendWhatsAppOtpAsync(ResendOtpDto model)
        {
            var (isValid, response, otp) = await ValidateOtpData(model);
            if (!isValid)
            {
                return response;
            }

            await _whatsAppApi.SendMessage(new WhatsAppMessage
            {
                To = otp.Msisdn,
                Content = otp.Code,
                ExtraData = new Dictionary<string, string>
                {
                    { WhatsAppExtraData.Properties.Type, WhatsAppExtraData.Values.Type.Otp },
                    { WhatsAppExtraData.Properties.SenderId, otp.SenderId }
                }
            });

            var data = new GenerateOtpResponse
            {
                OtpPrefix = otp.Prefix,
                RequestId = otp.RequestId,
            };

            return CommonResponses.SuccessResponse.OkResponse(data);
        }

        /// <summary>
        /// Verify the OTP asynchronously.
        /// </summary>
        /// <param name="model">The VerifyOtpDtoWebCheckout model.</param>
        /// <returns>The response containing the result of OTP verification.</returns>
        public async Task<ApiResponse<VerifyOtpResponse>> VerifyOtp2Async(VerifyOtpDtoWebCheckout model)
        {
            try
            {
                var score = model.PhoneNumber.StringToInt(true);

                var otplist =
                    (await _otpCacheRepository.FindInSortedSet<Otp>(CommonConstants.RedisOtpKey, score, score))
                    .ToList();

                var otp = otplist.Find(t => (DateTime.UtcNow - t.CreatedAt).TotalMinutes < 5);
                if (otp == null)
                {
                    _logger.LogWarning("OtpExpired for {ModelPhoneNumber}", model.PhoneNumber);

                    return CommonResponses.ErrorResponse.BadRequestResponse<VerifyOtpResponse>(AppResponses
                        .InvalidRequestId);
                }

                if (otp.IsVerified)
                {
                    _logger.LogWarning("Verified for {ModelPhoneNumber}", model.PhoneNumber);

                    return CommonResponses.ErrorResponse.ForbiddenResponse<VerifyOtpResponse>("OTP already verified");
                }

                if (otp.RequestId != model.RequestId)
                {
                    otp.OTPUseCount++;
                    await _otpCacheRepository.RemoveFromSortedSet(CommonConstants.RedisOtpKey, score);
                    await _otpCacheRepository.AddToSortedSet(CommonConstants.RedisOtpKey, score, otp);
                    return CommonResponses.ErrorResponse.BadRequestResponse<VerifyOtpResponse>(AppResponses
                        .InvalidRequestId);
                }

                if (otp.OTPUseCount >= 3)
                {
                    await _cache.RemoveAsync(model.PhoneNumber);
                    return CommonResponses.ErrorResponse.ForbiddenResponse<VerifyOtpResponse>(AppResponses
                        .OtpAttemptsExceed);
                }

                if (!otp.Msisdn.Equals(model.PhoneNumber))
                {
                    return CommonResponses.ErrorResponse.BadRequestResponse<VerifyOtpResponse>(AppResponses
                        .InvalidMobileNumber);
                }

                if (!otp.OtpCode.Equals(model.OtpCode))
                {
                    otp.OTPUseCount++;

                    await _otpCacheRepository.RemoveFromSortedSet(CommonConstants.RedisOtpKey, score);

                    await _otpCacheRepository.AddToSortedSet(CommonConstants.RedisOtpKey, score, otp);

                    return CommonResponses.ErrorResponse.BadRequestResponse<VerifyOtpResponse>(AppResponses.InvalidOtp);
                }


                await _otpCacheRepository.RemoveFromSortedSet(CommonConstants.RedisOtpKey, score);
                otp.VerifiedAt = DateTime.UtcNow;
                otp.IsVerified = true;
                await _otpCacheRepository.AddToSortedSet(CommonConstants.RedisOtpKey, score, otp);
                var clientApp = await _accountLookupService.ClientAppDetails(model.AppId);
                if (clientApp == null || string.IsNullOrWhiteSpace(clientApp.AccountLookUpUrl))
                {
                    return CommonResponses.ErrorResponse.BadRequestResponse<VerifyOtpResponse>(AppResponses
                        .AppMayNotBeConfigured);
                }

                var lookupCustomerInformation =
                    await _accountLookupService.LookUpCustomerInfoByPhoneNumber(clientApp.AccountLookUpUrl,
                        model.PhoneNumber);
                if (lookupCustomerInformation == null)
                {
                    return CommonResponses.ErrorResponse.BadRequestResponse<VerifyOtpResponse>(AppResponses
                        .HttpRequestFailed);
                }
                if (int.Parse(lookupCustomerInformation.Code)==StatusCodes.Status403Forbidden)
                {
                    return CommonResponses.ErrorResponse.ForbiddenResponse<VerifyOtpResponse>(lookupCustomerInformation.Message);
                }
                if (int.Parse(lookupCustomerInformation.Code) == StatusCodes.Status500InternalServerError)
                {
                    return CommonResponses.ErrorResponse.InternalServerErrorResponse<VerifyOtpResponse>();
                }
                var auth = _jwtService.GenerateAppToken(new TokenGenerationModel { AccountLookupData = lookupCustomerInformation.Data, AuthenticationConfiguration = clientApp });

                var data1 = JsonConvert.DeserializeObject<VerifyOtpResponse>(JsonConvert.SerializeObject(auth.Data));
                return CommonResponses.SuccessResponse.OkResponse(data1);
            }
            catch (Exception e)
            {
                _logger.LogError(e, CustomerErrorLogMessage,
                    model.PhoneNumber, e.Message);
                return CommonResponses.ErrorResponse.FailedDependencyErrorResponse<VerifyOtpResponse>();
            }
        }

        public async Task<ApiResponse<VerifyOtpResponse>> VerifyWhatsAppOtpAsync(VerifyOtpDto model)
        {
            try
            {
                model.OtpCode = $"{model.Prefix}-{model.OtpCode}";

                var score = model.PhoneNumber.StringToInt(true);

                var otplist =
                    (await _otpCacheRepository.FindInSortedSet<Otp>(CommonConstants.RedisOtpKey, score, score))
                    .ToList();

                var otp = otplist.Find(t => (DateTime.UtcNow - t.CreatedAt).TotalMinutes < 5);
                if (otp == null)
                {
                    _logger.LogWarning("OtpExpired for {ModelPhoneNumber}", model.PhoneNumber);

                    return CommonResponses.ErrorResponse.BadRequestResponse<VerifyOtpResponse>(AppResponses
                        .InvalidRequestId);
                }

                if (otp.IsVerified)
                {
                    _logger.LogWarning("Verified for {ModelPhoneNumber}", model.PhoneNumber);

                    return CommonResponses.ErrorResponse.ForbiddenResponse<VerifyOtpResponse>("OTP already verified");
                }

                if (otp.RequestId != model.RequestId)
                {
                    otp.OTPUseCount++;
                    await _otpCacheRepository.RemoveFromSortedSet(CommonConstants.RedisOtpKey, score);
                    await _otpCacheRepository.AddToSortedSet(CommonConstants.RedisOtpKey, score, otp);
                    return CommonResponses.ErrorResponse.BadRequestResponse<VerifyOtpResponse>(AppResponses
                        .InvalidRequestId);
                }

                if (otp.OTPUseCount >= 3)
                {
                    await _cache.RemoveAsync(model.PhoneNumber);
                    return CommonResponses.ErrorResponse.ForbiddenResponse<VerifyOtpResponse>(AppResponses
                        .OtpAttemptsExceed);
                }

                if (!otp.Msisdn.Equals(model.PhoneNumber))
                {
                    return CommonResponses.ErrorResponse.BadRequestResponse<VerifyOtpResponse>(AppResponses
                        .InvalidMobileNumber);
                }

                if (!otp.OtpCode.Equals(model.OtpCode))
                {
                    otp.OTPUseCount++;

                    await _otpCacheRepository.RemoveFromSortedSet(CommonConstants.RedisOtpKey, score);

                    await _otpCacheRepository.AddToSortedSet(CommonConstants.RedisOtpKey, score, otp);

                    return CommonResponses.ErrorResponse.BadRequestResponse<VerifyOtpResponse>(AppResponses.InvalidOtp);
                }


                await _otpCacheRepository.RemoveFromSortedSet(CommonConstants.RedisOtpKey, score);
                otp.VerifiedAt = DateTime.UtcNow;
                otp.IsVerified = true;
                await _otpCacheRepository.AddToSortedSet(CommonConstants.RedisOtpKey, score, otp);
                var clientApp = await _accountLookupService.ClientAppDetails(model.AppId);
                if (clientApp == null || string.IsNullOrWhiteSpace(clientApp.AccountLookUpUrl))
                {
                    return CommonResponses.ErrorResponse.BadRequestResponse<VerifyOtpResponse>(AppResponses
                        .AppMayNotBeConfigured);
                }

                var accountDetails =
                    await _accountLookupService.LookUpCustomerInfoByPhoneNumber(clientApp.AccountLookUpUrl,
                        model.PhoneNumber);
                if (accountDetails == null)
                {
                    return CommonResponses.ErrorResponse.BadRequestResponse<VerifyOtpResponse>(AppResponses
                        .HttpRequestFailed);
                }
                if (int.Parse(accountDetails.Code)==StatusCodes.Status403Forbidden)
                {
                    return CommonResponses.ErrorResponse.ForbiddenResponse<VerifyOtpResponse>(accountDetails.Message);
                }
                if (int.Parse(accountDetails.Code) == StatusCodes.Status500InternalServerError)
                {
                    return CommonResponses.ErrorResponse.InternalServerErrorResponse<VerifyOtpResponse>();
                }
                var auth = _jwtService.GenerateAppToken(new TokenGenerationModel { AccountLookupData = accountDetails.Data, AuthenticationConfiguration = clientApp });
                if (!auth.IsSuccessful)
                {
                    _logger.LogError(CustomerErrorLogMessage,
                    model.PhoneNumber, auth.Message);
                    return CommonResponses.ErrorResponse.FailedDependencyErrorResponse<VerifyOtpResponse>();
                }
                var data1 = JsonConvert.DeserializeObject<VerifyOtpResponse>(JsonConvert.SerializeObject(auth.Data));
                return CommonResponses.SuccessResponse.OkResponse(data1);
            }
            catch (Exception e)
            {
                _logger.LogError(e, CustomerErrorLogMessage,
                    model.PhoneNumber, e.Message);
                return CommonResponses.ErrorResponse.FailedDependencyErrorResponse<VerifyOtpResponse>();
            }
        }

        /// <summary>
        /// Verify the OTP code asynchronously.
        /// </summary>
        /// <param name="model">The VerifyOtpDto model containing the OTP details.</param>
        /// <returns>An ApiResponse object containing the verification response.</returns>
        public async Task<ApiResponse<VerifyOtpResponse>> VerifyOtpAsync(VerifyOtpDto model)
        {
            try
            {
                model.OtpCode = $"{model.Prefix}-{model.OtpCode}";

                var score = model.PhoneNumber.StringToInt(true);

                var otplist =
                    (await _otpCacheRepository.FindInSortedSet<Otp>(CommonConstants.RedisOtpKey, score, score))
                    .ToList();
                var otp = otplist.Find(t => (DateTime.UtcNow - t.CreatedAt).TotalMinutes < 5);
                if (otp == null)
                {
                    _logger.LogWarning("OtpExpired for {ModelPhoneNumber}", model.PhoneNumber);

                    return CommonResponses.ErrorResponse.BadRequestResponse<VerifyOtpResponse>(AppResponses
                        .InvalidRequestId);
                }

                if (otp.IsVerified)
                {
                    _logger.LogWarning("Verified for {ModelPhoneNumber}", model.PhoneNumber);

                    return CommonResponses.ErrorResponse.ForbiddenResponse<VerifyOtpResponse>("OTP already verified");
                }

                if (otp.RequestId != model.RequestId)
                {
                    otp.OTPUseCount++;
                    await _otpCacheRepository.RemoveFromSortedSet(CommonConstants.RedisOtpKey, score);
                    await _otpCacheRepository.AddToSortedSet(CommonConstants.RedisOtpKey, score, otp);
                    return CommonResponses.ErrorResponse.BadRequestResponse<VerifyOtpResponse>(AppResponses
                        .InvalidRequestId);
                }

                if (otp.OTPUseCount >= 3)
                {
                    await _cache.RemoveAsync(model.PhoneNumber);
                    return CommonResponses.ErrorResponse.ForbiddenResponse<VerifyOtpResponse>(AppResponses
                        .OtpAttemptsExceed);
                }

                if (!otp.Msisdn.Equals(model.PhoneNumber))
                {
                    return CommonResponses.ErrorResponse.BadRequestResponse<VerifyOtpResponse>(AppResponses
                        .InvalidMobileNumber);
                }

                if (!otp.OtpCode.Equals(model.OtpCode))
                {
                    otp.OTPUseCount++;

                    await _otpCacheRepository.RemoveFromSortedSet(CommonConstants.RedisOtpKey, score);

                    await _otpCacheRepository.AddToSortedSet(CommonConstants.RedisOtpKey, score, otp);

                    return CommonResponses.ErrorResponse.BadRequestResponse<VerifyOtpResponse>(AppResponses.InvalidOtp);
                }


                await _otpCacheRepository.RemoveFromSortedSet(CommonConstants.RedisOtpKey, score);
                otp.VerifiedAt = DateTime.UtcNow;
                otp.IsVerified = true;
                await _otpCacheRepository.AddToSortedSet(CommonConstants.RedisOtpKey, score, otp);
                var clientApp = await _accountLookupService.ClientAppDetails(model.AppId);
                if (clientApp == null || string.IsNullOrWhiteSpace(clientApp.AccountLookUpUrl))
                {
                    return CommonResponses.ErrorResponse.BadRequestResponse<VerifyOtpResponse>(AppResponses
                        .AppMayNotBeConfigured);
                }

                var customerInformation =
                    await _accountLookupService.LookUpCustomerInfoByPhoneNumber(clientApp.AccountLookUpUrl,
                        model.PhoneNumber,model.LinkedEmail);
                if (customerInformation == null)
                {
                    return CommonResponses.ErrorResponse.BadRequestResponse<VerifyOtpResponse>(AppResponses
                        .HttpRequestFailed);
                }
                if (int.Parse(customerInformation.Code)==StatusCodes.Status403Forbidden)
                {
                    return CommonResponses.ErrorResponse.ForbiddenResponse<VerifyOtpResponse>(customerInformation
                        .Message);
                }
                if (int.Parse(customerInformation.Code) == StatusCodes.Status500InternalServerError)
                {
                    return CommonResponses.ErrorResponse.InternalServerErrorResponse<VerifyOtpResponse>();
                }
                var auth = _jwtService.GenerateAppToken(new TokenGenerationModel { AccountLookupData = customerInformation.Data, AuthenticationConfiguration = clientApp });
                if (!auth.IsSuccessful)
                {
                    _logger.LogError(CustomerErrorLogMessage,
                    model.PhoneNumber, auth.Message);
                    return CommonResponses.ErrorResponse.FailedDependencyErrorResponse<VerifyOtpResponse>();
                }
                var data1 = JsonConvert.DeserializeObject<VerifyOtpResponse>(JsonConvert.SerializeObject(auth.Data));
                data1!.MobileNumber = model.PhoneNumber;
                return CommonResponses.SuccessResponse.OkResponse(data1);
            }
            catch (Exception e)
            {
                _logger.LogError(e, CustomerErrorLogMessage,
                    model.PhoneNumber, e.Message);
                return CommonResponses.ErrorResponse.FailedDependencyErrorResponse<VerifyOtpResponse>();
            }
        }

        /// <summary>
        /// Resends the OTP for the specified model.
        /// </summary>
        /// <param name="model">The model containing the necessary data for resending the OTP.</param>
        /// <returns>An asynchronous task that represents the operation and contains the API response with the generated OTP.</returns>
        public async Task<ApiResponse<GenerateOtpResponse>> ResendOtpAsync(ResendOtpDto model)
        {
            try
            {
                if (await _memoryCacheService.SetContainsAsync("ConsumerBlackListQS", model.PhoneNumber))
                {
                    _logger.LogWarning("phone number {ModelPhoneNumber} is blacklisted", model.PhoneNumber);
                    return CommonResponses.ErrorResponse.BadRequestResponse<GenerateOtpResponse>(
                        "There seem to be a problem with this account. Contact <EMAIL> for help");
                }

                var (isValid, response, otp) = await ValidateOtpData(model);
                if (!isValid)
                {
                    return response;
                }

                _mainActor.Tell(new ProcessOtpRequest(otp));

                await Task.CompletedTask;

                var data = new GenerateOtpResponse
                {
                    OtpPrefix = otp.Prefix,
                    RequestId = otp.RequestId,
                };

                return CommonResponses.SuccessResponse.OkResponse(data, "OTP resent successfully");
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Customer {MobileNumber}, An error occured sending otp. Error {ErrorMessage}",
                    model.PhoneNumber, e.Message);
                return CommonResponses.ErrorResponse.FailedDependencyErrorResponse<GenerateOtpResponse>();
            }
        }

        private async Task<(bool IsValid, ApiResponse<GenerateOtpResponse> Response, Otp Otp)> ValidateOtpData(
            ResendOtpDto model)
        {
            var score = model.PhoneNumber.StringToInt(true);
            var otplist = (await _otpCacheRepository.FindInSortedSet<Otp>(CommonConstants.RedisOtpKey, score, score))
                .ToList();
            var otp = otplist.Find(t => (DateTime.UtcNow - t.CreatedAt).TotalMinutes < 5);

            if (otp == null)
            {
                _logger.LogWarning("OtpExpired for {PhoneNumber}", model.PhoneNumber);
                return (false, CommonResponses.ErrorResponse
                    .BadRequestResponse<GenerateOtpResponse>(AppResponses.InvalidRequestId), null);
            }

            if (otp.RequestId != model.RequestId)
            {
                return (false, CommonResponses.ErrorResponse
                    .BadRequestResponse<GenerateOtpResponse>(AppResponses.InvalidRequestId), null);
            }

            if (!otp.Msisdn.Equals(model.PhoneNumber))
            {
                return (false, CommonResponses.ErrorResponse.BadRequestResponse<GenerateOtpResponse>(AppResponses
                    .InvalidMobileNumber), null);
            }

            if (otp.ResendUseCount > 0)
            {
                return (false, CommonResponses.ErrorResponse.BadRequestResponse<GenerateOtpResponse>(
                    "Cannot Resend Multiple Times"), null);
            }


            otp.ResendUseCount++;
            await _otpCacheRepository.RemoveFromSortedSet(CommonConstants.RedisOtpKey, score);
            await _otpCacheRepository.AddToSortedSet(CommonConstants.RedisOtpKey, score, otp);

            return (true, null, otp);
        }
    }
}