using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Hubtel.Authentication.Commons.Models;
using Hubtel.Authentication.Core.Api.Data.Entities;
using Hubtel.Authentication.Core.Api.Dtos;
using Hubtel.Authentication.Core.Api.Models;
using Microsoft.AspNetCore.JsonPatch;

namespace Hubtel.Authentication.Core.Api.Services;

/// <summary>
/// Represents a service for managing authentication configuration stores.
/// </summary>
public interface IAuthenticationConfigStoreService
{
    /// <summary>
    /// Adds a new authentication configuration store asynchronously.
    /// </summary>
    /// <param name="request">The <see cref="CreateAuthConfigStoreRequest"/> containing the data for the new authentication configuration store.</param>
    /// <returns>
    /// A <see cref="Task{TResult}"/> representing the asynchronous operation.
    /// The task result contains an <see cref="ApiResponse{T}"/> object where T is <see cref="GetAuthConfigStoreResponse"/>
    /// indicating the result of the operation.
    /// </returns>
    Task<ApiResponse<T>> AddAsync<T>(CreateAuthConfigStoreRequest request) where T:AuthConfigStoreResponse;

    /// <summary>
    /// Deletes an item asynchronously.
    /// </summary>
    /// <param name="id">The unique identifier of the item to be deleted.</param>
    /// <returns>A task representing the asynchronous operation. The task result is an ApiResponse bool object indicating whether the deletion was successful or not.</returns>
    Task<ApiResponse<bool>> DeleteAsync(Guid id);

    /// <summary>
    /// Retrieves the authorized config store asynchronously.
    /// </summary>
    /// <param name="filter">The base filter to apply.</param>
    /// <param name="ct">The cancellation token to cancel the operation.</param>
    /// <returns>A task representing the asynchronous operation that returns an ApiResponse containing a PagedResult of GetAuthConfigStoreResponse.</returns>
    Task<ApiResponse<PagedResult<T>>> GetAsync<T>(BaseFilter filter,
        CancellationToken ct = default) where T : AuthConfigStoreResponse;

    /// <summary>
    /// Retrieves the authentication configuration store asynchronously.
    /// </summary>
    /// <param name="id">The ID of the authentication configuration store to retrieve.</param>
    /// <param name="ct">The cancellation token.</param>
    /// <returns>
    /// A Task that represents the asynchronous operation.
    /// The task result contains a GetAuthConfigStoreResponse object representing the authentication configuration store.
    /// </returns>
    Task<T> GetAsync<T>(Guid id, CancellationToken ct = default) where T:AuthConfigStoreResponse;

    /// <summary>
    /// Updates the authentication configuration store for a given ID asynchronously.
    /// </summary>
    /// <param name="id">The ID of the authentication configuration store.</param>
    /// <param name="authConfigStore">The updated authentication configuration store.</param>
    /// <returns>A task representing the asynchronous operation. The task result indicates whether the update was successful or not.</returns>
    Task<(bool IsSaved, T Data)> UpdateAsync<T>(Guid id, UpdateAuthConfigStoreRequest authConfigStore) where T:AuthConfigStoreResponse;
    Task<(bool IsSaved, T Data)> PartialUpdateAsync<T>(Guid id, JsonPatchDocument<AuthenticationConfiguration> patchConfig) where T:AuthConfigStoreResponse;

    /// <summary>
    /// Retrieves the authentication configuration store with the specified ID.
    /// </summary>
    /// <param name="id">The ID of the authentication configuration store to retrieve.</param>
    /// <param name="ct">Cancellation token (optional).</param>
    /// <returns>An ApiResponse object containing the result of the operation.</returns>
    Task<T> GetCachedClientAsync<T>(Guid id, CancellationToken ct = default) where T:AuthConfigStoreResponse;

    Task<ApiResponse<List<AuthenticationChannel>>> GetAllAuthenticationChannelsAsync(Guid id,
        CancellationToken cancellationToken);
    Task<ApiResponse<List<AuthenticationChannel>>> AddAuthenticationChannelsAsync(Guid id,
        List<AuthenticationChannel> selectedAuthChannels, CancellationToken cancellationToken);

    Task<ApiResponse<List<ExternalLoginConfiguration>>> AddExternalLoginConfiguration(Guid id,List<ExternalLoginConfiguration> externalLogins,CancellationToken cancellationToken);
    Task<ApiResponse<List<ExternalLoginConfiguration>>> GetExternalLoginConfigurationAsync(Guid id,
        CancellationToken cancellationToken);

    /// <summary>
    /// Approves the authentication configuration for the specified app ID.
    /// </summary>
    /// <param name="appId">The app ID of the authentication configuration to be approved.</param>
    /// <returns>Returns an ApiResponse object representing the result of the approval operation.The ApiResponse object contains a Boolean value indicating whether the approval was successful.</returns>
    Task<ApiResponse<bool>> ApproveAuthConfiguration(Guid appId);

    /// <summary>
    /// Removes the specified authentication channels for the given user.
    /// </summary>
    /// <param name="id">The ID of the user.</param>
    /// <param name="channels">The list of authentication channels to be removed.</param>
    /// <param name="cancellationToken">A cancellation token that can be used to cancel the operation.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    Task<ApiResponse<bool>> RemoveAuthenticationChannelsAsync(Guid id, List<AuthenticationChannel> channels,
        CancellationToken cancellationToken);

    Task<ApiResponse<bool>> RemoveExternalLoginsAsync(Guid id, List<ExternalLoginConfiguration> externalLogins,
        CancellationToken cancellationToken);

    /// <summary>
    /// Retrieves all pending authentication configuration stores based on the provided filter.
    /// </summary>
    /// <param name="filter">The filter to apply to the retrieval process.</param>
    /// <param name="cancellationToken">The cancellation token to cancel the retrieval process.</param>
    /// <returns>An ApiResponse containing a PagedResult of GetBackOfficeAuthConfigStoreResponse objects.</returns>
    Task<ApiResponse<PagedResult<GetBackOfficeAuthConfigStoreResponse>>> GetAllPendingAsync(BaseFilter filter, CancellationToken cancellationToken);

    /// <summary>
    /// Adds a list of white-listed phone numbers to the authentication configuration store identified by the provided ID.
    /// </summary>
    /// <param name="id">The ID of the authentication configuration store.</param>
    /// <param name="additions">The list of white-listed phone numbers to add.</param>
    /// <param name="cancellationToken">A token to cancel the asynchronous operation if needed.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    Task<ApiResponse<List<WhiteListedNumber>>> AddWhiteListedNumbersToAuthConfigAsync(Guid id,
        List<WhiteListedNumber> additions, CancellationToken cancellationToken);

    /// <summary>
    /// Removes the specified white-listed numbers from the authentication configuration with the given ID.
    /// </summary>
    /// <param name="id">The ID of the authentication configuration.</param>
    /// <param name="removals">The list of white-listed numbers to be removed.</param>
    /// <param name="cancellationToken">The cancellation token to cancel the operation.</param>
    /// <returns>A response indicating if the white-listed numbers were successfully removed.</returns>
    Task<ApiResponse<bool>> RemoveWhiteListedNumbersFromAuthConfigAsync(Guid id, List<string> removals,
        CancellationToken cancellationToken);

    /// <summary>
    /// Retrieves all authentication whitelists associated with a given identifier.
    /// </summary>
    /// <param name="id">The identifier of the authentication configuration.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>An <see cref="ApiResponse{T}"/> containing the list of authentication whitelists.</returns>
    Task<ApiResponse<List<WhiteListedNumber>>> GetAllAuthenticationWhitelistsAsync(Guid id,
        CancellationToken cancellationToken);

    Task<ApiResponse<bool>> UpdateAllowedUrls(Guid id, List<string> allowedUrls,
       CancellationToken cancellationToken);
}