<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Flurl" Version="4.0.0" />
    <PackageReference Include="Flurl.Http" Version="4.0.2" />
	  <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="6.0.2" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="6.0.1" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="6.0.1" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="6.0.1" />
  </ItemGroup>

</Project>
