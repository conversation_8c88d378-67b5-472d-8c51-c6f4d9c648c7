using Hubtel.Authentication.Whatsapp.Sdk.Client;
using Hubtel.Authentication.Whatsapp.Sdk.Options;
using Microsoft.Extensions.DependencyInjection;

namespace Hubtel.Authentication.Whatsapp.Sdk.Extensions
{
    public static class HubtelServiceCollectionsExtensions
    {
        public static IServiceCollection AddHubtelWhatsAppSdk(this IServiceCollection services,
            Action<WhatsAppProxyConfig> configure)
        {
            if (services == null)
            {
                ArgumentNullException.ThrowIfNull(nameof(services));
            }
            var config = new WhatsAppProxyConfig();
            configure(config);

            _ = services.Configure(configure);
            services!.AddSingleton<IWhatsAppProxyApi, WhatsAppProxyApi>();
            return services!;
        }
    }
}