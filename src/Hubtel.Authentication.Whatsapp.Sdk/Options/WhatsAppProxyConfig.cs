namespace Hubtel.Authentication.Whatsapp.Sdk.Options
{
    /// <summary>
    /// Represents the configuration settings for the WhatsApp proxy.
    /// </summary>
    public class WhatsAppProxyConfig
    {
        /// <summary>
        /// Gets or sets the authorisation key.
        /// </summary>
        /// <remarks>
        /// This property is used to store the authorisation key for accessing your Whatsapp client.
        /// </remarks>
        /// <value>
        /// The authorisation key.
        /// </value>
        public string AuthorisationKey { get; set; } = null!;

        /// <summary>
        /// Gets or sets the base URL used for the whatsapp Api.
        /// </summary>
        /// <value>
        /// The base URL for the application.
        /// </value>
        public string BaseUrl { get; set; } = null!;

        /// <summary>
        /// Gets or sets the callback URL  for the whatsapp Api.
        /// </summary>
        /// <value>
        /// The callback URL for the application.
        /// </value>
        public string CallBackUrl { get; set; } = null!;
        /// <summary>
        /// Gets or sets the Client Id  for the whatsapp Api.
        /// </summary>
        /// <value>
        /// The Client Id for the application.
        /// </value>
        public string ClientId { get; set; } = null!;

        /// <summary>
        /// Gets or sets the Client secret  for the whatsapp Api.
        /// </summary>
        /// <value>
        /// The Client secret for the application.
        /// </value>
        public string ClientSecret { get; set; } = null!;
    }
}