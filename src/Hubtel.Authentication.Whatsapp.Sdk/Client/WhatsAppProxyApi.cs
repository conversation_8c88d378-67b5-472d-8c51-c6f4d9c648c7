using System.Diagnostics;
using System.Net;
using System.Text.Json;
using Flurl;
using Flurl.Http;
using Hubtel.Authentication.Whatsapp.Sdk.Models;
using Hubtel.Authentication.Whatsapp.Sdk.Options;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Hubtel.Authentication.Whatsapp.Sdk.Client
{
    /// <summary>
    /// Implementation of the IWhatsAppProxyApi interface for sending WhatsApp messages through a proxy server.
    /// </summary>
    public class WhatsAppProxyApi : IWhatsAppProxyApi
    {
        /// <summary>
        /// The logger used for capturing log messages in the WhatsAppProxyApi class.
        /// </summary>
        private readonly ILogger<WhatsAppProxyApi> _logger;

        /// <summary>
        /// Represents the WhatsApp proxy configuration.
        /// </summary>
        private readonly WhatsAppProxyConfig _proxyConfig;

        /// <summary>
        /// Represents the WhatsAppProxyApi class. </summary> <remarks>
        /// This class provides an API for interacting with the WhatsApp proxy. It takes in a configuration object
        /// and a logger object through dependency injection. </remarks>
        /// /
        public WhatsAppProxyApi(IOptions<WhatsAppProxyConfig> proxyConfig,ILogger<WhatsAppProxyApi>logger)
        {
            _logger = logger;
            _proxyConfig = proxyConfig.Value;
        }

        /// <summary>
        /// Sends a WhatsApp message using the specified <paramref name="message"/>.
        /// </summary>
        /// <param name="message">The WhatsApp message to send.</param>
        /// <returns>
        /// The response from the message sending operation, represented as a tuple containing the following items:
        /// - A boolean indicating if the message was sent successfully.
        /// - An optional <see cref="WhatsAppApiResponse"/> object representing the WhatsApp API response.
        /// - A string containing the error message (if any).
        /// </returns>
        /// <remarks>
        /// If the message sending operation is successful, the <see cref="WhatsAppApiResponse"/> object will be populated with the response from the WhatsApp API.
        /// If the message sending operation fails with a BadRequest error, the error message will be returned in the third item of the tuple.
        /// If an exception occurs while sending the message, the exception message will be returned in the third item of the tuple.
        /// </remarks>
        /// <seealso cref="WhatsAppMessage"/>
        /// <seealso cref="WhatsAppApiResponse"/>
        public async Task<(bool SuccesResponse, WhatsAppApiResponse? WhatsappApiResponse, string Error)> SendMessage(WhatsAppMessage message)
        {
            IFlurlResponse flurlResponse;
            if (string.IsNullOrWhiteSpace(message.CallbackUrl))
            {
                message.CallbackUrl = _proxyConfig.CallBackUrl;
            }

            try
            {
                 message.To = message.To.StartsWith("+", StringComparison.CurrentCulture) ? message.To : $"+{message.To}";
                flurlResponse = await _proxyConfig.BaseUrl.AppendPathSegments("messages", "send")
                    .WithHeader("Accept", "application/json")
                    .WithBasicAuth(_proxyConfig.ClientId,_proxyConfig.ClientSecret)
                    .AllowHttpStatus(StatusCodes.Status400BadRequest)
                    .PostJsonAsync(message);
                Activity.Current?.AddTag("recipient.phone_number", message.To);
                var content = await flurlResponse.ResponseMessage.Content.ReadAsStringAsync();
                if (flurlResponse.ResponseMessage.IsSuccessStatusCode)
                {
                    var whatsappApiResponse = JsonSerializer.Deserialize<WhatsAppApiResponse>(content);
                    _logger.LogInformation("WhatsApp message sent successfully with to:{RecipientNumber}", message.To);
                    return (true, whatsappApiResponse,string.Empty);
                }
                else if (flurlResponse.ResponseMessage.StatusCode == HttpStatusCode.BadRequest)
                {
                    var errorDetails = await flurlResponse.ResponseMessage.Content.ReadAsStringAsync();
                    _logger.LogError("Message sending failed with BadRequest Error Details: {ErrorDetails}",JsonSerializer.Serialize(errorDetails));

                    return (false, null,errorDetails);
                }
            }
            catch (Exception e)
            {
                _logger.LogError(e, "An exception occurred while sending the message");

                return (false, null,e.Message);

            }

            return (false, null, null)!;
        }
    }
}