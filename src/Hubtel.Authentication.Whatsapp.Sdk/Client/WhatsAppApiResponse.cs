using System.Text.Json.Serialization;

namespace Hubtel.Authentication.Whatsapp.Sdk.Client;

public class WhatsAppApiResponse
{
    [JsonPropertyName("rate")] public double Rate { get; set; }
    [JsonPropertyName("messageId")] public string MessageId { get; set; } = null!;
    [JsonPropertyName("status")] public int Status { get; set; }
    [JsonPropertyName("networkId")] public string NetworkId { get; set; } = null!;
    [JsonPropertyName("clientReference")] public string ClientReference { get; set; } = null!;

    [JsonPropertyName("statusDescription")]
    public string StatusDescription { get; set; } = null!;
}
