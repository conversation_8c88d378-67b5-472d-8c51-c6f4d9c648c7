using System.Text.Json.Serialization;

namespace Hubtel.Authentication.Whatsapp.Sdk.Models
{
    /// <summary>
    /// Represents a WhatsApp message.
    /// </summary>
    public class WhatsAppMessage
    {
        public WhatsAppMessage()
        {
            ExtraData = new Dictionary<string, string>();
        }

        [JsonPropertyName("From")] public string From { get; set; } = "Hubtel";
        [JsonPropertyName("To")] public string To { get; set; } = null!;

        [JsonPropertyName("Content")] public string Content { get; set; } = null!;

        [JsonPropertyName("channel")] public string Channel { get; set; } = "whatsapp";

        [JsonPropertyName("callbackUrl")] public string CallbackUrl { get; set; } = null!;

        [JsonPropertyName("extraData")] public Dictionary<string, string> ExtraData { get; set; }
    }
}