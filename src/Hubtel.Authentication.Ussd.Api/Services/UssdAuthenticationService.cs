using System;
using System.Linq;
using System.Threading.Tasks;
using Flurl;
using Flurl.Http;
using Hubtel.Authentication.Commons.Extensions;
using Hubtel.Authentication.Commons.Models;
using Hubtel.Authentication.Commons.Services;
using Hubtel.Authentication.Ussd.Api.Models;
using Hubtel.Authentication.Ussd.Api.Options;
using Hubtel.Redis.Sdk.Services;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using StackExchange.Redis;

namespace Hubtel.Authentication.Ussd.Api.Services;

public class UssdAuthenticationService : IUssdAuthenticationService
{
    private readonly ILogger<UssdAuthenticationService> _logger;
    private readonly INotificationService _notificationService;
    private readonly FraudulentEmailsConfig _fraudulentEmailsConfig;
    private readonly Endpoints _externalEndpoints;
    private readonly IDatabase _otpCacheRepository;

    public UssdAuthenticationService(ILogger<UssdAuthenticationService> logger, IMultiRedisHostCacheRepository multiRedisHostCacheRepository, 
        IOptions<Endpoints> externalEndpoints, INotificationService notificationService, IOptions<FraudulentEmailsConfig> fraudulentEmailsConfig)
    {
        _logger = logger;
        _notificationService = notificationService;
        _fraudulentEmailsConfig = fraudulentEmailsConfig.Value;
        _externalEndpoints = externalEndpoints.Value;
        _otpCacheRepository = multiRedisHostCacheRepository.GetDb(RedisConstants.MainRedisHostName, RedisConstants.DefaultOtpCacheDb);
    }
    
    public async Task<ExtendedOtp> FindLoginAttempt(string phoneNumber)
    {
        var score = phoneNumber.StringToInt(true);
        
        var otplist = (await _otpCacheRepository.FindInSortedSet<ExtendedOtp>(CommonConstants.RedisOtpKey, score, score)).ToList();

        var otp = otplist
            .OrderByDescending(x => x.CreatedAt)
            .FirstOrDefault(t => (DateTime.UtcNow - t.CreatedAt).TotalMinutes < 5);
        
        return otp ?? null;
    }

    public async Task<(bool isValid, string requestId)> ValidateUserOtp(string requestMobile, string userEnteredOtp)
    {
        var otp = await FindLoginAttempt(requestMobile);

        return otp != null ? (otp.OtpCode.Equals($"{otp.Prefix}-{userEnteredOtp}"), otp.RequestId) : (false, string.Empty);
    }

    public async Task StoreSuccessfulLogin(string requestMobile, string requestId)
    { 
        await _otpCacheRepository.StringSetAsync($"{CommonConstants.UssdVerified}:{requestMobile}:{requestId}", requestMobile,
            TimeSpan.FromMinutes(5));
    }

    public async Task PostSuccessfulCallback(string requestMobile, string requestId)
    {
        try
        {
            var otp = await FindLoginAttempt(requestMobile);
            
            // Send POST request with Basic Authentication and get the response
            var serverResponse = await _externalEndpoints.AuthenticationAPIUrl.AppendPathSegment("ussd/onsuccess")
                .WithBasicAuth(_externalEndpoints.AuthenticationAPIKey, _externalEndpoints.AuthenticationAPISecret)
                .PostJsonAsync(new { phoneNumber = requestMobile, otp.AppId, requestId });
                
            var rawResponse = await serverResponse.ResponseMessage.Content.ReadAsStringAsync();
                
            _logger.LogDebug("raw post response={rawResponse}",
                JsonConvert.SerializeObject(rawResponse, Formatting.Indented));
                
            var response = JsonConvert.DeserializeObject<ApiResponse<EmptyDto>>(rawResponse);
                

            // Handle the response as needed
            _logger.LogDebug("Response from successful API call {Response}", JsonConvert.SerializeObject(response, Formatting.Indented));
        }
        catch (FlurlHttpException ex)
        {
            // Handle exceptions (e.g., HTTP status codes other than 2xx)
            _logger.LogError(ex, "Error sending success response");
        }
    }

    public async Task PostFailedCallback(string requestMobile)
    {
        try
        {
            // Send POST request with Basic Authentication and get the response
            var response = await _externalEndpoints.AuthenticationAPIUrl.AppendPathSegment("ussd/onfailure")
                .WithBasicAuth(_externalEndpoints.AuthenticationAPIKey, _externalEndpoints.AuthenticationAPISecret)
                .PostJsonAsync(new { phoneNumber = requestMobile })
                .ReceiveJson<ApiResponse<EmptyDto>>(); // Adjust ResponseModel to your expected response type

            // Handle the response as needed
            _logger.LogDebug("Response from successful API call {Response}", JsonConvert.SerializeObject(response, Formatting.Indented));
        }
        catch (FlurlHttpException ex)
        {
            // Handle exceptions (e.g., HTTP status codes other than 2xx)
            Console.WriteLine($"Error: {ex.Message}");
            _logger.LogError(ex, "Error sending success response");
        }
    }

    public async Task ReportLogin(string requestMobile)
    {
        try
        {

            var otp = await FindLoginAttempt(requestMobile);

            if (otp == null)
            {
                _logger.LogError("Login attempt not found for user: {Mobile} to report", requestMobile);
                return;
            }

            foreach (var email in _fraudulentEmailsConfig.NotificationEmails)
            {
                await _notificationService.SendNotification(_fraudulentEmailsConfig.Topic, new Notification
                {
                    SenderId = "Hubtel Authentication",
                    Subject = "Fraudulent login attempt",
                    TemplateId = "fraudulent_login_report_email_template",
                    Destination = email,
                    Type = "email",
                    Data = new 
                    {
                        DATE = otp.CreatedAt.ToString("D"),
                        TIME = otp.CreatedAt.ToString("h:mm tt"),
                        LOCATION = otp.Location,
                        DEVICEFINGERPRINT = otp.DeviceName,
                        MOBILENUMBER = otp.Msisdn
                    }
                });
            }

        }
        catch (Exception e)
        {
            _logger.LogError(e, "error sending details");
        }

    }
}