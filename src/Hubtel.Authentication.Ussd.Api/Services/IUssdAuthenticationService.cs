using System.Threading.Tasks;
using Hubtel.Authentication.Commons.Models;

namespace Hubtel.Authentication.Ussd.Api.Services;

public interface IUssdAuthenticationService
{
    Task<ExtendedOtp> FindLoginAttempt(string phoneNumber);
    Task<(bool isValid, string requestId)> ValidateUserOtp(string requestMobile, string userEnteredOtp);
    Task StoreSuccessfulLogin(string requestMobile, string requestId);
    Task PostSuccessfulCallback(string requestMobile, string requestId);
    Task PostFailedCallback(string requestMobile);
    Task ReportLogin(string requestMobile);
}