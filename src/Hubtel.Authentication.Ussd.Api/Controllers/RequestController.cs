using System;
using System.Threading.Tasks;
using Akka.Actor;
using Akka.Hosting;
using Hubtel.ProgrammableServices.Sdk.Core;
using Hubtel.ProgrammableServices.Sdk.Fulfilment;
using Hubtel.ProgrammableServices.Sdk.Models;
using Hubtel.Authentication.Ussd.Api.Actors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Http;
using System.Diagnostics;
using OpenTelemetry.Trace;
using Hubtel.Authentication.Ussd.Api.Controllers.PsControllers;

namespace Hubtel.Authentication.Ussd.Api.Controllers;

[Route("api/requests")]
public class RequestController : Controller
{
    private readonly ILogger<RequestController> _logger;
    private readonly IHubtelProgrammableService _programmableService;
    private readonly IActorRef _fulfilmentActor;

    public RequestController(
        ILogger<RequestController> logger,
        IHubtelProgrammableService programmableService, IRequiredActor<FulfilmentActor> fulfilmentActor)
    {
        _logger = logger;
        _programmableService = programmableService;
        _fulfilmentActor = fulfilmentActor.ActorRef;
    }

    
    [Route("interaction")]
    [HttpPost]
    public async Task<IActionResult> HandleInteraction([FromBody] ProgrammableServiceRequest request)
    {
        var activity = Activity.Current;
        try
        {
            _logger.LogInformation("Starting interaction handling. ActionType {ActionType}", request.GetActionType());
            activity?.SetTag("request.phone_number", request.Mobile);
            activity?.AddTag("request.session_id", request.SessionId);
            activity?.SetTag("interaction.type", request.GetActionType().ToString());
            if (request.GetActionType() == ProgrammableServiceActionType.Query)
            {
                _logger.LogInformation("Executing interaction with name: {ControllerName}.", nameof(SampleInteractionController));
                
                var queryResponse = await _programmableService.ExecuteInteraction(request,
                    nameof(SampleInteractionController), nameof(SampleInteractionController.HandleQuery));
                _logger.LogInformation("Execution finished. Result message: {Message}", queryResponse.Message);
                if (!"Successful".Equals(queryResponse.Message, StringComparison.OrdinalIgnoreCase))
                {
                    _logger.LogWarning("Execution was not successful.");
                    return StatusCode(StatusCodes.Status400BadRequest, queryResponse);
                }

                _logger.LogInformation("Execution was successful.");
                return StatusCode(StatusCodes.Status200OK, queryResponse);
            }

            _logger.LogInformation("Executing interaction with {ControllerName}", nameof(AuthenticationInteractionController));
            var response =
                await _programmableService.ExecuteInteraction(request, nameof(AuthenticationInteractionController));
            _logger.LogInformation("Execution finished.");

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred while handling interaction. ActionType {ActionType}",
                request.GetActionType());
            activity?.RecordException(ex);
            return BadRequest();
        }
    }


    [HttpPost, Route("fulfilment")]
    public async Task<IActionResult> HandleFulfilment([FromBody] ProgrammableServiceFulfilmentRequest request)
    {
        _logger.LogInformation("Starting fulfilment handling.");
        try
        {
            _fulfilmentActor.Tell(new ProcessFulfilmentRequest(request));
            _logger.LogInformation("Fulfilment request sent to actor.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred while sending fulfilment request to actor");
            return BadRequest();
        }


        return Ok();
    }
}