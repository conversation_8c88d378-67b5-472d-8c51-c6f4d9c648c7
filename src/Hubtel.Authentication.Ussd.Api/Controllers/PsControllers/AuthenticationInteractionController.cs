using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Hubtel.Authentication.Ussd.Api.Extensions;
using Hubtel.Authentication.Ussd.Api.Services;
using Hubtel.ProgrammableServices.Sdk.Core;
using Hubtel.ProgrammableServices.Sdk.InteractionElements;
using Hubtel.ProgrammableServices.Sdk.Models;
using Microsoft.Extensions.Logging;

namespace Hubtel.Authentication.Ussd.Api.Controllers.PsControllers;

public class AuthenticationInteractionController : ProgrammableServiceControllerBase
{
    private readonly ILogger<AuthenticationInteractionController> _logger;
    private readonly IUssdAuthenticationService _ussdService;
    private const string OtpRetryCountKey = "otpRetryCount";
    private const int OtpValidationCount = 4; // Number of times otp can be entered;1 for the first time and 3 retries

    public AuthenticationInteractionController(ILogger<AuthenticationInteractionController> logger,
        IUssdAuthenticationService ussdService)
    {
        _logger = logger;
        _ussdService = ussdService;
    }

    [HandleInitiation]
    public async Task<ProgrammableServiceResponse> Start()
    {
        try
        {
            var otpDetails = await _ussdService.FindLoginAttempt(Request.Mobile);

            if (otpDetails == null)
            {
                var errorResponse = "There is no active login session for this number: {number}";

                errorResponse = errorResponse.Replace("{number}", Request.Mobile);

                return await RenderResponse(errorResponse);
            }
            if (otpDetails.AuthenticationChannel.IsApprovedAuthChannel())
            {
                var otpResponse = "Keep your account safe. Do not share your code with anyone. Your code is {code} starting with {prefix}";
                otpResponse = otpResponse.Replace("{code}", otpDetails.Code);
                otpResponse = otpResponse.Replace("{prefix}", otpDetails.Prefix);
                return await RenderResponse(otpResponse);
            }
            var header = "Are you trying to login at {app_name} from {device}{location} at {time}?";

            header = header.Replace("{app_name}", otpDetails.LoginAppName);
            header = header.Replace("{device}", otpDetails.DeviceName);
            header = header.Replace("{location}", string.IsNullOrWhiteSpace(otpDetails.Location) ? string.Empty : $" near {otpDetails.Location}");
            header = header.Replace("{time}", otpDetails.CreatedAt.ToString("h:mm tt"));

            var item1 = "Yes, it's me";
            var item2 = "No, don't allow";

            var menu = Menu.New(header)
                .AddItem(item1, $"{nameof(ValidUserLoginAttempt)}")
                .AddItem(item2, $"{nameof(InvalidUserLoginAttempt)}");

            await DataBag.Set("mobileNumber", Request.Mobile);

            // setup rich ux for web and mobile
            return await RenderMenu(menu, new List<ProgrammableServicesResponseData>
            {
                new ProgrammableServicesResponseData(item1, "1", decimal.Zero),
                new ProgrammableServicesResponseData(item2, "2", decimal.Zero),
            }, null, header, ProgrammableServiceDataTypes.Menu);
        }
        catch (Exception e)
        {
            _logger.LogError(e, e.Message);
            return await Error();
        }
    }

    [HandleResumeSession]
    public async Task<ProgrammableServiceResponse> DoSessionResume(List<ResumeSessionInfo> sessionInfos)
    {
        var header = "Do you want to continue from previous session?";

        var item1 = "YES";
        var item2 = "NO";

        var menu = Menu.New(header)
            .AddItem(item1, sessionInfos.LastOrDefault()?.MethodName, sessionInfos.LastOrDefault()?.ControllerName)
            .AddItem(item2, $"{nameof(Start)}");

        // setup rich ux for web and mobile
        return await RenderMenu(menu, new List<ProgrammableServicesResponseData>
        {
            new ProgrammableServicesResponseData(item1, "1", decimal.Zero),
            new ProgrammableServicesResponseData(item2, "2", decimal.Zero),
        }, null, header, ProgrammableServiceDataTypes.Menu);
    }

    public async Task<ProgrammableServiceResponse> ValidUserLoginAttempt()
    {
        try
        {
            var form = Form.New("", $"{nameof(OtpValidation)}");
            form.AddInput(Input.New("otp", "Enter the 4-digit code displayed on your screen"));
            return await RenderForm(form, null, null, "Enter the 4-digit code displayed on your screen", ProgrammableServiceDataTypes.Input,
                ProgrammableServiceFieldTypes.Number);
        }
        catch (Exception e)
        {
            _logger.LogError(e, e.Message);
            return await Error();
        }
    }

    public async Task<ProgrammableServiceResponse> OtpValidation()
    {
        try
        {
            var userEnteredOtp = FormData["otp"];
            var noOtpRetries = await DataBag.Get(OtpRetryCountKey);
            var displayText = "Incorrect Code. Try again";
            var retryCount = 1;

            if (string.IsNullOrEmpty(noOtpRetries))
            {
                await DataBag.Set(OtpRetryCountKey, "1");
            }
            else
            {
                var retryCountStr = await DataBag.Get(OtpRetryCountKey);
                int.TryParse(retryCountStr, out retryCount);
                retryCount++;
                await DataBag.Set(OtpRetryCountKey, $"{retryCount}");
            }

            var validationResult = await _ussdService.ValidateUserOtp(Request.Mobile, userEnteredOtp);

            if (validationResult.isValid)
            {
                await DataBag.Set($"{Request.Mobile}_{Request.SessionId}", validationResult.requestId);
                return Redirect($"{nameof(OtpValidationSucceeded)}");
            }

            if (retryCount >= OtpValidationCount)
            {
                return Redirect($"{nameof(OtpValidationFailed)}");
            }

            var form = Form.New("", $"{nameof(OtpValidation)}");
            form.AddInput(Input.New("otp", displayText));
            return await RenderForm(form, null, null, displayText, ProgrammableServiceDataTypes.Input,
                ProgrammableServiceFieldTypes.Number);
        }
        catch (Exception e)
        {
            _logger.LogError(e, e.Message);
            return await Error();
        }
    }

    public async Task<ProgrammableServiceResponse> OtpValidationSucceeded()
    {
        //Send success callback
        var requestId = await DataBag.Get($"{Request.Mobile}_{Request.SessionId}");

        _logger.LogInformation("Sending success callback for {MobileNumber} with RequestId: {RequestId}", Request.Mobile, requestId);

        await _ussdService.StoreSuccessfulLogin(Request.Mobile, requestId);
        await _ussdService.PostSuccessfulCallback(Request.Mobile, requestId);

        return await RenderResponse("Login approved. You will be redirected shortly.");
    }

    public async Task<ProgrammableServiceResponse> OtpValidationFailed()
    {
        //Send success callback
        await _ussdService.PostFailedCallback(Request.Mobile);

        return await RenderResponse("Login denied. Invalid code provided.");
    }

    public async Task<ProgrammableServiceResponse> InvalidUserLoginAttempt()
    {
        try
        {
            //Send success callback
            await _ussdService.PostFailedCallback(Request.Mobile);

            var header = "An attempt to login with your number {number} was blocked. Report this";

            header = header.Replace("{number}", "");

            var item1 = "Report";


            var menu = Menu.New(header)
                .AddItem(item1, $"{nameof(ReportLoginAttempt)}");

            await DataBag.Set("mobileNumber", Request.Mobile);

            // setup rich ux for web and mobile
            return await RenderMenu(menu, new List<ProgrammableServicesResponseData>
            {
                new ProgrammableServicesResponseData(item1, "1", decimal.Zero),
            }, null, header, ProgrammableServiceDataTypes.Menu);
        }
        catch (Exception e)
        {
            _logger.LogError(e, e.Message);
            return await Error();
        }
    }

    public async Task<ProgrammableServiceResponse> ReportLoginAttempt()
    {
        //submit login attempt callback
        await _ussdService.ReportLogin(Request.Mobile);

        return await RenderResponse("We will examine the report you submitted for your account. Thank you for notifying us.");
    }

    public async Task<ProgrammableServiceResponse> Exit()
    {
        return await RenderResponse("Thank you!");
    }

    private async Task<ProgrammableServiceResponse> RenderResponse(string response)
    {
        var resp = Render(response, null, null);
        // setup rich ux for web and mobile
        resp.Label = response;
        resp.DataType = ProgrammableServiceDataTypes.Display;
        return await Task.FromResult(resp);
    }

    public async Task<ProgrammableServiceResponse> Error()
    {
        return await RenderResponse("An error occured. Please try again!");
    }

}