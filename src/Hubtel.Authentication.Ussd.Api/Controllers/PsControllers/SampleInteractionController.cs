using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Akka.Actor;
using Akka.Hosting;
using Hubtel.PhoneNumbers.Extensions;
using Hubtel.ProgrammableServices.Sdk.Core;
using Hubtel.ProgrammableServices.Sdk.InteractionElements;
using Hubtel.ProgrammableServices.Sdk.Models;
using Hubtel.Authentication.Ussd.Api.Actors;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Hubtel.Authentication.Ussd.Api.Controllers.PsControllers;

public class SampleInteractionController : ProgrammableServiceControllerBase
{
    private readonly ILogger<SampleInteractionController> _logger;
    private readonly IPhoneNumberParser _phoneNumberParser;
    private readonly IActorRef _storageActor;
    private readonly IConfiguration _configuration;

    public SampleInteractionController(
        ILogger<SampleInteractionController> logger,
        IPhoneNumberParser phoneNumberParser, IRequiredActor<StorageActor> storageActor,
        IConfiguration configuration)
    {
        _logger = logger;
        _phoneNumberParser = phoneNumberParser;
        _storageActor = storageActor.ActorRef;
        _configuration = configuration;

    }

    [HandleInitiation]
    public async Task<ProgrammableServiceResponse> Start()
    {
        try
        {
            if (Request.IsUssd())
            {
                var header = "Buy my awesome product";

                var item1 = "Buy for myself";
                var item2 = "Buy for a different number";

                var menu = Menu.New(header)
                    .AddItem(item1, $"{nameof(EnterAmountForm)}")
                    .AddItem(item2, $"{nameof(RecipientForm)}");

                await DataBag.Set("mobileNumber", Request.Mobile);

                // setup rich ux for web and mobile
                return await RenderMenu(menu, new List<ProgrammableServicesResponseData>
                {
                    new ProgrammableServicesResponseData(item1, "1", decimal.Zero),
                    new ProgrammableServicesResponseData(item2, "2", decimal.Zero),
                }, null, header, ProgrammableServiceDataTypes.Menu);
            }

            return Redirect($"{nameof(RecipientForm)}");
        }
        catch (Exception e)
        {
            _logger.LogError(e, e.Message);
            return await Error();
        }
    }


    [HandleResumeSession]
    public async Task<ProgrammableServiceResponse> DoSessionResume(List<ResumeSessionInfo> sessionInfos)
    {
        var header = "Do you want to continue from previous session?";

        var item1 = "YES";
        var item2 = "NO";

        var menu = Menu.New(header)
            .AddItem(item1, sessionInfos.LastOrDefault()?.MethodName, sessionInfos.LastOrDefault()?.ControllerName)
            .AddItem(item2, $"{nameof(Start)}");

        // setup rich ux for web and mobile
        return await RenderMenu(menu, new List<ProgrammableServicesResponseData>
        {
            new ProgrammableServicesResponseData(item1, "1", decimal.Zero),
            new ProgrammableServicesResponseData(item2, "2", decimal.Zero),
        }, null, header, ProgrammableServiceDataTypes.Menu);
    }


    public async Task<ProgrammableServiceResponse> RecipientForm()
    {
        try
        {
            var form = Form.New("", $"{nameof(ProcessRecipientForm)}");
            form.AddInput(Input.New("mobileNumber", "mobile number"));
            return await RenderForm(form, null, null, "Enter mobile number", ProgrammableServiceDataTypes.Input,
                ProgrammableServiceFieldTypes.Phone, "mobileNumber", true);
        }
        catch (Exception e)
        {
            _logger.LogError(e, e.Message);
            return await Error();
        }
    }

    public async Task<ProgrammableServiceResponse> ProcessRecipientForm()
    {
        try
        {
            var mobileNumber = FormData["mobileNumber"];

            if (string.IsNullOrEmpty(mobileNumber)) // check if the input phone number is invalid
            {
                return Redirect($"{nameof(RecipientForm)}");
            }

            if (!_phoneNumberParser.IsPhoneNumberValid(mobileNumber, "GH", out var correctNumber))
            {
                return await RenderResponse("Mobile number is not valid");
            }

            await DataBag.Set("mobileNumber", mobileNumber);
            return Redirect($"{nameof(EnterAmountForm)}");
        }
        catch (Exception e)
        {
            _logger.LogError(e, e.Message);
            return await Error();
        }
    }

    public async Task<ProgrammableServiceResponse> EnterAmountForm()
    {
        try
        {
            var form = Form.New("", $"{nameof(Confirmation)}");
            form.AddInput(Input.New("amount", "amount"));
            return await RenderForm(form, null, null, "Enter amount", ProgrammableServiceDataTypes.Input,
                ProgrammableServiceFieldTypes.Decimal);
        }
        catch (Exception e)
        {
            _logger.LogError(e, e.Message);
            return await Error();
        }
    }

    public async Task<ProgrammableServiceResponse> Confirmation()
    {
        try
        {
            var mobileNumber = await DataBag.Get("mobileNumber");
            var amountStr = FormData["amount"]; // you may want to validate amount

            if (!decimal.TryParse(amountStr, out var amount))
            {
                return await RenderResponse("Amount is not valid");
            }

            if (amount <= decimal.Zero)
            {
                return await RenderResponse($"Amount is not valid");
            }


            if (amount < 1)
            {
                return await RenderResponse($"Sorry, minimum amount is GHS 1.00");
            }

            if (amount > 100)
            {
                return await RenderResponse($"Sorry, maximum amount is GHS 100.00");
            }


            await DataBag.Set("amount", amount.ToString());

            var header = $"Confirmation\nService: My awesome service for {mobileNumber}\nAmount: {amount}";

            var menu = Menu.New(header)
                .AddItem("Confirm", $"{nameof(ProcessAddToCart)}")
                .AddZeroItem("Cancel", $"{nameof(Exit)}");

            var dataItems = new List<ProgrammableServicesResponseData>
            {
                new ProgrammableServicesResponseData("Confirm", "1"),
                new ProgrammableServicesResponseData("Cancel", "0")
            };

            if (Request.IsUssd())
            {
                return await RenderMenu(menu, dataItems, null, header, ProgrammableServiceDataTypes.Confirm);
            }

            return await ProcessAddToCart();
        }
        catch (Exception e)
        {
            _logger.LogError(e, e.Message);
            return await Error();
        }
    }

    public async Task<ProgrammableServiceResponse> ProcessAddToCart()
    {
        try
        {
            var mobileNumber = await DataBag.Get("mobileNumber");
            var accountId = await DataBag.Get("mobileNumber");
            var rawAmount = await DataBag.Get("amount");
            var rawQuantity = "1";
            var country = "GH";
            decimal amount = decimal.Parse(rawAmount);
            int quantity = int.Parse(rawQuantity);

            //save your data into Storage so that when you get callback from Hubtel, you can 

            //compare and render service
            //for example

            var message = new SaveTransaction(Request.SessionId
                , accountId, Request.ServiceCode, mobileNumber
                , Request.Operator, "my service", country, amount, quantity
                , null
                , _configuration["RedisConfiguration:ServiceKeyPrefix"]);

            _storageActor.Tell(message);

            var cartItem = new ProgrammableServicesResponseCartData($"My awesome service for {mobileNumber}"
                , quantity, amount
                , _configuration["Inventory:ItemId"])
            {
                ServiceData =
                {
                    ["destination"] = mobileNumber, ["amount"] = $"{2m}",
                    ["destination"] = mobileNumber,
                    ["amount"] = rawAmount
                }
            };

            var checkoutMsg =
                $"Please authorize payment for GHs {amount} as my awesome service to {mobileNumber}";


            return AddToCart(checkoutMsg, cartItem);
        }
        catch (Exception e)
        {
            _logger.LogError(e, e.Message);
            return await Error();
        }
    }

    public async Task<ProgrammableServiceResponse> HandleQuery()
    {
        if (string.IsNullOrEmpty(Request.Message))
        {
            return Render("Empty request body", null, null, "Empty request body",
                ProgrammableServiceDataTypes.Display, "");
        }

        try
        {
            var queryParams = QueryHelpers.ParseQuery(Request.Message);

            if (!queryParams.ContainsKey("destination"))
            {
                return Render("Missing destination key and value", null, null, "Missing destination key and value",
                    ProgrammableServiceDataTypes.Display, "");
            }

            var account = queryParams["destination"][0];


            if (string.IsNullOrEmpty(account))
            {
                return Render("Missing destination value", null, null, "Missing destination value",
                    ProgrammableServiceDataTypes.Display, "");
            }

            if (!_phoneNumberParser.IsPhoneNumberValid(account, "GH", out var correctNumber))
            {
                return Render("Invalid destination value", null, null, "Invalid destination value",
                    ProgrammableServiceDataTypes.Display, "");
            }

            //todo: add your logic to present response for a query
            await Task.Delay(0);
            return Render($"Successful",
                new List<ProgrammableServicesResponseData>
                {
                    new ProgrammableServicesResponseData("name", "Felix")
                }, null,
                $"Successful",
                ProgrammableServiceDataTypes.Display, "");
        }
        catch (Exception e)
        {
            _logger.LogError(e, e.Message);
            return Render("Error", null, null, "Empty request body",
                ProgrammableServiceDataTypes.Display, "");
        }
    }

    public async Task<ProgrammableServiceResponse> Exit()
    {
        return await RenderResponse("Thank you!");
    }

    private async Task<ProgrammableServiceResponse> RenderResponse(string response)
    {
        var resp = Render(response, null, null);
        // setup rich ux for web and mobile
        resp.Label = response;
        resp.DataType = ProgrammableServiceDataTypes.Display;
        return await Task.FromResult(resp);
    }

    public async Task<ProgrammableServiceResponse> Error()
    {
        return await RenderResponse("An error occured. Please try again!");
    }
}