{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.AspNetCore.HttpLogging.HttpLoggingMiddleware": "Information"}, "GELF": {"Host": "graylog.hubtel.com", "Port": 12202, "LogSource": "Hubtel.Authentication.Ussd.Api", "Facility": "Hubtel.Authentication.Ussd.Api", "Environment": "Development", "LogLevel": {"Default": "Debug"}}}, "AllowedHosts": "*", "KafkaProducerConfig": {"Hosts": [{"BootstrapServers": "localhost:9092", "Alias": "general-kafka"}]}, "FraudulentEmailsConfig": {"Topic": "hubtel.loyalty.upgrade.movement", "NotificationEmails": ["<EMAIL>", "<EMAIL>"]}, "StatsdConfig": {"Server": "127.0.0.1", "Port": "6379", "Prefix": "mystatsdprefix"}, "ApplicationInsights": {"InstrumentationKey": "tbd"}, "FulfilmentApiEndpoint": {"Url": "https://hubtelprogrammableservices.requestcatcher.com/test"}, "Inventory": {"ItemId": "1234"}, "HttpLoggingConfig": {"LoggingFields": "All"}, "RedisConfiguration": {"TimeInSeconds": "3600", "ServiceKeyPrefix": "hubtel:ps:myservice:", "ServiceApiKeyPrefix": "hubtel:ps:myservice-api:", "Setup": [{"Host": "127.0.0.1", "Name": "localRedis", "Port": "6379", "Databases": [{"alias": "myapidb", "db": 1}]}, {"Host": "127.0.0.1", "Name": "OTPServiceRedis", "Port": "6379", "Databases": [{"alias": "otpcachedb", "db": 1}]}]}, "Endpoints": {"AuthenticationAPIUrl": "http://localhost:5001", "AuthenticationAPIKey": "y573fnt66h473d", "AuthenticationAPISecret": "duib5hft2evh5ryg3g63"}, "OpenTelemetryConfig": {"ServiceName": "hubtel.authentication.ussd.api", "Host": "localhost", "Port": 4317, "Protocol": "http", "ShowConsoleMetrics": false, "ShowConsoleTrace": true}}