
### start service flow
POST http://*************:2090/api/requests/interaction
Content-Type: application/json

{
  "Mobile":"233208074263", 
  "SessionId":"234567ityjhgwe654765utrye",  
  "ServiceCode":"7c920dc49b7a40588c657119ce599044", 
  "Type":"initiation", 
  "Message":"*714#", 
  "Sequence":1,
  "Operator":"mtn",
  "Platform":"USSD"
}

### start service flow
POST http://*************:2090/api/requests/interaction
Content-Type: application/json

{
  "Mobile":"233265134310", 
  "SessionId":"234567ityjhgwe654765utrye",  
  "ServiceCode":"7c920dc49b7a40588c657119ce599044", 
  "Type":"initiation", 
  "Message":"*714#", 
  "Sequence":1,
  "Operator":"mtn",
  "Platform":"USSD"
}

### start service flow
POST http://*************:2090/api/requests/interaction
Content-Type: application/json

{
  "Mobile":"233265134310", 
  "SessionId":"234567ityjhgwe654765utrye",  
  "ServiceCode":"7c920dc49b7a40588c657119ce599044", 
  "Type":"initiation", 
  "Message":"*714#", 
  "Sequence":1,
  "Operator":"mtn",
  "Platform":"USSD"
}



### USSD 
POST http://*************:2090/api/requests/interaction
Content-Type: application/json

{
  "Mobile":"233208074263", 
  "SessionId":"234567ityjhgwe654765utrye",  
  "ServiceCode":"7c920dc49b7a40588c657119ce599044", 
  "Type":"response", 
  "Message":"1", 
  "Sequence":2,
  "Operator":"mtn",
  "Platform":"USSD"
}

### USSD 
POST http://*************:2090/api/requests/interaction
Content-Type: application/json

{
  "Mobile":"233208074263", 
  "SessionId":"234567ityjhgwe654765utrye",  
  "ServiceCode":"7c920dc49b7a40588c657119ce599044", 
  "Type":"response", 
  "Message":"9327", 
  "Sequence":2,
  "Operator":"mtn",
  "Platform":"USSD"
}

### fulfilment after payment 
POST http://*************:2090/api/requests/fulfilment
Content-Type: application/json

{
  "OrderId": "0269f01143a54bb2814fdee99d7ae6c6",
  "SessionId": "234567ityjhgwe654765utrye00000",
  "ExtraData": {},
  "OrderInfo": {
    "CustomerMobileNumber": "************",
    "CustomerName": "Kofi Hubtel",
    "Status": "Paid",
    "Currency": "GHS",
    "BranchName": "Main",
    "IsRecurring": false,
    "RecurringInvoiceId": null,
    "OrderDate": "2023-04-11T14:55:34.2946014Z",
    "Items": [
      {
        "ItemId": "fdd76c884e614b1c8f669a3207b09a98",
        "Name": "ECG Invoice Payment",
        "Quantity": 1,
        "UnitPrice": 1.0
      }
    ],
    "Payment": {
      "PaymentType": "mobilemoney",
      "PaymentDescription": "The MTN Mobile Money payment has been approved and processed successfully.",
      "IsSuccessful": true,
      "AmountPaid": 1.0,
      "PaymentDate": "2023-04-11T14:58:34.2946014Z"
    }
  }
}


### query account
POST http://*************:2090/api/requests/interaction
Content-Type: application/json

{
  "Mobile":"chrome-br", 
  "SessionId":"234567ityjhgwe654765utrye",  
  "ServiceCode":"7c920dc49b7a40588c657119ce599044", 
  "Type":"query",
  "Message":"?destination=************", 
  "Operator":"web-store" 
}


