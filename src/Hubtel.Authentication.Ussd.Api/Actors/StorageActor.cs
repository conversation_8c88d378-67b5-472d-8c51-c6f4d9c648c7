using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Hubtel.Redis.Sdk.Services;
using Hubtel.Authentication.Ussd.Api.Models;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using StackExchange.Redis;

namespace Hubtel.Authentication.Ussd.Api.Actors;

public class StorageActor : BaseActor
{
    private readonly IMultiRedisHostCacheRepository _multiRedisHostCacheRepository;
    private readonly IDatabase _redisCache;
    private readonly IConfiguration _configuration;

    public StorageActor(
        IConfiguration configuration, IMultiRedisHostCacheRepository multiRedisHostCacheRepository)
    {
        _configuration = configuration;
        _multiRedisHostCacheRepository = multiRedisHostCacheRepository;
        _redisCache = _multiRedisHostCacheRepository.GetDb("localRedis", "myapidb");
        ReceiveAsync<SaveTransaction>(DoSaveTransaction);
    }

    private async Task DoSaveTransaction(SaveTransaction message)
    {
        var trx = new Transaction
        {
            SessionId = message.SessionId,
            Destination = message.Mobile,
            UserAgent = message.Mobile,
            Operator = message.UserOperator,
            Status = "addedToCart",
            Country = message.Country,
            Item = message.Item,
            Amount = message.Amount,
            Quantity = message.Quantity,
            CreatedAt = DateTime.UtcNow,
            ExtraData = message.ExtraData
        };

        await _redisCache.StringSetAsync($"{message.KeyPrefix}{trx.SessionId}"
            , JsonConvert.SerializeObject(trx)
            , TimeSpan.FromSeconds(_configuration.GetValue<int>("RedisConfiguration:TimeInSeconds")));
    }
}

public struct SaveTransaction
{
    public string SessionId { get; }
    public string AccountId { get; }
    public string ServiceCode { get; }
    public string Mobile { get; }
    public string UserOperator { get; }
    public string Item { get; }
    public string Country { get; }
    public decimal Amount { get; }
    public int Quantity { get; }
    public Dictionary<string, string> ExtraData { get; }
    public string KeyPrefix { get; }

    public SaveTransaction(string sessionId, string accountId, string serviceCode, string mobile,
        string userOperator, string item, string country, decimal amount, int quantity,
        Dictionary<string, string> extraData = null, string keyPrefix = null)
    {
        SessionId = sessionId;
        AccountId = accountId;
        ServiceCode = serviceCode;
        Mobile = mobile;
        UserOperator = userOperator;
        Item = item;
        Country = country;
        Amount = amount;
        Quantity = quantity;
        ExtraData = extraData;
        KeyPrefix = keyPrefix;
    }
}