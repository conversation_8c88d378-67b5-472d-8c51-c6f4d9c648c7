using System;
using System.Threading.Tasks;
using Hubtel.ProgrammableServices.Sdk.Fulfilment;
using Hubtel.Redis.Sdk.Services;
using Hubtel.Authentication.Ussd.Api.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using StackExchange.Redis;

namespace Hubtel.Authentication.Ussd.Api.Actors;

public class FulfilmentActor : BaseActor
{
    private readonly ILogger<FulfilmentActor> _logger;
    private readonly IProgrammableServicesCallbackApi _callbackApi;
    private readonly IMultiRedisHostCacheRepository _multiRedisHostCacheRepository;
    private readonly IDatabase _redisCache;
    private readonly IConfiguration _configuration;

    public FulfilmentActor(ILogger<FulfilmentActor> logger
        , IProgrammableServicesCallbackApi callbackApi
        , IConfiguration configuration
        , IMultiRedisHostCacheRepository multiRedisHostCacheRepository)
    {
        _logger = logger;
        _callbackApi = callbackApi;

        _configuration = configuration;
        _multiRedisHostCacheRepository = multiRedisHostCacheRepository;
        _redisCache = _multiRedisHostCacheRepository.GetDb("localRedis", "myapidb");
        ReceiveAsync<ProcessFulfilmentRequest>(DoProcessFulfilmentRequest);
    }

    private async Task DoProcessFulfilmentRequest(ProcessFulfilmentRequest message)
    {
        //here's an example of what you should typically do with a fulfilment request
        //fulfilment can be from an interaction or direct (via an external API call)
        _logger.LogDebug($"fulfilling service....");
        Transaction existingTransaction;
        Transaction transaction = null;
        if (!message.Request.IsDirectApiCall()) //comes after an interaction 
        {
            var json = await _redisCache.StringGetAsync(
                $"{_configuration["RedisConfiguration:ServiceKeyPrefix"]}{message.Request.SessionId}");

            if (string.IsNullOrEmpty(json))
            {
                await _callbackApi.ReportServiceFulfilmentStatus(message.Request.OrderId, message.Request.SessionId,
                    message.Request.OrderId, "session not found or has expired", "failed", new
                    {
                    });
                return;
            }

            existingTransaction = JsonConvert.DeserializeObject<Transaction>(json);
            if ("success".Equals(existingTransaction.Status))
            {
                _logger.LogWarning(
                    $"request for fulfilment for sessionId {existingTransaction.SessionId} has already been completed");
                return;
            }

            if ("failed".Equals(existingTransaction.Status))
            {
                _logger.LogWarning(
                    $"request for fulfilment for sessionId {existingTransaction.SessionId} has already been completed");
                return;
            }

            transaction = existingTransaction;
        }
        else //it's a direct fulfilment, perhaps from an external API call
        {
            var json = await _redisCache.StringGetAsync(
                $"{_configuration["RedisConfiguration:ServiceApiKeyPrefix"]}{message.Request.SessionId}");

            if (!string.IsNullOrEmpty(json)) //already handled
            {
                existingTransaction = JsonConvert.DeserializeObject<Transaction>(json);

                if ("success".Equals(existingTransaction.Status))
                {
                    _logger.LogWarning(
                        $"request for fulfilment for sessionId {existingTransaction.SessionId} has already been completed");
                    return;
                }

                if ("failed".Equals(existingTransaction.Status))
                {
                    _logger.LogWarning(
                        $"request for fulfilment for sessionId {existingTransaction.SessionId} has already been completed");
                    return;
                }
            }

            transaction = new Transaction
            {
                Amount = decimal.Parse(message.Request.ExtraData["Amount"]),
                Destination = message.Request.ExtraData["Destination"],
                SessionId = message.Request.OrderId,

                CreatedAt = DateTime.UtcNow,
                UserAgent = "direct-api",
                Quantity = 1,
                Operator = "direct-api",
                Country = "GH",
            };

            //todo: validate other details since this is an API call
        }


        //simulate fulfilment, set final status and others
        await Task.Delay(100);
        var transactionId = Guid.NewGuid().ToString("N");
        transaction.Status = "success";
        transaction.ServiceStatusDescription = "service is fulfilled";
        transaction.ServiceTransactionId = transactionId;
        transaction.UpdatedAt = DateTime.UtcNow;
        transaction.OrderId = message.Request.OrderId;

        //update transaction status 
        if (message.Request.IsDirectApiCall())
        {
            await _redisCache.StringSetAsync(
                $"{_configuration["RedisConfiguration:ServiceApiKeyPrefix"]}{transaction.SessionId}",
                JsonConvert.SerializeObject(transaction));
        }
        else
        {
            await _redisCache.StringSetAsync(
                $"{_configuration["RedisConfiguration:ServiceKeyPrefix"]}{transaction.SessionId}",
                JsonConvert.SerializeObject(transaction));
        }

        //notify hubtel about service fulfilment status

        var resp = await _callbackApi.ReportServiceFulfilmentStatus(transaction.OrderId, transaction.SessionId,
            transactionId, transaction.ServiceStatusDescription, transaction.Status, new
            {
            });
    }
}

public struct ProcessFulfilmentRequest
{
    public ProgrammableServiceFulfilmentRequest Request { get; }

    public ProcessFulfilmentRequest(ProgrammableServiceFulfilmentRequest request)
    {
        Request = request;
    }
}