<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
    </PropertyGroup>

    <ItemGroup>

        <PackageReference Include="Akka.Hosting" Version="********" />

        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.36" />


        <PackageReference Include="Akka" Version="1.5.31" />

        <PackageReference Include="Mapster" Version="7.4.0" />
        <PackageReference Include="JustEat.StatsD" Version="4.2.0" />
        <PackageReference Include="Gelf.Extensions.Logging" Version="2.6.0" />
        <PackageReference Include="StackExchange.Redis" Version="2.8.16" />
        <PackageReference Include="Hubtel.PhoneNumbers" Version="6.1.1" />
        <PackageReference Include="Hubtel.ProgrammableServices.Sdk" Version="6.0.0" />
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="6.36.0" />

    </ItemGroup>

    <ItemGroup>
        <None Update="_tests\interaction_staging.http">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="_tests\interaction.http">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Hubtel.Authentication.Commons\Hubtel.Authentication.Commons.csproj" />
    </ItemGroup>

</Project>
