using System;
using System.Collections.Generic;

namespace Hubtel.Authentication.Ussd.Api.Models;

public class Transaction
{
    public Transaction()
    {
        ExtraData = new Dictionary<string, string>();
        Status = "addedToCart";
    }

    /// <summary>
    /// The primary key to the transactions tables
    /// </summary>

    public long Id { get; set; }

    /// <summary>
    /// The session Id of the user's transaction
    /// </summary>
    public string SessionId { get; set; }

    /// <summary>
    /// Phone Number to be credited with airtime
    /// </summary>
    public string Destination { get; set; }

    /// <summary>
    /// The specific type of device used in accessing the service e.g chrome-version, android-version
    /// ios-version
    /// </summary>
    public string UserAgent { get; set; }

    /// <summary>
    /// The generic type of medium used in accessing chrome, ussd, webstore, 
    /// </summary>
    public string Operator { get; set; }

    /// <summary>
    /// The name of the item to be served e.g 2332489098838
    /// </summary>
    public string Item { get; set; }

    /// <summary>
    /// The country of the service
    /// </summary>
    public string Country { get; set; }

    /// <summary>
    /// The amount of service to be rendered
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// The quantity of the service
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// The order number from sales API
    /// </summary>
    public string OrderId { get; set; }

    /// <summary>
    /// The status of the transaction
    /// addedToCart/pending/success/failed
    /// </summary>
    public string Status { get; set; }

    /// <summary>
    /// The callback payload from sales API
    /// </summary>
    public string CallbackPayload { get; set; }

    /// <summary>
    /// The XML service request to MTN
    /// </summary>
    public string ServiceRequestPayload { get; set; }

    /// <summary>
    /// The XML service response from MTN
    /// </summary>
    public string ServiceResponsePayload { get; set; }

    /// <summary>
    /// The user's airtime balance before the topup
    /// </summary>
    public string UserBalanceBefore { get; set; }

    /// <summary>
    /// The user's airtime balance after the topup
    /// </summary>
    public string UserBalanceAfter { get; set; }

    /// <summary>
    /// The Owner [Hubtel]'s balance before the toptup
    /// </summary>
    public string OwnerBalanceBefore { get; set; }

    /// <summary>
    /// The Owner [Hubtel]'s balance after the toptup
    /// </summary>
    public string OwnerBalanceAfter { get; set; }

    /// <summary>
    /// The service transacrion Id from MTN
    /// </summary>
    public string ServiceTransactionId { get; set; }

    /// <summary>
    /// The service status from MTN
    /// </summary>
    public string ServiceStatus { get; set; }

    /// <summary>
    /// The service status description from MTN
    /// </summary>
    public string ServiceStatusDescription { get; set; }

    /// <summary>
    /// The date and time the service request was created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// The date and time the service request was updated
    /// </summary>
    public DateTime? UpdatedAt { get; set; }

    public Dictionary<string, string> ExtraData { get; set; }
}