using Hubtel.Authentication.Commons.Services;
using Hubtel.PhoneNumbers.Extensions;
using Hubtel.ProgrammableServices.Sdk.Core;
using Hubtel.ProgrammableServices.Sdk.Extensions;
using Hubtel.ProgrammableServices.Sdk.Storage;
using Hubtel.Redis.Sdk.Extensions;
using Hubtel.Redis.Sdk.Options;
using Hubtel.Authentication.Ussd.Api.Extensions;
using Hubtel.Authentication.Ussd.Api.Options;
using Hubtel.Authentication.Ussd.Api.Services;
using Hubtel.Kafka.Producer.Sdk.Extensions;
using Hubtel.Kafka.Producer.Sdk.Options;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Hubtel.OpenTelemetry.Instrumentation.Extensions;

string corsPolicyName = "Hubtel.Templates.ProgrammableServicesBasic.Tmpl.PolicyName";


var builder = WebApplication.CreateBuilder(args);
var services = builder.Services;
var config = builder.Configuration;

services.AddHubtelGelfLogging(config);

services.AddHubtelRedisSdk(c =>
    config.GetSection(nameof(RedisConfiguration)).Bind(c));

services.AddHubtelKafkaProducerSdk(c =>
    config.GetSection(nameof(KafkaProducerConfig)).Bind(c));

var httpLoggingConfiguration = new HttpLoggingConfig();
config.GetSection(nameof(HttpLoggingConfig)).Bind(httpLoggingConfiguration);

services.AddSingleton<IUssdAuthenticationService, UssdAuthenticationService>();
services.Configure<Endpoints>(config.GetSection("Endpoints"));
services.Configure<FraudulentEmailsConfig>(config.GetSection("FraudulentEmailsConfig"));


services.AddNotificationService();
services.AddHubtelPhoneNumberSdk();

services.AddHealthChecks();

services.AddCors(options => options
    .AddPolicy(corsPolicyName, policy => policy
        .AllowAnyOrigin()
        .AllowAnyHeader()
        .AllowAnyMethod()));

services.AddHubtelProgrammableServices((p) => new ProgrammableServiceConfiguration
{
    Storage = new DefaultProgrammableServiceStorage(),
    HubtelFulfilmentApiEndpoint = config["FulfilmentApiEndpoint:Url"],
    EnableResumeSession = false,
});

services.AddHubtelControllers();
services.AddHubtelOpenTelemetry(config);

services.AddHttpLogging(options =>
{
    options.LoggingFields = httpLoggingConfiguration.LoggingFields;
    options.RequestBodyLogLimit = 4096;
    options.ResponseBodyLogLimit = 4096;
});
services.AddHubtelActorSystem();

var app = builder.Build();
app.UseHubtelActorSystem();

app.UseHttpLogging();

if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}

app.UseHttpsRedirection();

app.UseHubtelExceptionHandler(!app.Environment.IsProduction());

app.UseRouting();

app.UseCors(corsPolicyName);

app.UseAuthentication();

app.UseAuthorization();

app.UseEndpoints(endpoints =>
{
    endpoints.MapHealthChecks("/health");
    endpoints.MapControllers();
});

await app.RunAsync();