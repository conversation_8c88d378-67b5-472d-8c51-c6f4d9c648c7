using System;
using Hubtel.ProgrammableServices.Sdk.Models;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Serialization;
using Newtonsoft.Json;
using System.Net;
using Akka.Actor;

namespace Hubtel.Authentication.Ussd.Api.Extensions;

public static class WebApplicationExtensions
{
    public static void UseHubtelActorSystem(this WebApplication app)
    {
        var actorSys = app.Services.GetRequiredService<ActorSystem>();

        _ = actorSys ?? throw new ArgumentNullException(nameof(actorSys));
    }

    public static void UseHubtelExceptionHandler(
        this WebApplication app,
        bool returnStackTrace = false)
    {
        var logger = app.Services.GetRequiredService<ILogger<Program>>();

        app.UseExceptionHandler(appError =>
        {
            appError.Run(async context =>
            {
                context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                context.Response.ContentType = "application/json";

                var contextFeature = context.Features.Get<IExceptionHandlerFeature>();
                if (contextFeature != null)
                {
                    logger.LogError(contextFeature.Error, $"Something went wrong: {contextFeature.Error?.Message}");

                    var respJson = JsonConvert.SerializeObject(new ProgrammableServiceResponse
                    {
                        Message =
                            "Ooops, something really bad happened. Please try again later.",
                    }, new JsonSerializerSettings
                    {
                        ContractResolver = new CamelCasePropertyNamesContractResolver()
                    });

                    context.Response.ContentLength = respJson.Length;

                    await context.Response.WriteAsync(respJson);
                }
            });
        });
    }
}