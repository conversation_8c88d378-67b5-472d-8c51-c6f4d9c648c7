using Hubtel.Authentication.Commons.Models;
using System;
using System.Linq;

namespace Hubtel.Authentication.Ussd.Api.Extensions
{
    public static class ApplicationExtensions
    {
        public static bool IsApprovedAuthChannel(this AuthenticationChannel authChannel)
        {
            string[] authList = { nameof(AuthenticationChannel.Email), nameof(AuthenticationChannel.Sms), nameof(AuthenticationChannel.WhatsApp) };
            return authList.Contains(Enum.GetName(authChannel));
        }
    }
}
