using System;
using System.Collections.Generic;
using System.Reflection;
using System.Text.Json;
using System.Text.RegularExpressions;
using Akka.Actor;
using Akka.Hosting;
using Gelf.Extensions.Logging;
using Hubtel.Authentication.Ussd.Api.Actors;
using JustEat.StatsD;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Hubtel.Authentication.Ussd.Api.Extensions;

public static class ServiceCollectionExtension
{
    public static IServiceCollection AddHubtelActorSystem(
        this IServiceCollection services)
    {
        var actorSystemName = Regex.Replace(Assembly.GetExecutingAssembly().GetName().Name ?? "ActorSystemName",
            @"[^a-zA-Z\s]+", "");
        services.AddAkka(actorSystemName, ((builder) =>
        {
            
            builder.WithActors((system, registry, resolver) =>
            {
                var defaultStrategy= new OneForOneStrategy(
                    3, TimeSpan.FromSeconds(3), ex =>
                    {
                        if (ex is not ActorInitializationException)
                            return Directive.Resume;

                        system?.Terminate().Wait(1000);

                        return Directive.Stop;
                    });
                var fulfilmentActorProps = resolver
                    .Props<FulfilmentActor>()
                    .WithSupervisorStrategy(defaultStrategy);
                var fulfilmentActor = system.ActorOf(fulfilmentActorProps, nameof(FulfilmentActor));
                registry.Register<FulfilmentActor>(fulfilmentActor);


                var storageActorProps = resolver
                    .Props<StorageActor>()
                    .WithSupervisorStrategy(defaultStrategy);
                var storageActor = system.ActorOf(storageActorProps, nameof(StorageActor));
                registry.Register<StorageActor>(storageActor);
            });
        }));


        return services;
    }

    public static IServiceCollection AddHubtelGelfLogging(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        services.AddLogging(loggingBuilder => loggingBuilder.AddGelf(options =>
        {
            options.AdditionalFields = new Dictionary<string, object>
            {
                { "facility", configuration.GetSection("Logging")["GELF:Facility"] },
                { "Environment", configuration.GetSection("Logging")["GELF:Environment"] },
                { "machine_name", Environment.MachineName }
            };
            options.Host = configuration.GetSection("Logging")["GELF:Host"];
            options.LogSource = configuration.GetSection("Logging")["GELF:LogSource"];
            options.Port = int.Parse(configuration.GetSection("Logging")["GELF:Port"]);
        }));
        return services;
    }

    public static IServiceCollection AddHubtelStatsD(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        services.AddStatsD(provider =>
        {
            return new StatsDConfiguration
            {
                Host = configuration["StatsdConfig:Server"],
                Port = int.Parse(configuration["StatsdConfig:Port"]),
                Prefix = configuration["StatsdConfig:Prefix"],
                OnError = ex => true
            };
        });
        return services;
    }


    public static IServiceCollection AddHubtelControllers(this IServiceCollection services)
    {
        services.Configure<RouteOptions>(options => options.LowercaseUrls = true);

        services.AddControllers(options =>
            {
                options.SuppressImplicitRequiredAttributeForNonNullableReferenceTypes = true;
            })
            .AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.WriteIndented = true;
                options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
            });

        return services;
    }
}