using LookUpApi.Dtos;
using Microsoft.AspNetCore.Mvc;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace LookUpApi.Controllers
{
    [ApiController]
    [Route("/lookup")]
    public class LookUpController : ControllerBase
    {
        private readonly ILogger<LookUpController> _logger;
        private static readonly JsonSerializerOptions _jsonSerializerOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true
        };
        public LookUpController(ILogger<LookUpController> logger)
        {
            _logger = logger;
        }
        [HttpGet("email/{email}")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(EmailLookUpResponse))]
        public IActionResult LookupEmail([FromRoute]string email)
        {
            _logger.LogInformation("LookupEmail called with email: {Email}", email);
            HashSet<string> listOfOk = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"];
            HashSet<string> listOfForbidden = ["<EMAIL>"];
            string emailPattern = @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$";
            var regex = new Regex(emailPattern,RegexOptions.None,matchTimeout: TimeSpan.FromSeconds(3));
            if (!regex.IsMatch(email))
            {

                return BadRequest(new { Message = "Invalid Email Address" });
            }
            if (listOfForbidden.Contains(email))
            {
                var fresponse = new EmailLookUpResponse()
                {
                    Code = $"{StatusCodes.Status403Forbidden}",
                    Message = "User is forbidden",
                    Data = null
                };
                return StatusCode(StatusCodes.Status403Forbidden,fresponse);
            }
            if (!listOfOk.Contains(email))
            {
                return NotFound(new { Message = "Invalid Email Address or User does not exist" });
            }
            var responses = new EmailLookUpResponse()
            {
                Code = "200",
                Message = "Success",
                Data = new EmailLookUpData
                {
                    PhoneNumbers = [],
                    TokenData = new Dictionary<string, string>
                    {
                        { "email", email },
                        { "extraData1", "value1" }
                    }

                }
            };
            _logger.LogInformation("LookupEmail response: {Responses}", JsonSerializer.Serialize(responses, _jsonSerializerOptions));
            return Ok(responses);
        }

        [HttpGet("phonenumber/{phoneNumber}")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(PhoneNumberLookUpResponse))]
        public IActionResult LookupPhoneNumber([FromRoute]string phoneNumber,[FromQuery]string? linkedEmail)
        {
            _logger.LogInformation("LookupPhoneNumber called with phonenumber: {PhoneNumber}", phoneNumber);

            if (!string.IsNullOrEmpty(linkedEmail))
            {
                _logger.LogInformation("Email was optionally added");
            }
            var set = new HashSet<string> { "233265134310", "233241591706", "233547469379", "233552590820", "0552590820" };
            if (!set.Contains(phoneNumber))
            {
                return NotFound(new { Message = "Custom my pm not found" });
            }
            var tokenData = new Dictionary<string, string>
            {
                { "mobileNumber", phoneNumber },
                { "extraData2", "value2" },
                { "extraData3", "value3" }
            };
            var responses = new PhoneNumberLookUpResponse()
            {
                Code = "200",
                Message = "Success",
                Data = new AccountLookupData
                {
                    MobileNumber = phoneNumber,
                    Email = "<EMAIL>",
                    TokenData = tokenData
                },
            };
            return Ok(responses);
        }
    }
}
