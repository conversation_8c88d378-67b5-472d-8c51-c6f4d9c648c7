using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace LookUpApi.Dtos
{
    public class EmailLookUpResponse : LookUpResponse
    {
        public EmailLookUpData? Data { get; set; }
    }

    public class EmailLookUpData
    {
        [JsonPropertyName("phoneNumbers")]
        public IEnumerable<PhoneNumber> PhoneNumbers { get; set; } = Enumerable.Empty<PhoneNumber>();
        [JsonPropertyName("tokenData")]
        public Dictionary<string, string> TokenData { get; set; } = new();
    }

    public class VerifyEmailResponse
    {
        public string? Email { get; set; }
        public string? Token { get; set; }
        public string? Expiry { get; set; }
    }
    public abstract class LookUpResponse
    {
        [JsonPropertyName("code")]
        public string? Code { get; set; }

        [JsonPropertyName("message")]
        public string? Message { get; set; }
    }


    public class PhoneNumber
    {
        public PhoneNumber(string countryCode, string number)
        {
            CountryCode = countryCode;
            Number = number;
        }
        [Required]
        [JsonPropertyName("countryCode")]
        public string CountryCode { get; set; }
        [Required]
        [Json<PERSON>ropertyName("number")]
        public string Number { get; set; }
    }
}
