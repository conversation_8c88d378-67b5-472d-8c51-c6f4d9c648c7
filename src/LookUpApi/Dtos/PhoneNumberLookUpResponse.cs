using System.Text.Json.Serialization;

namespace LookUpApi.Dtos
{
    public class PhoneNumberLookUpResponse : LookUpResponse
    {
        [JsonPropertyName("data")]
        public AccountLookupData? Data { get; set; }
    }
    public class AccountLookupData
    {
        [JsonPropertyName("mobileNumber")]
        public string? MobileNumber { get; set; }

        [JsonPropertyName("email")]
        public string? Email { get; set; }

        [JsonPropertyName("tokenData")]
        public Dictionary<string, string> TokenData { get; set; } = new();
    }
}
